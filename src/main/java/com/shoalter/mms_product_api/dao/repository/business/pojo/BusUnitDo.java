package com.shoalter.mms_product_api.dao.repository.business.pojo;

import java.util.Date;
import java.math.BigDecimal;
import lombok.Data;
import javax.persistence.*;
@Entity
@Table(name = "BUS_UNIT")
@Data
public class BusUnitDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID")
    private Integer id;
    @Column(name = "CODE")
    private String code;
    @Column(name = "NAME")
    private String name;
    @Column(name = "CREATED_BY")
    private String createdBy;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "LAST_UPDATED_BY")
    private String lastUpdatedBy;
    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;
    @Column(name = "PO_FILE_NAME")
    private String poFileName;
    @Column(name = "INVOICE_FILE_NAME")
    private String invoiceFileName;
    @Column(name = "CONTACT_EMAIL")
    private String contactEmail;
    @Column(name = "DISP_SEQ")
    private Integer dispSeq;
    @Column(name = "LOG_FILE")
    private String logFile;
    @Column(name = "ACTIVE_IND")
    private String activeInd;
    @Column(name = "TYPE")
    private String type;
    @Column(name = "PLATFORM_ID")
    private Integer platformId;
}