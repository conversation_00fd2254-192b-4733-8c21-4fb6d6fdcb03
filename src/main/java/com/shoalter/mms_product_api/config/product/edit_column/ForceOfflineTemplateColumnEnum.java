package com.shoalter.mms_product_api.config.product.edit_column;

import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.service.product.template.TemplateInterface;
import lombok.Getter;

@Getter
public enum ForceOfflineTemplateColumnEnum implements TemplateInterface<ForceOfflineTemplateColumnEnum> {

	STORE_ID(0, "Store Id", null, null, null),
	SKU_ID(1, "Sku ID", null, null, null),
	STORE_STATUS(2, "SKU Status", ExcelValidationName.VALIDATION_PRODUCT_STATUS, null, null),
	CASE_NUMBER(3, "Case Number", null, null, null);

	private final Integer columnNumber;
	private final String columnName;
	private final String validationName;
	private final ForceOfflineTemplateColumnEnum parent;
	private final String parentSegment;

	ForceOfflineTemplateColumnEnum(Integer columnNumber, String columnName, String validationName, ForceOfflineTemplateColumnEnum parent, String parentSegment) {
		this.columnNumber = columnNumber;
		this.columnName = columnName;
		this.validationName = validationName;
		this.parent = parent;
		this.parentSegment = parentSegment;
	}

	@Override
	public Integer getParentColumnNumber() {
		return parent.getColumnNumber();
	}
}
