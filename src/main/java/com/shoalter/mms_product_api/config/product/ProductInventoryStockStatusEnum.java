package com.shoalter.mms_product_api.config.product;

import lombok.Getter;

/**
 * 存貨狀態，用於IIDS等相關系統
 */
@Getter
public enum ProductInventoryStockStatusEnum {
    AVAILABLE("notSpecified"),//有存貨
    FOOS("forceOutOfStock"), //強制售罄
    FORCE_IN_STOCK("forceInStock");//存貨無限

    private final String parameter;

   ProductInventoryStockStatusEnum(String parameter){
       this.parameter=parameter;
   }

   public static ProductInventoryStockStatusEnum of(String parameter){
	   for(ProductInventoryStockStatusEnum stockStatusEnum:ProductInventoryStockStatusEnum.values()){
		   if(stockStatusEnum.parameter.equalsIgnoreCase(parameter) || stockStatusEnum.name().equalsIgnoreCase(parameter)){
			   return stockStatusEnum;
		   }
	   }
	   return null;
   }
}
