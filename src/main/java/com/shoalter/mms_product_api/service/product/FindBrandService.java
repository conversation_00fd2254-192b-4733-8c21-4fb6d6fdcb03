package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.response.BrandDto;
import com.shoalter.mms_product_api.service.product.pojo.response.FindBrandDto;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class FindBrandService {

	public final BrandRepository brandRepository;

	public ResponseDto<List<BrandDto>> start(String buCode, String keyword) {
		List<BrandDto> brandDoList = brandRepository.searchByBuCodeAndKeyword(buCode, keyword);
		return ResponseDto.<List<BrandDto>>builder().data(brandDoList).status(1).build();
	}

	public ResponseDto<FindBrandDto> start(Integer id) {

		return ResponseDto.success(brandRepository.findBrandById(id));

	}
}

