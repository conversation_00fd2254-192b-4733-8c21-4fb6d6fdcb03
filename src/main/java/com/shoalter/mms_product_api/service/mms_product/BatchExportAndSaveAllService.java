package com.shoalter.mms_product_api.service.mms_product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.BatchImportSourceEnum;
import com.shoalter.mms_product_api.config.product.CategoryConfig;
import com.shoalter.mms_product_api.config.product.ExportStatusEnum;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.PackageBoxTypeCodeAndDescEnum;
import com.shoalter.mms_product_api.config.product.ProductDeliverMethod;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.product.SystemUserEnum;
import com.shoalter.mms_product_api.config.product.ThirdPartySourceEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.PickupDaysType;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.BuExportHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.BuExportHistoryDo;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantStoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractMerchantDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.BadRequestException;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.mapper.SingleEditProductDtoMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.bu_export_history.pojo.projection.TmallProductDataProjection;
import com.shoalter.mms_product_api.service.hybris.helper.HybrisHelper;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisCreateUpdateOptionValueMainRequestData;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisCreateUpdateOptionValueMainResponseData;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisCreateUpdateOptionValueRequestData;
import com.shoalter.mms_product_api.service.mms_product.pojo.BatchExportAndSaveAllMainRequestData;
import com.shoalter.mms_product_api.service.mms_product.pojo.MmsBatchSaveProductDto;
import com.shoalter.mms_product_api.service.product.helper.MerchantHelper;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ExternalPlatform;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductFieldDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RequiredArgsConstructor
@Service
@Slf4j
public class BatchExportAndSaveAllService {

	private final BuExportHistoryRepository buExportHistoryRepository;
	private final MerchantStoreRepository merchantStoreRepository;
	private final BrandRepository brandRepository;
	private final StoreRepository storeRepository;
	private final SysParmRepository sysParmRepository;
	private final MessageSource messageSource;
	private final MerchantHelper merchantHelper;
	private final BatchSaveAllService batchSaveAllService;
	private final SingleEditProductDtoMapper singleEditProductDtoMapper;
	private final HybrisHelper hybrisHelper;
	private final Gson gson;

	@Value("${hybris.api.batch.size}")
	private int hybrisApiBatchSize;
	private static final int DEFAULT_VARIANT_LIMIT = 100;


	public ResponseDto<Void> start(UserDto userDto, Integer historyId, BatchExportAndSaveAllMainRequestData batchExportAndSaveAllMainRequestData, String clientIp) {

		Optional<BuExportHistoryDo> buExportHistoryDo = buExportHistoryRepository.findById(historyId);
		if (buExportHistoryDo.isEmpty()) {
			throw new BadRequestException(messageSource.getMessage("message146", null, null));
		}

		if (batchExportAndSaveAllMainRequestData == null || batchExportAndSaveAllMainRequestData.getStoreId() == null) {
			throw new NoDataException();
		}

		Integer storeId = batchExportAndSaveAllMainRequestData.getStoreId();
		Integer merchantId = merchantStoreRepository.findMerchantIdByStoreId(storeId);
		if (!checkHasPermission(userDto, merchantId)) throw new SystemI18nException("message28", userDto.getRoleCode());

		List<TmallProductDataProjection> tmallDataProjections = buExportHistoryRepository.findTmallProductDataByIdAndStatus(
			historyId,
			ExportStatusEnum.SUCCESS.name()
		);

		Integer variantLimit = getAutoCreateVariantLimitFromSysParm();
		tmallDataProjections = filterVariantLimitData(tmallDataProjections, variantLimit);

		if (CollectionUtil.isEmpty(tmallDataProjections)) throw new SystemI18nException("message223");

		SpringBeanProvider.getBean(BatchExportAndSaveAllService.class).generateTmallDataAndCreate(userDto, historyId, clientIp, storeId, merchantId, tmallDataProjections);

		return ResponseDto.success(null);
	}

	/**
	 * Filters the given list of Tmall product data projections based on the variant limit.
	 * Only a specified maximum number of variants will be retained for each `numIid`.
	 * Groups exceeding the variant limit will be logged, and the group will be ignored.
	 */
	protected List<TmallProductDataProjection> filterVariantLimitData(List<TmallProductDataProjection> tmallDataProjections, Integer variantLimit) {
		log.info("{} variantLimit: {}", ConstantType.SYSTEM_PARAM_SEGMENT_LIMIT_TMALL_AUTO_CREATE, variantLimit);
		return tmallDataProjections.stream()
			.filter(Objects::nonNull)
			.filter(data -> data.getNumIid() != null)
			.collect(Collectors.groupingBy(TmallProductDataProjection::getNumIid))
			.values()
			.stream()
			.flatMap(group -> {
				if (group.size() > variantLimit) {
					log.warn("TMALL sku numIid: {} has {} variants, exceed limit {}, ignore this group", group.get(0).getNumIid(), group.size(), variantLimit);
					// ignore this group
					return Stream.of();
				}
				return group.stream();
			})
			.collect(Collectors.toList());
	}

	protected Integer getAutoCreateVariantLimitFromSysParm() {
		return sysParmRepository.findBySegmentAndCode(ConstantType.SYSTEM_PARAM_SEGMENT_LIMIT_TMALL_AUTO_CREATE, ConstantType.SYSTEM_PARAM_CODE_LIMIT_TMALL_AUTO_CREATE)
			.stream()
			.map(SysParmDo::getParmValue)
			.filter(StringUtil::isNotEmpty)
			.map(Integer::parseInt)
			.findFirst()
			.orElseGet(() -> {
				log.warn("SysParm not found, segment: {}. Using default value: {}", ConstantType.SYSTEM_PARAM_SEGMENT_LIMIT_TMALL_AUTO_CREATE, DEFAULT_VARIANT_LIMIT);
				return DEFAULT_VARIANT_LIMIT;
			});
	}

	@Async("createRecordExecutor")
	public void generateTmallDataAndCreate(UserDto userDto, Integer historyId, String clientIp, Integer storeId, Integer merchantId, List<TmallProductDataProjection> tmallDataProjections) {
		List<Map<String, Pair<List<HybrisCreateUpdateOptionValueRequestData>, List<SysParmDo>>>> needSaveAndCreateList = new ArrayList<>();
		List<HktvProductFieldDto> hktvProductFieldDtoList = generateHktvProductFieldDtoList(
			tmallDataProjections,
			needSaveAndCreateList
		);

		ResponseDto<Void> saveOptionValueResult = saveOptionValue(userDto, historyId, storeId, needSaveAndCreateList);
		if (saveOptionValueResult.getStatus() == StatusCodeEnum.FAIL.getCode()) {
			log.error("saveOptionValueResult is fail, errorMessage: {}", saveOptionValueResult.getErrorMessageList());
			return;
		}


		MmsBatchSaveProductDto mmsBatchSaveProductDto = generateMmsBatchSaveProductDto(
			merchantId,
			storeId,
			hktvProductFieldDtoList,
			tmallDataProjections.get(0).getExportType()
		);
		log.info("start batch save all tmall product, size: {}", mmsBatchSaveProductDto.getProducts().size());

		ResponseDto<Void> result = batchSaveAllService.start(userDto, mmsBatchSaveProductDto, clientIp);
		log.info("batch save all tmall product result status: {}, errorMessage: {}", result.getStatus(), result.getErrorMessageList());
	}

	public ResponseDto<Void> saveOptionValue(UserDto userDto, Integer historyId, Integer storeId, List<Map<String, Pair<List<HybrisCreateUpdateOptionValueRequestData>, List<SysParmDo>>>> needSaveAndCreateList) {
		if (CollectionUtil.isEmpty(needSaveAndCreateList)) {
			return ResponseDto.success(null);
		}

		List<HybrisCreateUpdateOptionValueRequestData> needSendHybrisList = needSaveAndCreateList.stream()
			.flatMap(map -> map.values().stream())
			.flatMap(pair -> pair.getLeft().stream())
			.distinct()
			.collect(Collectors.toList());

		List<SysParmDo> needSaveSysParmList = needSaveAndCreateList.stream()
			.flatMap(map -> map.values().stream())
			.flatMap(pair -> pair.getRight().stream())
			.distinct()
			.collect(Collectors.toList());

		List<List<HybrisCreateUpdateOptionValueRequestData>> sendHybrisData = ListUtils.partition(needSendHybrisList, hybrisApiBatchSize);
		List<List<SysParmDo>> saveSysParmData = ListUtils.partition(needSaveSysParmList, hybrisApiBatchSize);
		log.info("start save option value, needSendHybris count: {}, needSaveSysParmList count: {}, batch save count: {}", needSendHybrisList.size(), needSaveSysParmList.size(), sendHybrisData.size());

		AtomicBoolean hasError = new AtomicBoolean(false);
		List<String> errorMessage = new ArrayList<>();
		for (int batchIndex = 0; batchIndex < sendHybrisData.size(); batchIndex++) {
			List<HybrisCreateUpdateOptionValueRequestData> requestHybrisValue = sendHybrisData.get(batchIndex);
			List<SysParmDo> saveSysParmValue = saveSysParmData.get(batchIndex);

			if (CollectionUtil.isEmpty(requestHybrisValue)) {
				log.warn("requestHybrisValue is empty, skip batchIndex: {}", batchIndex);
				continue;
			}

			log.info("batch save option value batchIndex: {}, option value size: {} ", batchIndex, requestHybrisValue.size());

			ResponseDto<HybrisCreateUpdateOptionValueMainResponseData> result =
				hybrisHelper.requestCreateUpdateOptionValue(
					userDto,
					HybrisCreateUpdateOptionValueMainRequestData.generate(requestHybrisValue),
					String.format("%s_%s_batch_%d", historyId, storeId, batchIndex)
				);

			if (result.getStatus() == StatusCodeEnum.FAIL.getCode()) {
				hasError.set(true);
				errorMessage.addAll(result.getErrorMessageList());
				log.error("batch save option value to hybris error, batchIndex: {} message：{}", batchIndex, result.getErrorMessageList());
				continue;
			}

			try {
				sysParmRepository.saveAll(saveSysParmValue);
				log.info("batch save option value to sysParm success, batchIndex: {} ", batchIndex);
			} catch (Exception e) {
				hasError.set(true);
				errorMessage.add(e.getMessage());
				log.error("batch save option value to sysParm fail, batchIndex: {}, errorMessage: {}", batchIndex, e.getMessage());
			}
		}
		return hasError.get() ? ResponseDto.fail(errorMessage) : ResponseDto.success(null);
	}

	private List<HktvProductFieldDto> generateHktvProductFieldDtoList(
		List<TmallProductDataProjection> tmallDataProjections,
		List<Map<String, Pair<List<HybrisCreateUpdateOptionValueRequestData>, List<SysParmDo>>>> needSaveAndCreateList
	) {
		AtomicInteger sequence = new AtomicInteger(1);
		Date now = new Date();
		Map<String, List<SysParmDo>> productFieldMap = sysParmRepository.findBySegmentAndParentCodeList(
				SysParmSegment.PRODUCT_FIELD_VALUE,
				SysParmCodeEnum.PRODUCT_FIELD_MAINLAND_CODE_SET
			)
			.stream()
			.filter(sysParamDo -> Objects.nonNull(sysParamDo.getParentCode()))
			.collect(Collectors.groupingBy(SysParmDo::getParentCode));

		return tmallDataProjections
			.stream()
			.map(tmallProductData -> {
				HktvProductFieldDto hktvProductFieldDto = gson.fromJson(tmallProductData.getSkuData(), HktvProductFieldDto.class);
				Map<String, Pair<List<HybrisCreateUpdateOptionValueRequestData>, List<SysParmDo>>> needSaveAndCreateMap = new HashMap<>();
				String productSkuId = String.format("%s-%s", singleEditProductDtoMapper.toTmallProductId(hktvProductFieldDto.getProductId()), singleEditProductDtoMapper.toTmallSkuId(hktvProductFieldDto.getSkuId()));

				Map<String, String> optionValueMap1 = checkAndGenerateNeedSaveAndCreateMap(
					hktvProductFieldDto,
					productSkuId,
					sequence,
					now,
					productFieldMap,
					SysParmCodeEnum.MAINLAND_FIELD_1,
					needSaveAndCreateMap
				);
				hktvProductFieldDto.setField1(optionValueMap1.get("field"));
				hktvProductFieldDto.setValue1(optionValueMap1.get("value"));

				Map<String, String> optionValueMap2 = checkAndGenerateNeedSaveAndCreateMap(
					hktvProductFieldDto,
					productSkuId,
					sequence,
					now,
					productFieldMap,
					SysParmCodeEnum.MAINLAND_FIELD_2,
					needSaveAndCreateMap
				);
				hktvProductFieldDto.setField2(optionValueMap2.get("field"));
				hktvProductFieldDto.setValue2(optionValueMap2.get("value"));


				Map<String, String> optionValueMap3 = checkAndGenerateNeedSaveAndCreateMap(
					hktvProductFieldDto,
					productSkuId,
					sequence,
					now,
					productFieldMap,
					SysParmCodeEnum.MAINLAND_FIELD_3,
					needSaveAndCreateMap
				);
				hktvProductFieldDto.setField3(optionValueMap3.get("field"));
				hktvProductFieldDto.setValue3(optionValueMap3.get("value"));

				needSaveAndCreateList.add(needSaveAndCreateMap);

				return hktvProductFieldDto;
			})
			.collect(Collectors.toList());
	}


	private Map<String, String> checkAndGenerateNeedSaveAndCreateMap(
		HktvProductFieldDto hktvProductFieldDto,
		String productSkuId,
		AtomicInteger sequence,
		Date now,
		Map<String, List<SysParmDo>> productFieldMap,
		SysParmCodeEnum fieldCodeEnum,
		Map<String, Pair<List<HybrisCreateUpdateOptionValueRequestData>, List<SysParmDo>>> needSaveAndCreateMap
	) {
		String value = "";
		if (fieldCodeEnum == SysParmCodeEnum.MAINLAND_FIELD_1) {
			if (hktvProductFieldDto.getValue1() != null) {
				value = hktvProductFieldDto.getValue1();
			}
		} else if (fieldCodeEnum == SysParmCodeEnum.MAINLAND_FIELD_2) {
			if (hktvProductFieldDto.getValue2() != null) {
				value = hktvProductFieldDto.getValue2();
			}
		} else if (fieldCodeEnum == SysParmCodeEnum.MAINLAND_FIELD_3) {
			if (hktvProductFieldDto.getValue3() != null) {
				value = hktvProductFieldDto.getValue3();
			}
		}

		String parentFieldCode = fieldCodeEnum.getCode();
		List<SysParmDo> sysParmDoList = productFieldMap.computeIfAbsent(parentFieldCode, k -> new ArrayList<>());
		if (StringUtil.isNotEmpty(value)) {
			SysParmDo newSysParmDo;
			SysParmDo originalSysParmDo = checkOptionValueIsExist(sysParmDoList, value);
			if (originalSysParmDo != null) {
				parentFieldCode = originalSysParmDo.getParentCode();
				newSysParmDo = originalSysParmDo;
			} else {
				String code = String.format("ML_%s", UUID.randomUUID().toString());
				newSysParmDo = generateSysParmDo(code, value, sequence.get(), parentFieldCode, now);
				HybrisCreateUpdateOptionValueRequestData hybrisRequestData = HybrisCreateUpdateOptionValueRequestData.generate(code, value, value, sequence.get(), parentFieldCode);
				needSaveAndCreateMap.putIfAbsent(productSkuId, Pair.of(new ArrayList<>(), new ArrayList<>()));
				needSaveAndCreateMap.get(productSkuId).getLeft().add(hybrisRequestData);
				needSaveAndCreateMap.get(productSkuId).getRight().add(newSysParmDo);
				// if create new option value, add to present sysParmDoList
				sysParmDoList.add(newSysParmDo);

				sequence.getAndIncrement();
			}
			value = newSysParmDo.getCode();
		}

		Map<String, String> resultMap = new HashMap<>();
		resultMap.put("field", StringUtils.isBlank(value) ? "" : parentFieldCode);
		resultMap.put("value", value);

		return resultMap;
	}

	private SysParmDo generateSysParmDo(String code, String value, Integer sequence, String field, Date now) {
		SysParmDo sysParmDo = new SysParmDo();
		sysParmDo.setSegment(SysParmSegment.PRODUCT_FIELD_VALUE);
		sysParmDo.setCode(code);
		sysParmDo.setParmValue(code);
		sysParmDo.setShortDesc(value);
		sysParmDo.setShortDescTc(value);
		sysParmDo.setShortDescSc(value);
		sysParmDo.setLongDesc(value);
		sysParmDo.setLongDescTc(value);
		sysParmDo.setDispSeq(sequence);
		sysParmDo.setParentSegment(SysParmSegment.PRODUCT_FIELD);
		sysParmDo.setParentCode(field);
		sysParmDo.setCreatedBy(SystemUserEnum.SYSTEM.getSystemCode());
		sysParmDo.setCreatedDate(now);
		return sysParmDo;
	}

	private MmsBatchSaveProductDto generateMmsBatchSaveProductDto(
		Integer merchantId,
		Integer storeId,
		List<HktvProductFieldDto> hktvProductFieldDtoList,
		String exportType
	) {
		Integer brandId = brandRepository.findIdByBrandCode(ConstantType.BRAND_CODE_SHIPPED_FROM_MAINLAND)
			.stream().findFirst().orElseThrow(NoDataException::new);
		Map<String, List<SysParmDo>> segmentSysParmDoMap = sysParmRepository.findBySegments(List.of(SysParmSegment.COUNTRY_OF_ORIGIN))
			.stream()
			.collect(Collectors.groupingBy(SysParmDo::getSegment));
		String storefrontStoreCode = storeRepository.findStorefrontStoreCodesByIds(List.of(storeId))
			.stream()
			.filter(Objects::nonNull)
			.findFirst()
			.orElse(null);
		Integer contractNo = storeRepository.findStoreContractMerchantByStorefrontStoreCode(
				ConstantType.BUSINESS_TYPE_ECOM, ConstantType.HKTV, storefrontStoreCode)
			.stream()
			.findFirst()
			.map(StoreContractMerchantDo::getContractId)
			.orElse(null);

		Map<String, AtomicBoolean> productIdFlagMap = new HashMap<>();
		MmsBatchSaveProductDto mmsBatchSaveProductDto = new MmsBatchSaveProductDto();
		mmsBatchSaveProductDto.setMerchantId(merchantId);
		mmsBatchSaveProductDto.setFileName(exportType + StringUtil.PERIOD + StringUtil.FILE_EXTENSION_EXCEL);
		mmsBatchSaveProductDto.setSource(BatchImportSourceEnum.TMALL);

		List<SingleEditProductDto> products = hktvProductFieldDtoList.stream()
			.map(hktvProductFieldDto -> {
				hktvProductFieldDto.setStorefrontStoreCode(storefrontStoreCode);
				SingleEditProductDto singleEditProductDto = singleEditProductDtoMapper.toSingleEditProductDtoFromTmallData(hktvProductFieldDto);
				ProductMasterDto product = singleEditProductDto.getProduct();
				BuProductDto buProductDto = product.getAdditional();
				HktvProductDto hktvProductDto = buProductDto.getHktv();

				product.setMerchantId(merchantId);
				product.setBrandId(brandId);
				String manufacturedCountry = hktvProductFieldDto.getManufacturedCountry() == null
					? "N/A"
					: segmentSysParmDoMap.get(SysParmSegment.COUNTRY_OF_ORIGIN)
					.stream()
					.filter(sysParmDo ->
						(sysParmDo.getShortDescTc() != null && sysParmDo.getShortDescTc().equals(hktvProductFieldDto.getManufacturedCountry()))
							|| (sysParmDo.getShortDescSc() != null && sysParmDo.getShortDescSc().equals(hktvProductFieldDto.getManufacturedCountry())))
					.map(SysParmDo::getCode)
					.findFirst()
					.orElse("N/A");
				product.setManufacturedCountry(manufacturedCountry);
				hktvProductDto.setContractNo(contractNo);

				product.setPackingBoxType(PackageBoxTypeCodeAndDescEnum.ICE_FREE_CHILL_FOOD.getPackageBoxTypeCode());
				hktvProductDto.setProductReadyMethod(ProductReadyMethodType.MERCHANT_DELIVERY);
				hktvProductDto.setDeliveryMethod(ProductDeliverMethod.MERCHANT_DELIVERY);
				if (hktvProductFieldDto.getConvertedOtherProductPhoto() != null) {
					hktvProductDto.setVariantProductPhoto(hktvProductFieldDto.getConvertedOtherProductPhoto()
						.stream().limit(15).collect(Collectors.toList()));
				}
				if (hktvProductFieldDto.getConvertedOtherPhoto() != null) {
					hktvProductDto.setOtherPhoto(hktvProductFieldDto.getConvertedOtherPhoto()
						.stream().limit(30).collect(Collectors.toList()));
				}
				hktvProductDto.setProductTypeCode(List.of(CategoryConfig.CREATE_SKU_FROM_TMALL_CATEGORY));
				hktvProductDto.setPrimaryCategoryCode(CategoryConfig.CREATE_SKU_FROM_TMALL_CATEGORY);
				hktvProductDto.setOnlineStatus(OnlineStatusEnum.OFFLINE);

				String productId = product.getProductId();
				String skuId = product.getSkuId();
				productIdFlagMap.putIfAbsent(productId, new AtomicBoolean(true));
				AtomicBoolean isFirst = productIdFlagMap.get(productId);

				if (isFirst.getAndSet(false)) {
					hktvProductDto.setIsPrimarySku(ConstantType.CONSTANT_YES);
					log.info("productId: {}, skuId: {}, isPrimarySku: {}", productId, skuId, hktvProductDto.getIsPrimarySku());
				} else {
					hktvProductDto.setIsPrimarySku(ConstantType.CONSTANT_NO);
				}
				hktvProductDto.setCurrency(CurrencyEnum.RMB.name());
				hktvProductDto.setVisibility(ConstantType.CONSTANT_YES);
				hktvProductDto.setReturnDays(ConstantType.ZERO);
				hktvProductDto.setProductReadyDays("7");
				hktvProductDto.setPickupDays(PickupDaysType.MON_SAT);
				hktvProductDto.setPickupTimeslot("PM");
				ExternalPlatform externalPlatform = new ExternalPlatform();
				externalPlatform.setSource(List.of(ThirdPartySourceEnum.TMALL.getValue()));
				externalPlatform.setProductId(hktvProductFieldDto.getProductId());
				externalPlatform.setSkuId(hktvProductFieldDto.getSkuId());
				hktvProductDto.setExternalPlatform(externalPlatform);
				product.setAdditional(buProductDto);
				product.getAdditional().setHktv(hktvProductDto);

				return singleEditProductDto;
			}).collect(Collectors.toList());
		mmsBatchSaveProductDto.setProducts(products);
		return mmsBatchSaveProductDto;
	}

	private boolean checkHasPermission(UserDto userDto, Integer merchantId) {

		if (!RoleCode.THIRD_PARTY_ROLES.contains(userDto.getRoleCode())) {
			log.info("user does not have permission, roleCode: {}", userDto.getRoleCode());
			return false;
		}

		List<Integer> userMerchantList = merchantHelper.findMerchantIdByRole(userDto);
		if (CollectionUtil.isEmpty(userMerchantList)) {
			log.info("user does not have permission, userMerchantList is empty.");
			return false;
		}

		if (!userMerchantList.contains(merchantId)) {
			log.info("user does not have permission, userMerchantList is not include merchantId: {}.", merchantId);
			return false;
		}

		return true;
	}

	private SysParmDo checkOptionValueIsExist(List<SysParmDo> sysParmDoList, String value) {
		if (CollectionUtil.isEmpty(sysParmDoList)) {
			return null;
		}
		return sysParmDoList.stream()
			.filter(param -> param.getShortDescTc().equals(value) || (param.getShortDescSc() != null && param.getShortDescSc().equals(value)))
			.findFirst()
			.orElse(null);
	}
}
