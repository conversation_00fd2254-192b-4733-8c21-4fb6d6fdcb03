package com.shoalter.mms_product_api.dao.repository.approval_deal;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealDo;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealIdAndSkuCodeViewDo;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealIdAndVariantViewDo;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealStatusEnum;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealTypeEnum;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealListDto;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealListMainRequestData;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface ApprovalDealRepository extends JpaRepository<ApprovalDealDo, Long> {

	String permissionRmSql =
		"AND (:#{#userDto.roleCode} IN ('RM ADMIN', 'OPERATION_ADMIN', 'ADMIN', 'SUPER_SYSTEM_ADMIN', 'OPS_DEPT_HEAD') "
			+ "    OR ( "
			+ "           EXISTS (SELECT 1 "
			+ "                   FROM RM_TEAM rt "
			+ "                            JOIN CONTRACT c ON rt.USER_ID = c.RM_ID AND c.MASTER_CONTRACT_ID IS NULL "
			+ "                            JOIN STORE s ON s.ID = c.STORE_ID "
			+ "                   WHERE ( "
			+ "                       (:#{#userDto.roleCode} = 'RM' AND rt.USER_ID = :#{#userDto.userId}) "
			+ "                           OR (:#{#userDto.roleCode} = 'RML' AND rt.TEAM_LEADER_ID = :#{#userDto.userId}) "
			+ "                       ) "
			+ "                     AND ad.MERCHANT_ID = c.MERCHANT_ID "
			+ "                     AND s.STOREFRONT_STORE_CODE = ad.STOREFRONT_STORE_CODE) "
			+ "           ) "
			+ "    )";

	@Query(value = "SELECT ad.ID " +
		"FROM APPROVAL_DEAL ad " +
		"WHERE ad.BU = :bu " +
		"AND ad.STOREFRONT_STORE_CODE = :storefrontStoreCode " +
		"AND ad.SKU_CODE = :skuCode " +
		"AND ad.APPROVAl_TYPE = :approvalType " +
		"AND ad.APPROVAl_STATUS IN (:approvalStatus) " +
		"ORDER BY ad.ID DESC LIMIT 1", nativeQuery = true)
	Optional<Long> findIdByBuAndStorefrontStoreCodeAndSkuCodeAndApprovalTypeAndStatus(String bu, String storefrontStoreCode, String skuCode, String approvalType, Set<String> approvalStatus);

	@Query(value = "SELECT ad.ID as id, ad.SKU_CODE as skuCode " +
		"FROM APPROVAL_DEAL ad " +
		"WHERE ad.BU = :bu " +
		"AND ad.STOREFRONT_STORE_CODE = :storefrontStoreCode " +
		"AND ad.SKU_CODE IN (:skuCodes) " +
		"AND ad.APPROVAl_TYPE = :approvalType " +
		"AND ad.APPROVAl_STATUS IN (:approvalStatus)", nativeQuery = true)
	List<ApprovalDealIdAndSkuCodeViewDo> findAllByBuAndStorefrontStoreCodeAndSkuCodeAndApprovalTypeAndStatus(String bu, String storefrontStoreCode, List<String> skuCodes, String approvalType, Set<String> approvalStatus);

	@Query(value = "SELECT ad.ID " +
		"FROM APPROVAL_DEAL ad " +
		"WHERE ad.BU = :bu " +
		"AND ad.STOREFRONT_STORE_CODE = :storefrontStoreCode " +
		"AND ad.PRODUCT_CODE = :productCode " +
		"AND ad.APPROVAl_TYPE = :approvalType " +
		"AND ad.APPROVAl_STATUS IN (:approvalStatus)", nativeQuery = true)
	Set<Long> findAllByBuAndStorefrontStoreCodeAndProductCodeAndApprovalTypeAndStatus(String bu, String storefrontStoreCode, String productCode, String approvalType, Set<String> approvalStatus);

	@Query(value = "SELECT COUNT(ad.ID) as count " +
		"FROM APPROVAL_DEAL ad " +
		"WHERE ad.BU = :bu " +
		"AND ad.STOREFRONT_STORE_CODE = :storefrontStoreCode " +
		"AND ad.PRODUCT_CODE = :productCode " +
		"AND ad.APPROVAl_TYPE = :approvalType " +
		"AND ad.APPROVAl_STATUS IN (:approvalStatus)" +
		"AND ad.IS_PRIMARY_SKU = :isPrimarySku", nativeQuery = true)
	int countIsPrimarySkuByBuAndStorefrontStoreCodeAndProductCodeAndApprovalTypeAndStatus(String bu, String storefrontStoreCode, String productCode, String approvalType, Set<String> approvalStatus, boolean isPrimarySku);

	@Query(value = "SELECT ad.STOREFRONT_STORE_CODE as storefrontStoreCode, " +
		"ad.PRODUCT_CODE as productCode, " +
		"COUNT(CASE WHEN ad.IS_PRIMARY_SKU = true THEN ad.ID END) as primarySkuCount, " +
		"GROUP_CONCAT(CASE WHEN ad.IS_PRIMARY_SKU = true THEN ad.ID END) as primarySkuIds, " +
		"COUNT(CASE WHEN ad.IS_PRIMARY_SKU = false THEN ad.ID END) as variantSkuCount, " +
		"GROUP_CONCAT(CASE WHEN ad.IS_PRIMARY_SKU = false THEN ad.ID END) as variantSkuIds " +
		"FROM APPROVAL_DEAL ad " +
		"WHERE ad.STOREFRONT_STORE_CODE IN (:storefrontStoreCodes) " +
		"AND ad.PRODUCT_CODE IN (:productCodes) " +
		"AND ad.BU = :bu " +
		"AND ad.APPROVAL_TYPE = :approvalType " +
		"AND ad.APPROVAL_STATUS IN (:approvalStatus) " +
		"GROUP BY ad.STOREFRONT_STORE_CODE, ad.PRODUCT_CODE", nativeQuery = true)
	List<ApprovalDealIdAndVariantViewDo> findAllPrimarySkuAndVariantSkuCountsAndIds(String bu, Set<String> storefrontStoreCodes, Set<String> productCodes, String approvalType, Set<String> approvalStatus);

	Optional<ApprovalDealDo> findByIdAndApprovalTypeAndApprovalStatus(Long id, ApprovalDealTypeEnum approvalType, ApprovalDealStatusEnum approvalStatus);

	List<ApprovalDealDo> findByBuAndStorefrontStoreCodeInAndApprovalStatusIn(BuCodeEnum buCode, List<String> storefrontStoreCodes, Set<ApprovalDealStatusEnum> approvalStatuses);

	@Query(value = "SELECT ad.ID id, ad.STOREFRONT_STORE_CODE storeCode, ad.SKU_CODE skuId, ad.SKU_NAME skuNameEn, ad.SKU_NAME_TCHI skuNameCh, " +
		"ad.MERCHANT_NAME merchantName, ad.APPROVAl_TYPE approvalType, ad.APPROVAl_STATUS approvalStatus, ad.LAST_UPDATED_DATE lastUpdatedDate " +
		"FROM APPROVAL_DEAL ad " +
		"JOIN BUS_UNIT bu ON bu.CODE = ad.BU " +
		"WHERE (:#{#request.skuId} IS NULL OR ad.SKU_CODE = :#{#request.skuId}) " +
		"AND (:#{#request.skuName} IS NULL OR (ad.SKU_NAME LIKE %:#{#request.skuName}% OR ad.SKU_NAME_TCHI LIKE %:#{#request.skuName}%)) " +
		"AND (:#{#request.merchantName} IS NULL OR ad.MERCHANT_NAME = :#{#request.merchantName}) " +
		"AND (:#{#request.buCode} IS NULL OR bu.CODE = :#{#request.buCode}) " +
		"AND (COALESCE(:#{#request.storefrontStoreCodes}, NULL) IS NULL OR ad.STOREFRONT_STORE_CODE IN (:#{#request.storefrontStoreCodes})) " +
		"AND (COALESCE(:#{#request.approvalType}, NULL) IS NULL OR ad.APPROVAl_TYPE IN (:#{#request.approvalType})) " +
		"AND (COALESCE(:#{#request.approvalStatus}, NULL) IS NULL OR ad.APPROVAl_STATUS IN (:#{#request.approvalStatus})) " +
		permissionRmSql + "ORDER BY " +
		"CASE WHEN :#{#userDto.roleCode} = 'RM' AND ad.APPROVAl_STATUS = 'MERCHANT_SUBMITTED' THEN 1 " +
		"	  WHEN :#{#userDto.roleCode} = 'RML' AND ad.APPROVAl_STATUS = 'RM_REVIEWED' THEN 1 ELSE 2 END",
		countQuery = "SELECT ad.ID id, ad.STOREFRONT_STORE_CODE storeCode, ad.SKU_CODE skuId, ad.SKU_NAME skuNameEn, ad.SKU_NAME_TCHI skuNameCh, " +
			"ad.MERCHANT_NAME merchantName, ad.APPROVAl_TYPE approvalType, ad.APPROVAl_STATUS approvalStatus, ad.LAST_UPDATED_DATE lastUpdatedDate " +
			"FROM APPROVAL_DEAL ad " +
			"JOIN BUS_UNIT bu ON bu.CODE = ad.BU " +
			"WHERE (:#{#request.skuId} IS NULL OR ad.SKU_CODE = :#{#request.skuId}) " +
			"AND (:#{#request.skuName} IS NULL OR (ad.SKU_NAME LIKE %:#{#request.skuName}% OR ad.SKU_NAME_TCHI LIKE %:#{#request.skuName}%)) " +
			"AND (:#{#request.merchantName} IS NULL OR ad.MERCHANT_NAME = :#{#request.merchantName}) " +
			"AND (:#{#request.buCode} IS NULL OR bu.CODE = :#{#request.buCode}) " +
			"AND (COALESCE(:#{#request.storefrontStoreCodes}, NULL) IS NULL OR ad.STOREFRONT_STORE_CODE IN (:#{#request.storefrontStoreCodes})) " +
			"AND (COALESCE(:#{#request.approvalType}, NULL) IS NULL OR ad.APPROVAl_TYPE IN (:#{#request.approvalType})) " +
			"AND (COALESCE(:#{#request.approvalStatus}, NULL) IS NULL OR ad.APPROVAl_STATUS IN (:#{#request.approvalStatus})) " +
			permissionRmSql + "ORDER BY " +
			"CASE WHEN :#{#userDto.roleCode} = 'RM' AND ad.APPROVAl_STATUS = 'MERCHANT_SUBMITTED' THEN 1 " +
			"	  WHEN :#{#userDto.roleCode} = 'RML' AND ad.APPROVAl_STATUS = 'RM_REVIEWED' THEN 1 ELSE 2 END",
		nativeQuery = true)
	Page<ApprovalDealListDto> findApprovalDealList(UserDto userDto, ApprovalDealListMainRequestData request, Pageable pageable);

	@Query(value = "SELECT ad.APPROVAl_TYPE FROM APPROVAL_DEAL ad WHERE ad.ID = :id " + permissionRmSql, nativeQuery = true)
	Optional<String> findApprovalTypeById(UserDto userDto, Long id);
}
