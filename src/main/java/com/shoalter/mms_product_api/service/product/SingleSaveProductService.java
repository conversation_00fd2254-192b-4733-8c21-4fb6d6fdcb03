package com.shoalter.mms_product_api.service.product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.asynctask.CreateSaveProductRecordTask;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.MonitorPlatformGroupEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractTypeDo;
import com.shoalter.mms_product_api.exception.BadRequestException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.CheckBuHelper;
import com.shoalter.mms_product_api.service.product.helper.CheckProductHelper;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import com.shoalter.mms_product_api.service.product.helper.GenerateIIDSDataHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductPreProcessingHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductPriceMonitorProductHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.helper.SyncBaseProductInfoHelper;
import com.shoalter.mms_product_api.service.product.pojo.CheckProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductRecordResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.VariantMatrixProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class SingleSaveProductService {
	private final SaveProductRecordRepository saveProductRecordRepository;

	private final PermissionHelper permissionHelper;
	private final SaveProductHelper saveProductHelper;
	private final CheckProductHelper checkProductHelper;
	private final SyncBaseProductInfoHelper syncBaseProductInfoHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ProductPreProcessingHelper productPreProcessingHelper;
	private final GenerateIIDSDataHelper generateIIDSDataHelper;
	private final CheckBuHelper checkBuHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final ProductMasterHelper productMasterHelper;
	private final ProductPriceMonitorProductHelper productPriceMonitorProductHelper;
	private final CreateSaveProductRecordTask createSaveProductRecordTask;
	private final Gson gson;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final StoreRepository storeRepository;
	private final MessageSource messageSource;
	private final ExchangeRateHelper exchangeRateHelper;


	public ResponseDto<ProductRecordResponseDto> start(UserDto userDto, SingleEditProductDto productRequestDto, String clientIp) {
		ResponseDto<ProductRecordResponseDto> responseDto = SpringBeanProvider.getBean(SingleSaveProductService.class).processing(userDto, productRequestDto, clientIp);
		if (StatusCodeEnum.SUCCESS.getCode() == responseDto.getStatus()) {
			SpringBeanProvider.getBean(SingleSaveProductService.class).callProductMasterAndUpdateRecordRow(userDto, responseDto.getData().getRecordId());
		}

		return responseDto;
	}

	@Transactional
	public ResponseDto<ProductRecordResponseDto> processing(UserDto userDto, SingleEditProductDto productRequestDto, String clientIp) {
		Integer merchantId = (productRequestDto.getProduct().getMerchantId() == null) ? userDto.getMerchantId() : productRequestDto.getProduct().getMerchantId();
		permissionHelper.checkPermission(userDto, merchantId);

		if (productRequestDto.getProduct().getAdditional().getHktv() != null) {
			String storeCode = productRequestDto.getProduct().getAdditional().getHktv().getStores();
			// check contract type
			List<String> everutsContractTypeStores = storeRepository.findContractTypeByBuCodesAndStoreCodesOrStorefrontStoreCodes(BuCodeEnum.HKTV.name(), List.of(storeCode)).stream()
				.filter(storeContractTypeDo -> ContractType.EVERUTS.equals(storeContractTypeDo.getContractType()))
				.map(StoreContractTypeDo::getStorefrontCode).collect(Collectors.toList());
			if (CollectionUtil.isNotEmpty(everutsContractTypeStores)) {
				return ResponseDto.fail(List.of(messageSource.getMessage("message325", null, null)));
			}

			// check sku Id exists or not
			CheckProductResultDto checkSkuIsExistResult = checkProductSkuExistsInMerchant(userDto, productRequestDto);
			if (CollectionUtil.isNotEmpty(checkSkuIsExistResult.getErrorMessageList())) {
				return ResponseDto.<ProductRecordResponseDto>builder().status(StatusCodeEnum.FAIL.getCode()).errorMessageList(checkSkuIsExistResult.getErrorMessageList()).build();
			}
		}

		// save basic product history record info
		boolean hasMatrix = CollectionUtil.isNotEmpty(productRequestDto.getVariantSkuProductList());
		ProductMasterDto baseProduct = productRequestDto.getProduct();
		SaveProductRecordDo saveProductRecordDo =
			saveProductRecordHelper.createSaveProductRecord(userDto, merchantId, SaveProductType.SINGLE_CREATE_PRODUCT, String.format(SaveProductRecordHelper.CREATE_PRODUCT_FILE_NAME, baseProduct.getSkuId(), System.currentTimeMillis()), SaveProductStatus.WAIT_START, clientIp);

		SaveProductRecordRowDo row = saveProductRecordRowHelper.createProductRecordRowDo(saveProductRecordDo.getId(), productRequestDto, SaveProductStatus.WAIT_START, null);
		productRequestDto.getProduct().setRecordRowId(row.getId());

		Pair<List<ProductMasterResultDto>, List<ProductMasterResultDto>> productMasterResultPair = syncBaseProductInfoHelper.findVariantProductsFromProductMaster(userDto, List.of(baseProduct));
		Pair<Boolean, String> passCheck = syncBaseProductInfoHelper.variantProductCheckAndUpdateRecordRows(productMasterResultPair, List.of(baseProduct), saveProductRecordDo);
		if (!passCheck.getLeft()) {
			throw new BadRequestException(passCheck.getRight());
		}
		syncBaseProductInfoHelper.handlePrimarySkuAndAddVariantProduct(productMasterResultPair, List.of(baseProduct), saveProductRecordDo);

		// save variant product sku
		if (hasMatrix) {
			createSaveProductRecordTask.generateMatrixProduct(userDto, productRequestDto, clientIp);
		}

		//generate relate data in row content
		CheckProductResultDto check3PlResult = productPreProcessingHelper.preProcessingHktvProduct(userDto, saveProductRecordDo, row, null);
		generateIIDSDataHelper.generateIIDSData(row);

		BigDecimal rmbRate = exchangeRateHelper.getExchangeRateByCurrency(CurrencyEnum.RMB);
		ResponseDto<Void> checkResult = checkProductHelper.checkCreateProductHandler(userDto, saveProductRecordDo, row, check3PlResult, rmbRate);
		productPriceMonitorProductHelper.priceMonitorCreateProcess(row, userDto);

		if (checkResult.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
			checkBuHelper.checkUpdateBuList(saveProductRecordDo, row);
			saveProductHelper.setFieldValueNullByRule(row);
		} else {
			row.setErrorMessage(StringUtil.generateErrorMessage(checkResult.getErrorMessageList()));
			row.setStatus(SaveProductStatus.FAIL);
			saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
		}
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return ResponseDto.<ProductRecordResponseDto>builder()
			.status(StatusCodeEnum.SUCCESS.getCode())
			.data(ProductRecordResponseDto.builder().recordId(saveProductRecordDo.getId()).build())
			.build();
	}

	@Transactional
	public void callProductMasterAndUpdateRecordRow(UserDto userDto, Long recordId) {
		//get data
		SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findById(recordId).orElseThrow();
		List<SaveProductRecordRowDo> rows = saveProductRecordRowRepository.findByRecordIdAndStatus(recordId, SaveProductStatus.WAIT_START);
		if (rows.isEmpty()) {
			saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
			return;
		}
		SaveProductRecordRowDo row = rows.get(0);
		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		ProductMasterDto productMasterDto = gson.fromJson(gson.toJson(singleEditProductDto.getProduct()), ProductMasterDto.class);

		//call product master
		SaveProductResultDto productMasterCreateProductResult = productMasterHelper.requestSaveProduct(userDto, List.of(productMasterDto), row.getSku(), saveProductRecordDo);
		saveProductRecordHelper.updateRecordByProductMasterResult(productMasterCreateProductResult, saveProductRecordDo, rows);
	}

	private CheckProductResultDto checkProductSkuExistsInMerchant(UserDto userDto, SingleEditProductDto productRequestDto) {
		List<String> skuList;
		if (CollectionUtil.isNotEmpty(productRequestDto.getVariantSkuProductList())) {
			skuList = productRequestDto.getVariantSkuProductList().stream().map(VariantMatrixProductDto::getSkuId).collect(Collectors.toList());
			skuList.add(productRequestDto.getProduct().getSkuId());
		} else {
			skuList = List.of(productRequestDto.getProduct().getSkuId());
		}

		return checkProductHelper.checkProductSkuExistsInStore(userDto, productRequestDto.getProduct().getAdditional().getHktv().getStores(), skuList);
	}
}
