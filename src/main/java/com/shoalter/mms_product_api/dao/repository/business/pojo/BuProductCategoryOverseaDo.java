package com.shoalter.mms_product_api.dao.repository.business.pojo;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "BU_PRODUCT_CATEGORY_OVERSEA")
@Data
public class BuProductCategoryOverseaDo {
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Id
	@Column(name = "ID")
	private Integer id;
	@Column(name = "CATEGORY_ID")
	private Integer categoryId;
	@Column(name = "REGION")
	private String region;
	@Column(name = "CREATED_BY")
	private String createdBy;
	@Column(name = "CREATED_DATE")
	private Date createdDate;
	@Column(name = "LAST_UPDATED_BY")
	private String lastUpdatedBy;
	@Column(name = "LAST_UPDATED_DATE")
	private Date lastUpdatedDate;
}
