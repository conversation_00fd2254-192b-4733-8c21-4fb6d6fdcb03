package com.shoalter.mms_product_api.service.bundle.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class BundleInventoryInfoResponseDto extends BundleInventoryInfoRequestDto {

	@JsonProperty("available_quantity")
	private Integer availableQuantity;

	public static BundleInventoryInfoResponseDto generateBundleInventoryInfoResponseDto(BundleInventoryInfoRequestDto bundleInventoryInfoRequestDto) {
		return BundleInventoryInfoResponseDto.builder()
				.mall(bundleInventoryInfoRequestDto.getMall())
				.settingQuantity(bundleInventoryInfoRequestDto.getSettingQuantity())
				.alertQuantity(bundleInventoryInfoRequestDto.getAlertQuantity())
				.ceilingQuantity(bundleInventoryInfoRequestDto.getCeilingQuantity())
				.build();
	}
}
