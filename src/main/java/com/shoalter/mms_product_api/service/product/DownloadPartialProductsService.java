package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.HktvExportInfoRepository;
import com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo.HktvExportInfoDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.DownloadPartialProductsRequestDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class DownloadPartialProductsService {

	private final MessageSource messageSource;
	private final HktvExportInfoRepository hktvExportInfoRepository;

	@Transactional
	public ResponseDto<Void> start(UserDto userDto, DownloadPartialProductsRequestDto requestDto) {
		//check request
		if (requestDto.getTemplateType() == null || CollectionUtil.isEmpty(requestDto.getSkuUuids())) {
			throw new NoDataException();
		}

		if (requestDto.getSkuUuids().size() > ConstantType.PRODUCT_UPLOAD_MAX_SIZE) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message136", null, null)));
		}

		// temprary disabled: check if export is processing
//		List<HktvExportInfoDo> hktvExportInfoDoList = hktvExportInfoRepository.findByMerchantIdAndStatus(userDto.getMerchantId(), ExportStatusEnum.PROCESSING.getExportStatus());
//		if (CollectionUtil.isNotEmpty(hktvExportInfoDoList)) {
//			return ResponseDto.fail(List.of(messageSource.getMessage("message117", null, null)));
//		}

		HktvExportInfoDo hktvExportInfoDo = hktvExportInfoRepository.save(HktvExportInfoDo.generate(requestDto.getTemplateType(), userDto, requestDto.getSkuUuids()));

		log.info("HKTV export info created: {}", hktvExportInfoDo.getId());

		return ResponseDto.success(null);
	}

}
