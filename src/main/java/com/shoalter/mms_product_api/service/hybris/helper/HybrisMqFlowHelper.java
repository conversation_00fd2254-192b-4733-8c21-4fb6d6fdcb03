package com.shoalter.mms_product_api.service.hybris.helper;

import static com.shoalter.mms_product_api.util.BigDecimalUtil.removeAmtLastZero;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.HybrisMqHandlingEnum;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductProtocol;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.edit_column.SaveProductRecordRowTempStatusEnum;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowTempRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowTempDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductStoreStatusRepository;
import com.shoalter.mms_product_api.dao.repository.product.TempProductSyncHybrisRecordRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreStatusDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreStatusHistoryDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.RecordRowTraceIdStatusViewDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.TempProductSyncHybrisRecordDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.helper.RabbitTemplateHelper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.hybris.enums.HybrisMqFromSystemEnum;
import com.shoalter.mms_product_api.service.hybris.enums.SyncHybrisStatusEnum;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisProductResultMqMessageDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateDiscountRuleMqMessage;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateOnOfflineDateActionDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateOnOfflineDateActionDto.HybrisUpdateOnOfflineDateActionDtoBuilder;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdatePriceActionDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateProductMainMqMessage;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateProductMainMqMessage.HybrisUpdateProductMainMqMessageBuilder;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateReadyPickupDaysActionDto;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.helper.UserHelper;
import com.shoalter.mms_product_api.service.product.pojo.ExchangeRatePriceDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterMqDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterReturnDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductData;
import com.shoalter.mms_product_api.util.DateUtil;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.shoalter.mms_product_api.util.StringUtil;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class HybrisMqFlowHelper {
	private final SaveProductHelper saveProductHelper;
	private final RabbitTemplateHelper rabbitTemplateHelper;
	private final UserHelper userHelper;
	private final ProductRepository productRepository;
	private final StoreRepository storeRepository;
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowTempRepository saveProductRecordRowTempRepository;
	private final TempProductSyncHybrisRecordRepository syncHybrisRecordRepository;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ExchangeRateHelper exchangeRateHelper;
	private final MessageSource messageSource;
	private final Gson gson;
	private final ProductStoreStatusRepository productStoreStatusRepository;

	private static final DateTimeFormatter hybrisDateFormat = DateTimeFormatter.ofPattern(
		"MM/dd/yyyy HH:mm:ss");

	public ResponseDto<Map<SaveProductRecordRowTempDo, HybrisUpdateProductMainMqMessage>> generateDateFromProductMasterUpdateProductRequest(
		HybrisMqHandlingEnum hybrisMqHandlingEnum, String correlationId, List<ProductMasterMqDto> productMasterMqDto) {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.DATE_FORMAT_SIMPLE);
		Map<SaveProductRecordRowTempDo, HybrisUpdateProductMainMqMessage> result = new HashMap<>();

		try {
			//find storefront store code
			Set<String> storeCodes = productMasterMqDto.stream().map(HktvProductDto::getStores).collect(Collectors.toSet());
			List<StoreDo> storeDos = storeRepository.findByStoreCodeIn(storeCodes);
			Map<String, StoreDo> storeCodeMap = storeDos.stream()
				.collect(Collectors.toMap(StoreDo::getStoreCode, Function.identity()));

			SaveProductData saveProductData = null;
			if (HybrisMqHandlingEnum.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB == hybrisMqHandlingEnum) {
				Set<String> storefrontStoreCodes = storeDos.stream().map(StoreDo::getStorefrontStoreCode).collect(Collectors.toSet());
				saveProductData = exchangeRateHelper.generateSaveProductData(hybrisMqHandlingEnum.getSaveProductType(), storefrontStoreCodes);
				if (saveProductData.getRmbToHkdExchangeRate() == null) {
					return ResponseDto.fail(List.of(messageSource.getMessage("message360", null, null)));
				}
			}

			//generate data
			for (ProductMasterMqDto product : productMasterMqDto) {
				StoreDo storeDo = storeCodeMap.get(product.getStores());
				HybrisUpdateProductMainMqMessage hybrisUpdateProductMainMqMessage = generateHybrisUpdateProductMainMqMessage(hybrisMqHandlingEnum, product, storeDo, simpleDateFormat, saveProductData);
				Optional<SaveProductRecordRowTempDo> saveProductRecordRowTempDoOpt = saveProductRecordRowTempRepository.findByRecordRowId(product.getRecordRowId());
				if (saveProductRecordRowTempDoOpt.isPresent()) {
					log.info("sku uuid {} of record row id {} is being rolled back", product.getUuid(), product.getRecordRowId());
					result.put(saveProductRecordRowTempDoOpt.get(), hybrisUpdateProductMainMqMessage);
				} else {
					SaveProductRecordRowTempDo saveProductRecordRowTempDo = saveProductRecordRowTempRepository.save(SaveProductRecordRowTempDo.generateInitObject(product, correlationId));
					result.put(saveProductRecordRowTempDo, hybrisUpdateProductMainMqMessage);
				}
			}

		} catch (Exception e) {
			List<String> errorMessage = List.of("handle special handling flow error : " + e.getMessage());
			log.error(e.getMessage(), e);
			return ResponseDto.fail(errorMessage);
		}

		return ResponseDto.success(result);
	}

	public void updateRecordRowTempByHybrisProductUpdateResult(HybrisProductResultMqMessageDto messageDto) {
		SaveProductRecordRowTempDo saveProductRecordRowTempDo = null;
		boolean hybrisSuccess = StatusCodeEnum.SUCCESS.name().equalsIgnoreCase(messageDto.getStatus());

		try {
			//get record info
			Long recordRowId = Long.parseLong(messageDto.getTraceId());
			SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findByRecordRowId(recordRowId).orElseThrow();
			UserDto userDto = userHelper.generateUserDtoByRecord(saveProductRecordDo);
			saveProductRecordRowTempDo = saveProductRecordRowHelper.findSaveProductRecordRowTempByRecordRowIdRetryable(recordRowId).orElseThrow();

			//update record info depend on hybris' result
			if (!StatusCodeEnum.SUCCESS.name().equalsIgnoreCase(messageDto.getStatus())) {
				saveProductRecordRowTempDo.setStatus(SaveProductRecordRowTempStatusEnum.FAIL);
				saveProductRecordRowTempDo.setErrorMessage("hybris update product fail: " + messageDto.getMessage());
			} else {
				saveProductRecordRowTempDo.setStatus(SaveProductRecordRowTempStatusEnum.SUCCESS);
				ProductMasterMqDto productMasterMqDto = saveProductRecordRowTempDo.getContent();
				SpringBeanProvider.getBean(HybrisMqFlowHelper.class).updateProductAndSaveHistory(userDto, saveProductRecordDo, productMasterMqDto);
			}

		} catch (Exception e) {
			if (saveProductRecordRowTempDo != null) {
				saveProductRecordRowTempDo.setStatus(hybrisSuccess ? SaveProductRecordRowTempStatusEnum.ROLLBACK_FAIL : SaveProductRecordRowTempStatusEnum.FAIL);
				saveProductRecordRowTempDo.setErrorMessage("update product fail: " + e.getMessage());
			}
			log.error("handle hybris product update result failed. errorMessage: {}", e.getMessage(), e);
		}

		if (saveProductRecordRowTempDo != null) {
			saveProductRecordRowTempRepository.save(saveProductRecordRowTempDo);
		}
	}

	public void updateSyncHybrisRecordByHybrisProductUpdateResult(HybrisProductResultMqMessageDto messageDto) {
		TempProductSyncHybrisRecordDo syncHybrisRecordDo = syncHybrisRecordRepository.findByTraceId(messageDto.getTraceId()).orElse(null);
		if (syncHybrisRecordDo == null) {
			log.error("handle hybris product update result failed. Cannot find TempProductSyncHybrisRecordDo by traceId: {}, fromSystem: {}",
				messageDto.getTraceId(), messageDto.getFromSystem());
			return;
		}
		boolean hybrisSuccess = StatusCodeEnum.SUCCESS.name().equalsIgnoreCase(messageDto.getStatus());
		if (Boolean.FALSE.equals(hybrisSuccess)) {
			syncHybrisRecordDo.setStatus(SyncHybrisStatusEnum.FAIL);
			log.error("hybris update product fail: {}", messageDto.getMessage());
		} else {
			syncHybrisRecordDo.setStatus(SyncHybrisStatusEnum.SUCCESS);
		}
		syncHybrisRecordRepository.save(syncHybrisRecordDo);
	}

	public void handleHybrisMqFlowProcess(SaveProductRecordDo record, List<SaveProductRecordRowDo> rowList) {
		if (!SaveProductProtocol.QUEUE.equals(record.getProtocol())) {
			return;
		}

		log.info("checking record id {} for hybris mq flow, upload type: {}", record.getId(), record.getUploadType());

		//find trace id status
		List<Long> recordRowIds = rowList.stream().map(SaveProductRecordRowDo::getId).collect(Collectors.toList());
		List<RecordRowTraceIdStatusViewDo> traceIdStatusViewDoList = saveProductRecordRowTempRepository.findTraceIdStatusByRecordRowIds(recordRowIds);
		Map<String, Set<SaveProductRecordRowTempStatusEnum>> traceIdStatusMap = traceIdStatusViewDoList.stream()
			.collect(Collectors.groupingBy(RecordRowTraceIdStatusViewDo::getTraceId,
				Collectors.mapping(RecordRowTraceIdStatusViewDo::getStatus, Collectors.toSet())));

		//check trace id status
		List<String> needToHandleTraceIds = new ArrayList<>();
		for (Entry<String, Set<SaveProductRecordRowTempStatusEnum>> entry : traceIdStatusMap.entrySet()) {
			Set<SaveProductRecordRowTempStatusEnum> currentTraceIdStatus = entry.getValue();

			//if all product result of a trace id was collected, sent result to product master
			if (SaveProductRecordRowTempStatusEnum.TRANSITIONAL_STATUS_SET.containsAll(currentTraceIdStatus)) {
				needToHandleTraceIds.add(entry.getKey());
			}
		}

		if (needToHandleTraceIds.isEmpty()) {
			return;
		}


		log.info("all product results of record id {} for hybris mq flow are collected", record.getId());
		List<SaveProductRecordRowTempDo> recordInfoList = saveProductRecordRowTempRepository.findByRecordRowIdInAndTraceIdIn(recordRowIds, needToHandleTraceIds);

		//gather data
		Map<String, DataWrapper> processResult = new HashMap<>();
		for (SaveProductRecordRowTempDo recordInfo : recordInfoList) {
			processResult.putIfAbsent(recordInfo.getTraceId(), new DataWrapper(new ArrayList<>(), new ArrayList<>()));
			DataWrapper dataWrapper = processResult.get(recordInfo.getTraceId());
			dataWrapper.getRecordInfoList().add(recordInfo);
			dataWrapper.getProductMasterReturnDtoList().add(ProductMasterReturnDto.generate(recordInfo));
			recordInfo.setStatus(SaveProductRecordRowTempStatusEnum.SENT_PM);
		}

		//send MQ
		processResult.forEach((traceId, dataWrapper) -> SpringBeanProvider.getBean(HybrisMqFlowHelper.class)
			.sendProductMasterResultMqAndUpdateRecordInfo(traceId, dataWrapper.getProductMasterReturnDtoList(), dataWrapper.getRecordInfoList()));
	}


	@Transactional
	public void sendToHybrisAndUpsertRecordRowTemp(String correlationId, SaveProductRecordRowTempDo saveProductRecordRowTempDo, HybrisUpdateProductMainMqMessage hybrisUpdateProductMainMqMessage) {
		try {
			rabbitTemplateHelper.sendProductUpdateToHybris(hybrisUpdateProductMainMqMessage, correlationId);
		} catch (Exception e) {
			log.error("sendToHybrisAndUpsertRecordRowTemp Error traceId:{}, storefrontStoreCode:{}, SkuCode:{}, error message:{}",
				correlationId, hybrisUpdateProductMainMqMessage.getMerchantId(), hybrisUpdateProductMainMqMessage.getSkuCode(), e.getMessage(), e);
			saveProductRecordRowTempDo.setStatus(SaveProductRecordRowTempStatusEnum.FAIL);
			saveProductRecordRowTempDo.setErrorMessage("Send Product Update To Hybris Error : " + e.getMessage());
		}
		saveProductRecordRowTempRepository.save(saveProductRecordRowTempDo);
	}

	@Transactional
	public void sendToHybrisForSync(HybrisUpdateProductMainMqMessage hybrisUpdateProductMainMqMessage, TempProductSyncHybrisRecordDo syncHybrisRecordDo) {
		try {
			rabbitTemplateHelper.sendProductUpdateToHybris(hybrisUpdateProductMainMqMessage, hybrisUpdateProductMainMqMessage.getTraceId());
		} catch (Exception e) {
			log.error("sendToHybrisForSync Error, traceId:{}, storefrontStoreCode:{}, SkuCode:{}, error message:{}",
				hybrisUpdateProductMainMqMessage.getTraceId(), hybrisUpdateProductMainMqMessage.getMerchantId(), hybrisUpdateProductMainMqMessage.getSkuCode(), e.getMessage(), e);
			syncHybrisRecordDo.setStatus(SyncHybrisStatusEnum.FAIL);
		}
		syncHybrisRecordRepository.save(syncHybrisRecordDo);
	}

	@Transactional
	public void sendProductMasterResultMqAndUpdateRecordInfo(
		String traceId, List<ProductMasterReturnDto> productMasterReturnDtoList, List<SaveProductRecordRowTempDo> recordInfoList) {
		try {
			rabbitTemplateHelper.sendProductResultToProductMaster(productMasterReturnDtoList, traceId);
			saveProductRecordRowTempRepository.saveAll(recordInfoList);
		} catch (Exception e) {
			log.error("exception occur when send product result message: ", e);
		}
	}

	@Transactional
	public void updateProductAndSaveHistory(UserDto userDto,
		SaveProductRecordDo saveProductRecordDo, ProductMasterMqDto productMasterMqDto) {

		String productUuid = productMasterMqDto.getUuid();

		ProductDo productDo =
			updateProductDetails(userDto, saveProductRecordDo, productMasterMqDto, productUuid);

		updateProductStoreStatusDetails(saveProductRecordDo, productMasterMqDto, productUuid);

		//save history
		ProductStoreStatusHistoryDo productStoreStatusHistoryDo = saveProductHelper.generateProductStoreStatusHistory(
			productDo, productMasterMqDto);
		saveProductHelper.saveProductHistory(productDo.getId(), saveProductRecordDo.getUserIp(),
			productStoreStatusHistoryDo);
	}

	private void updateProductStoreStatusDetails(SaveProductRecordDo saveProductRecordDo,
		ProductMasterMqDto productMasterMqDto, String productUuid) {

		if (HybrisMqHandlingEnum.isUpdateOnlineStatus(saveProductRecordDo)) {
		  ProductStoreStatusDo productStoreStatusDo =
			productStoreStatusRepository.findBySkuUuid(productUuid).orElseThrow();
			productStoreStatusDo.setOnlineStatus(productMasterMqDto.getOnlineStatus().toString());
			productStoreStatusRepository.save(productStoreStatusDo);
		}

	}

	private ProductDo updateProductDetails(UserDto userDto, SaveProductRecordDo saveProductRecordDo,
		ProductMasterMqDto productMasterMqDto, String productUuid) {

		boolean isUpdateProduct = false;

		ProductDo productDo = productRepository.findByUuid(productUuid).orElseThrow();
		saveProductHelper.resetUpdateProductParameter(productMasterMqDto, productDo);

		if (HybrisMqHandlingEnum.isUpdatePrice(saveProductRecordDo)) {
			saveProductHelper.setPriceRelatedField(productMasterMqDto, productDo);
			isUpdateProduct = true;
		}

		if (HybrisMqHandlingEnum.isUpdateProductReadyDays(saveProductRecordDo)) {
			isUpdateProduct = true;
			productDo.setProductReadyDays(productMasterMqDto.getProductReadyDays());
		}

		if (!isUpdateProduct){
			return productDo;
		}

		productDo.setLastUpdatedDate(new Date());
		productDo.setLastUpdatedBy(userDto.getUserCode());
		productDo = productRepository.save(productDo);
		return productDo;
	}

	private HybrisUpdateProductMainMqMessage generateHybrisUpdateProductMainMqMessage(
		HybrisMqHandlingEnum hybrisMqHandlingEnum, ProductMasterMqDto product, StoreDo storeDo, SimpleDateFormat simpleDateFormat, SaveProductData saveProductData) throws ParseException {
		switch (hybrisMqHandlingEnum) {
			case OPEN_API_BATCH_EDIT_PRICE:
				return HybrisUpdatePriceActionDto.builder()
					.traceId(product.getRecordRowId().toString())
					.fromSystem(HybrisMqFromSystemEnum.MMS_PRODUCT.getCode())
					.action(hybrisMqHandlingEnum.getAction())
					.merchantId(
						saveProductHelper.generateDefaultStringValue(storeDo.getStorefrontStoreCode()))
					.skuCode(product.getSkuId())
					.originalPrice(
						product.getOriginalPrice() != null ? product.getOriginalPrice().doubleValue() : null)
					.primaryHktvCatId(product.getPrimaryCategoryCode())
					.discountRule(
						generateSaveHybrisCreatePromotionalDiscountRuleDto(product, simpleDateFormat))
					.build();
			case BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB:
				ExchangeRatePriceDto exchangeRatePrice = exchangeRateHelper.convertHybrisMqExchangeRatePrice(hybrisMqHandlingEnum, product.getSkuId(), saveProductData, product.getCurrency(), product.getOriginalPrice());
				log.info("generateHybrisUpdateProductMainMqMessage store:{}, skuId:{}, currency:{}, exchangeRatePrice:{}", storeDo.getStorefrontStoreCode(), product.getSkuId(), product.getCurrency(), gson.toJson(exchangeRatePrice));
				BigDecimal originalPrice = exchangeRatePrice.getOriginalPriceHkd();
				BigDecimal originalMainlandPrice = exchangeRatePrice.getOriginalMainlandPrice();

				return HybrisUpdatePriceActionDto.builder()
					.traceId(product.getRecordRowId().toString())
					.fromSystem(HybrisMqFromSystemEnum.MMS_PRODUCT.getCode())
					.action(hybrisMqHandlingEnum.getAction())
					.merchantId(
						saveProductHelper.generateDefaultStringValue(storeDo.getStorefrontStoreCode()))
					.skuCode(product.getSkuId())
					.originalPrice(originalPrice == null ? null : originalPrice.doubleValue())
					.originalMainlandPrice(
						originalMainlandPrice == null ? null : originalMainlandPrice.doubleValue())
					.primaryHktvCatId(product.getPrimaryCategoryCode())
					.discountRule(null)
					.build();
			case OPEN_API_BATCH_EDIT_PRODUCT_READY_DAYS:
				return HybrisUpdateReadyPickupDaysActionDto.builder()
					.traceId(product.getRecordRowId().toString())
					.fromSystem(HybrisMqFromSystemEnum.MMS_PRODUCT.getCode())
					.action(hybrisMqHandlingEnum.getAction())
					.merchantId(
						saveProductHelper.generateDefaultStringValue(storeDo.getStorefrontStoreCode()))
					.skuCode(product.getSkuId())
					.readyPickupDays(Integer.parseInt(product.getProductReadyDays()))
					.build();

			case BATCH_EDIT_PRODUCT_ONLINE_STATUS:

				HybrisUpdateOnOfflineDateActionDtoBuilder<?, ?> hybrisUpdateOnOfflineDateActionDtoBuilder =
					initCommonFields(
						HybrisUpdateOnOfflineDateActionDto.builder(),
						hybrisMqHandlingEnum,
						product,
						storeDo);

				String offlineDate =
					OnlineStatusEnum.OFFLINE == product.getOnlineStatus() ?
						hybrisDateFormat.format(LocalDateTime.now()) : StringUtil.HYBRIS_ONLINE_DATE;

				return
					hybrisUpdateOnOfflineDateActionDtoBuilder
						.onlineDate("")
						.offlineDate(offlineDate)
						.build();

			default:
				return null;
		}
	}

	@SuppressWarnings("unchecked")
	private <T extends HybrisUpdateProductMainMqMessageBuilder<?, ?> > T initCommonFields(
		T builder,
		HybrisMqHandlingEnum hybrisMqHandlingEnum,
		ProductMasterMqDto product,
		StoreDo storeDo
	) {
		return (T) builder
			.traceId(product.getRecordRowId().toString())
			.fromSystem(HybrisMqFromSystemEnum.MMS_PRODUCT.getCode())
			.action(hybrisMqHandlingEnum.getAction())
			.merchantId(saveProductHelper.generateDefaultStringValue(storeDo.getStorefrontStoreCode()))
			.skuCode(product.getSkuId());
	}

	private HybrisUpdateDiscountRuleMqMessage generateSaveHybrisCreatePromotionalDiscountRuleDto(ProductMasterMqDto product, SimpleDateFormat simpleDateFormat) throws ParseException {
		BigDecimal normalDiscount = product.getSellingPrice();
		if (normalDiscount == null || normalDiscount.doubleValue() == 0d) {
			log.info("Discount Price equal to 0, set to original price and request to expire the discount rule.");
			normalDiscount = product.getOriginalPrice();
		}
		BigDecimal commissionRate = product.getCommissionRate();
		String discountTextEn = product.getDiscountTextEn();
		String discountTextZh = product.getDiscountTextCh();
		String discountTextSc = product.getDiscountTextSc();
		String style = product.getStyle();
		Date startDate = new Date();
		Date endDate = product.getSellingPrice() != null ? simpleDateFormat.parse(DateUtil.FOREVER_DATE) : new Date();

		HybrisUpdateDiscountRuleMqMessage saveHybrisCreatePromotionalDiscountRuleDto = new HybrisUpdateDiscountRuleMqMessage();
		saveHybrisCreatePromotionalDiscountRuleDto.setNormalDiscount(normalDiscount == null ? null : normalDiscount.doubleValue());
		saveHybrisCreatePromotionalDiscountRuleDto.setStartDate(simpleDateFormat.format(startDate));
		saveHybrisCreatePromotionalDiscountRuleDto.setEndDate(simpleDateFormat.format(endDate));

		// commission rate cut float point when it's an integer
		if (commissionRate != null && Math.max(0, commissionRate.stripTrailingZeros().scale()) == 0) {
			saveHybrisCreatePromotionalDiscountRuleDto.setCommissionRate(removeAmtLastZero(commissionRate));
		} else {
			saveHybrisCreatePromotionalDiscountRuleDto.setCommissionRate(commissionRate);
		}

		saveHybrisCreatePromotionalDiscountRuleDto.setDiscountTextEn(discountTextEn);
		saveHybrisCreatePromotionalDiscountRuleDto.setDiscountTextZh(discountTextZh);
		saveHybrisCreatePromotionalDiscountRuleDto.setDiscountTextZhCN(discountTextSc);
		saveHybrisCreatePromotionalDiscountRuleDto.setStyle(style);
		return saveHybrisCreatePromotionalDiscountRuleDto;
	}

	@Getter
	@AllArgsConstructor
	private static class DataWrapper {
		List<ProductMasterReturnDto> productMasterReturnDtoList;
		List<SaveProductRecordRowTempDo> recordInfoList;
	}
}
