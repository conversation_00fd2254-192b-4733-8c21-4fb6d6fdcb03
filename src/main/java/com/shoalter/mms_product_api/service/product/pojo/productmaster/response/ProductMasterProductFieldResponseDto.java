package com.shoalter.mms_product_api.service.product.pojo.productmaster.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class ProductMasterProductFieldResponseDto {
    private Integer pid;
    private Integer priority;
    @JsonProperty("product_field_category")
    @SerializedName("product_field_category")
    private List<ProductMasterProductFieldCategoryResponseDto> productFieldCategory;
    @JsonProperty("product_field_option")
    @SerializedName("product_field_option")
    private List<ProductMasterProductFieldOptionResponseDto> productFieldOption;
}
