package com.shoalter.mms_product_api.helper;

import com.github.houbb.opencc4j.util.ZhTwConverterUtil;
import com.shoalter.mms_product_api.helper.constant.CustomDictionaryConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Helper class for text translation between different Chinese variants.
 */
@Service
public class TranslationHelper {

    // Thread-safe cache for compiled regex patterns
    private static final ConcurrentHashMap<String, Pattern> patternCache = new ConcurrentHashMap<>();

    /**
     * Translates the given text if the input is not empty.
     */
    public String translateIfNotEmpty(String text) {
        return translateIfNotEmpty(text, true);
    }

    /**
     * Translates the given text to simplified Chinese with optional custom dictionary usage.
     */
    public String translateIfNotEmpty(String text, boolean useCustomDictionary) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }

        String processedText = text;
        if (useCustomDictionary) {
            Map<String, String> dictionary = CustomDictionaryConstant.getDictionary(CustomDictionaryConstant.HK_CN_DICTIONARY_TAG);
            processedText = applyDictionaryTranslation(processedText, dictionary, CustomDictionaryConstant.HK_CN_DICTIONARY_TAG);
        }

        return ZhTwConverterUtil.toSimple(processedText);
    }

    /**
     * Applies dictionary-based translation to the input text.
     * Uses regex pattern matching for efficient text replacement.
     *
     * @param text the text to be processed
     * @param dictionary the translation dictionary to use
     * @param dictionaryTag identifier for the dictionary (used for pattern caching)
     * @return processed text with dictionary replacements applied
     */
    private String applyDictionaryTranslation(String text, Map<String, String> dictionary, String dictionaryTag) {
        if (dictionary == null || dictionary.isEmpty() || text == null) {
            return text;
        }

        // Get or create pattern from cache based on dictionary tag
        Pattern pattern = patternCache.computeIfAbsent(dictionaryTag, key -> createPatternFromDictionary(dictionary));

        // Replace occurrences of the keys with their corresponding values
        Matcher matcher = pattern.matcher(text);
        StringBuilder buffer = new StringBuilder();
        while (matcher.find()) {
            String replacement = dictionary.get(matcher.group());
            matcher.appendReplacement(buffer, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(buffer);
        return buffer.toString();
    }

    /**
     * Creates a regex pattern from dictionary keys, sorted by length (descending) to ensure
     * longer matches take precedence.
     */
    private Pattern createPatternFromDictionary(Map<String, String> dictionary) {
        List<String> sortedKeys = dictionary.keySet().stream()
            .sorted((a, b) -> b.length() - a.length())
            .collect(Collectors.toList());
        String patternString = String.join("|", sortedKeys);
        return Pattern.compile(patternString);
    }
}
