package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.dao.repository.store.StoreOverseaDeliveryRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.CategoryOverseaRegionDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.DeliveryDistrictDto;
import com.shoalter.mms_product_api.service.product.pojo.response.DeliveryDistrictMainResponseData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class FindDeliveryDistrictService {

	private final StoreOverseaDeliveryRepository storeOverseaDeliveryRepository;

	public ResponseDto<List<String>> start(String buCode, Integer storeId, Integer primaryCategoryId) {
		List<CategoryOverseaRegionDo> categoryRegions = storeOverseaDeliveryRepository.findCategoryOverseaRegionByStoreIdAndCategoryIds(buCode, storeId, List.of(primaryCategoryId));

		Map<Integer, Set<String>> categoryRegionMap = groupRegionsByCategoryId(categoryRegions);

		return ResponseDto.<List<String>>builder()
			.data(new ArrayList<>(categoryRegionMap.getOrDefault(primaryCategoryId, Set.of())))
			.status(1).build();
	}

	public ResponseDto<DeliveryDistrictMainResponseData> start(String buCode, Integer storeId, List<String> categoryCodeList) {
		List<CategoryOverseaRegionDo> categoryRegions = storeOverseaDeliveryRepository.findCategoryOverseaRegionByStoreIdAndCategoryCodes(buCode, storeId, categoryCodeList);

		Map<String, Set<String>> categoryRegionMap = groupRegionsByCategoryCode(categoryRegions);

		List<DeliveryDistrictDto> results = mapToDtoByCategoryCode(categoryRegionMap);

		return ResponseDto.<DeliveryDistrictMainResponseData>builder()
			.data(DeliveryDistrictMainResponseData.builder().list(results).build())
			.status(1).build();
	}

	/**
	 * Groups oversea delivery regions by category ID
	 */
	private Map<Integer, Set<String>> groupRegionsByCategoryId(List<CategoryOverseaRegionDo> categoryRegions) {
		return categoryRegions.stream()
			.collect(Collectors.groupingBy(
				CategoryOverseaRegionDo::getCategoryId,
				Collectors.mapping(CategoryOverseaRegionDo::getRegion, Collectors.toSet())
			));
	}

	/**
	 * Groups oversea delivery regions by category Code
	 */
	private Map<String, Set<String>> groupRegionsByCategoryCode(List<CategoryOverseaRegionDo> categoryRegions) {
		return categoryRegions.stream()
			.collect(Collectors.groupingBy(
				CategoryOverseaRegionDo::getCategoryCode,
				Collectors.mapping(CategoryOverseaRegionDo::getRegion, Collectors.toSet())
			));
	}

	/**
	 * Converts a map of category Codes and their corresponding regions to a list of DeliveryDistrictDto objects.
	 */
	private List<DeliveryDistrictDto> mapToDtoByCategoryCode(Map<String, Set<String>> categoryRegionMap) {
		return categoryRegionMap.entrySet().stream()
			.map(entry -> DeliveryDistrictDto.builder()
				.primaryCategoryCode(entry.getKey())
				.regions(entry.getValue())
				.build())
			.collect(Collectors.toList());
	}
}
