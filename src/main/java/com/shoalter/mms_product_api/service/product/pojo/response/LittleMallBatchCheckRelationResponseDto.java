package com.shoalter.mms_product_api.service.product.pojo.response;

import com.shoalter.mms_product_api.service.product.pojo.CheckIndexErrorDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallCheckRelationErrorDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LittleMallBatchCheckRelationResponseDto {

	private List<LittleMallCheckRelationErrorDto> relationErrors = new ArrayList<>();
	private List<CheckIndexErrorDto> checkIndexErrors = new ArrayList<>();

}
