package com.shoalter.mms_product_api.queue.mock;


import com.google.gson.Gson;
import com.rabbitmq.client.Channel;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisProductResultMqMessageDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateOnOfflineDateActionDto;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(prefix = "mock.listener", name = "enabled", havingValue = "true", matchIfMissing = false)
public class MockListener {

	private final Gson gson;
	private final RabbitTemplate rabbitTemplate;

	private final static String QUEUE_NAME = "hktvmall_hybris_update_product_from_mms_product_to_hktvmall_hybris_queue";

	@SneakyThrows
	@RabbitListener(queues = QUEUE_NAME, containerFactory = "productMasterSingleContainerFactory", ackMode = "MANUAL")
	public void receive(@Payload HybrisUpdateOnOfflineDateActionDto messageDto, Message message,
		Channel channel) {
		log.info("Mock listener start, Consumer {}, message: {}", QUEUE_NAME, gson.toJson(messageDto));
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();

		try {

			HybrisProductResultMqMessageDto hybrisProductResultMqMessageDto = new HybrisProductResultMqMessageDto();
			hybrisProductResultMqMessageDto.setTraceId(messageDto.getTraceId());
			hybrisProductResultMqMessageDto.setStatus("success");

			rabbitTemplate.convertAndSend(
				"hktvmall_hybris_topic",
				"hktvmall_hybris.update_product_result_from_hktvmall_hybris_to_mms_product",
				hybrisProductResultMqMessageDto);

		} catch (Exception e) {
			log.error("Consumer {} fail, errorMessage: {}", QUEUE_NAME, e.getMessage());
			log.error(e.getMessage(), e);
		}

		channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
		stopWatch.stop();
		log.info("Time taken by {} receive method : {} milliseconds", QUEUE_NAME,
			stopWatch.getTotalTimeMillis());
	}
}
