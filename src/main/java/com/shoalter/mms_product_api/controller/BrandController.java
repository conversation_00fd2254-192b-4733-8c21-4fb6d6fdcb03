package com.shoalter.mms_product_api.controller;

import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.FindBrandService;
import com.shoalter.mms_product_api.service.product.pojo.response.BrandDto;
import com.shoalter.mms_product_api.service.product.pojo.response.FindBrandDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Brand")
@RequiredArgsConstructor
@RestController
@PreAuthorize("hasRole('ROLE_MMS_ROLE')")
@Slf4j
@RequestMapping("/brand")
public class BrandController {

	private final FindBrandService findBrandService;

	@GetMapping("/{brandId}")
	public ResponseDto<FindBrandDto> findBrandById(@PathVariable(required = true) Integer brandId) {

		return findBrandService.start(brandId);
	}

	@GetMapping
	public ResponseDto<List<BrandDto>> findBrandByBuCodeAndKeyword(
		@RequestParam String buCode,
		@RequestParam(required = false) String keyword) {
		return findBrandService.start(buCode, keyword);
	}

}
