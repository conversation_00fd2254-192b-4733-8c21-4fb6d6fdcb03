package com.shoalter.mms_product_api.config.product;

import java.util.Set;

public enum SaveProductStatusEnum {
	USER_INVISIBLE_WAIT_START(-3, "USER_INVISIBLE_WAIT_START"),
	WAIT_START(-2, "WAIT_START"),
	FAIL(-1, "FAIL"),
	PROCESSING(0, "PROCESSING"),
	SUCCESS(1, "SUCCESS"),
	CHECKING_PRODUCT(2, "CHECKING_PRODUCT"),
	REQUESTING_PM(3, "REQUESTING_PM"),
	CHECKING_PM(4, "CHECKING_PM"),
	CHECKING_UPDATE_BINDING_PRODUCT(7, "CHECKING_UPDATE_BINDING_PRODUCT"),
	REQUESTING_HKTV_AND_PM(8,"REQUESTING_HKTV_AND_PM"),
	UPDATING(9, "UPDATING");

	public static Set<Integer> UPDATING_LIST = Set.of(WAIT_START.getCode(), PROCESSING.getCode(), CHECKING_PRODUCT.getCode(), REQUESTING_PM.getCode(), CHECKING_PM.getCode());

	private final int code;
	private final String name;

	SaveProductStatusEnum(int code, String name) {
		this.code = code;
		this.name = name;
	}

	public int getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public static String getProductStatusName(int code) {
		for (SaveProductStatusEnum type : values()) {
			if (type.getCode() == code) {
				return type.getName();
			}
		}
		return "UNKNOWN_SAVE_PRODUCT_STATUS";
	}
}
