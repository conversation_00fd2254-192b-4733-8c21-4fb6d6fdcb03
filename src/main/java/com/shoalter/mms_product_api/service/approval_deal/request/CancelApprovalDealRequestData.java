package com.shoalter.mms_product_api.service.approval_deal.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import lombok.Data;
import lombok.NonNull;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CancelApprovalDealRequestData {
	@JsonProperty("bu_code")
	private BuCodeEnum buCode;
	private List<String> storefrontStoreCodes;
}
