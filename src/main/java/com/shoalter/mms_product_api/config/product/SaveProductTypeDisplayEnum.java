package com.shoalter.mms_product_api.config.product;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum SaveProductTypeDisplayEnum {
	SINGLE_CREATE("Single Create Product", List.of(SaveProductType.SINGLE_CREATE_PRODUCT, SaveProductType.SINGLE_CREATE_LITTLE_MALL_PRODUCT)),
	SINGLE_EDIT("Single Edit Product", List.of(SaveProductType.SINGLE_EDIT_PRODUCT, SaveProductType.SINGLE_EDIT_LITTLE_MALL_PRODUCT, SaveProductType.COMMISSION_RATE_APPROVE)),
	BATCH_CREATE("Batch Create Product", List.of(SaveProductType.BATCH_CREATE_PRODUCT, SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT)),
	BATCH_CREATE_WITH_EXCEL("Batch Create with Excel", List.of(SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL, SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL)),
	IMPORT_FROM_SHOPLINE("Import From Shopline", List.of(SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE)),
	IMPORT_FROM_HKTV("Import Form HKTVmall", List.of(SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV)),
	IMPORT_FROM_EXCEL("Import Form Excel", List.of(SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL)),
	BATCH_EDIT_ALL("Batch Edit - All Field", List.of(SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL)),
	BATCH_EDIT_PACKAGING_DIMENSION("Batch Edit - Packing Dimension", List.of(SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION)),
	BATCH_EDIT_PRICE("Batch Edit - Price", List.of(SaveProductType.BATCH_EDIT_PRODUCT_PRICE)),
	BATCH_EDIT_ONLINE_STATUS("Batch Edit - Online Status", List.of(SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS)),
	BATCH_EDIT_VISIBILITY("Batch Edit - Visibility", List.of(SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY)),
	BATCH_EDIT_PRODUCT("Batch Edit Product", List.of(SaveProductType.BATCH_EDIT_PRODUCT, SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT)),
	BATCH_EDIT_HKTV_PRODUCT_TRANSLATE("Translate Simplified Chinese", List.of(SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE)),
	BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB("Batch Edit Price Exchange Rate RMB", List.of(SaveProductType.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB)),
	;

	private final String name;
	private final List<Integer> codes;
}
