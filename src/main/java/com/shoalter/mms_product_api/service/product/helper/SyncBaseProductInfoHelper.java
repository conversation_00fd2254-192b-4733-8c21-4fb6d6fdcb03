package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ProductMasterBusinessUnitType;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRelationRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowRelationDo;
import com.shoalter.mms_product_api.mapper.SyncVariantCheckDataMapper;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterBaseResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchUuidRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.SyncVariantCheckDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class SyncBaseProductInfoHelper {

	private final Gson gson;

	private final ProductMasterHelper productMasterHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ProductPreProcessingHelper productPreProcessingHelper;

    private final SaveProductRecordRowRepository saveProductRecordRowRepository;
    private final SaveProductRecordRowRelationRepository saveProductRecordRowRelationRepository;
	private final MessageSource messageSource;

	private Pair<Boolean, List<SingleEditProductDto>> createVariantProductRecordRow(List<ProductMasterDto> variantProductsFromUser, Map<String, ProductMasterResultDto> skuIdToProductMasterDataMap, List<ProductMasterResultDto> variantProductsWithSameProductCode, SaveProductRecordDo saveProductRecordDo) {
		List<SingleEditProductDto> variantProductRecordRows = new ArrayList<>();

		//if primary sku not include in user editing product, no need to create variant record row
		ProductMasterDto primarySku = variantProductsFromUser.stream()
			.filter(data -> ConstantType.CONSTANT_YES.equals(data.getAdditional().getHktv().getIsPrimarySku()))
			.findFirst().orElse(null);
		if (primarySku == null || !skuIdToProductMasterDataMap.containsKey(primarySku.getSkuId())) {
			return Pair.of(false, variantProductRecordRows);
		}

		SyncVariantCheckDto primaryProductFromUserEdit = SyncVariantCheckDataMapper.INSTANCE.toSyncVariantCheckDto(primarySku);
		SyncVariantCheckDto primaryProductFromProductMaster = SyncVariantCheckDataMapper.INSTANCE.toSyncVariantCheckDto(skuIdToProductMasterDataMap.get(primarySku.getSkuId()));
		boolean syncVariant = !primaryProductFromUserEdit.equals(primaryProductFromProductMaster) ||
			BooleanUtils.isTrue(primarySku.getAdditional().getHktv().getReplicateToOtherVariantsSkus());

		if (!syncVariant) {
			return Pair.of(false, variantProductRecordRows);
		}

		//only create record row if the variant is not in the user edit product list
		for (ProductMasterResultDto variantProduct : variantProductsWithSameProductCode) {
			SingleEditProductDto singleEditProductDto = convertVariantProductFromProductMasterResultDto(saveProductRecordDo, primarySku, variantProduct);
			createVariantProductRecordRowDo(saveProductRecordDo, singleEditProductDto, primarySku.getRecordRowId());
			variantProductRecordRows.add(singleEditProductDto);
		}
		return Pair.of(true, variantProductRecordRows);
	}

    private void setHktvValueToSync(ProductMasterDto baseProduct, ProductMasterDto productMasterDto) {
        HktvProductDto hktvInfo = baseProduct.getAdditional().getHktv();
        if(hktvInfo == null){
            return;
        }
        productMasterDto.setBrandId(baseProduct.getBrandId());
        productMasterDto.getAdditional().getHktv().setInvoiceRemarksEn(hktvInfo.getInvoiceRemarksEn());
        productMasterDto.getAdditional().getHktv().setInvoiceRemarksCh(hktvInfo.getInvoiceRemarksCh());
        productMasterDto.getAdditional().getHktv().setInvoiceRemarksSc(hktvInfo.getInvoiceRemarksSc());
        productMasterDto.getAdditional().getHktv().setVisibility(hktvInfo.getVisibility());
        productMasterDto.getAdditional().getHktv().setFeatureStartTime(hktvInfo.getFeatureStartTime());
        productMasterDto.getAdditional().getHktv().setFeatureEndTime(hktvInfo.getFeatureEndTime());
        productMasterDto.getAdditional().getHktv().setVoucherType(hktvInfo.getVoucherType());
        productMasterDto.getAdditional().getHktv().setVoucherDisplayType(hktvInfo.getVoucherDisplayType());
        productMasterDto.getAdditional().getHktv().setUrgent(hktvInfo.getUrgent());
        productMasterDto.setBuToSend(List.of(ProductMasterBusinessUnitType.HKTV));

        if (BooleanUtils.isTrue(baseProduct.getAdditional().getHktv().getReplicateToOtherVariantsSkus())) {
            productMasterDto.getAdditional().getHktv().setVariantProductPhoto(baseProduct.getAdditional().getHktv().getVariantProductPhoto());
        }
    }

    private void createVariantProductRecordRowDo(SaveProductRecordDo productRecord, SingleEditProductDto productRequestDto, Long parentRowId) {
        SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
        saveProductRecordRowDo.setRecordId(productRecord.getId());
        saveProductRecordRowDo.setSku(productRequestDto.getProduct().getSkuId());
        saveProductRecordRowDo.setUuid(productRequestDto.getProduct().getUuid());
        saveProductRecordRowDo.setStatus(SaveProductStatus.PROCESSING);
        saveProductRecordRowDo.setParentProductRowId(parentRowId);
		saveProductRecordRowDo.setNotSyncHybris(true);
        if (productRequestDto.getProduct().getUuid() != null) {
            saveProductRecordRowDo.setUuid(productRequestDto.getProduct().getUuid());
        }
        saveProductRecordRowDo = saveProductRecordRowRepository.save(saveProductRecordRowDo);
        productRequestDto.getProduct().setRecordRowId(saveProductRecordRowDo.getId());
        saveProductRecordRowDo.setContent(gson.toJson(productRequestDto));
    }

	public Pair<Boolean, String> variantProductCheckAndUpdateRecordRows(Pair<List<ProductMasterResultDto>, List<ProductMasterResultDto>> productMasterResultPair, List<ProductMasterDto> variantProductsFromUser, SaveProductRecordDo saveProductRecordDo) {
		boolean isSingleEditProduct = SaveProductType.SINGLE_EDIT_PRODUCT_TYPE_SET.contains(saveProductRecordDo.getUploadType());
		List<ProductMasterResultDto> variantProductsFromProductMaster = productMasterResultPair.getRight();

		//filter primary sku data
		AtomicReference<ProductMasterResultDto> primarySkuFromProductMaster = new AtomicReference<>();
		AtomicReference<ProductMasterDto> primarySkuFromEditing = new AtomicReference<>();
		AtomicInteger countPrimarySku = new AtomicInteger();
		AtomicBoolean isPrimarySkuChange = new AtomicBoolean(false);
		variantProductsFromProductMaster.stream()
			.filter(data -> ConstantType.CONSTANT_YES.equals(data.getAdditional().getHktv().getIsPrimarySku()))
			.forEach(data -> {
				countPrimarySku.getAndIncrement();
				primarySkuFromProductMaster.set(data);
			});
		variantProductsFromUser.stream()
			.filter(data -> ConstantType.CONSTANT_YES.equals(data.getAdditional().getHktv().getIsPrimarySku()))
			.forEach(data -> {
				countPrimarySku.getAndIncrement();
				primarySkuFromEditing.set(data);

				if (primarySkuFromProductMaster.get() != null && !primarySkuFromProductMaster.get().getSkuId().equals(data.getSkuId())) {
					isPrimarySkuChange.set(true);
				}

			});

		//checking
		String errorMessage = checkPrimarySku(isSingleEditProduct, countPrimarySku.get(),
			primarySkuFromProductMaster.get(), isPrimarySkuChange.get());
		if (errorMessage == null) {
			errorMessage = checkVariantGlobalField(variantProductsFromUser, primarySkuFromEditing.get(), primarySkuFromProductMaster.get());
		}

		//if any error, all variant product failed
		if (errorMessage != null) {
			for (ProductMasterDto product : variantProductsFromUser) {
				saveProductRecordRowRepository.updateFailRowByRecordRowId(product.getRecordRowId(), errorMessage);
			}
			return Pair.of(false, errorMessage);
		}
		return Pair.of(true, null);
	}

	private String checkPrimarySku(boolean isSingleEditProduct, int countPrimarySku, ProductMasterResultDto primarySkuFromProductMaster,
								   boolean isPrimarySkuChange) {
		String errorMessage = null;

		//there must be 1 primary sku
		if (countPrimarySku == 0) {
			errorMessage = messageSource.getMessage("message34", null, null);

		//single edit allow update primary sku
		} else if (isSingleEditProduct) {
			if ((countPrimarySku == 2 && primarySkuFromProductMaster == null) ||
				countPrimarySku > 2) {
				errorMessage = messageSource.getMessage("message126", null, null);
			}
		} else {

			//if not single edit, there can only be 1 primary sku
			if (countPrimarySku > 1) {
				errorMessage = messageSource.getMessage("message126", null, null);

			//if not single edit, primary sku cannot be changed
			} else if (isPrimarySkuChange) {
				errorMessage = messageSource.getMessage("message24", null, null);
			}
		}

		return errorMessage;
	}

	String checkVariantGlobalField(List<ProductMasterDto> variantProductsFromUser, ProductMasterDto primarySkuFromEditing, ProductMasterResultDto primarySkuFromProductMaster) {
		String errorMessage = null;

		//all edit product should have same global field
		if (variantProductsFromUser.size() > 1) {
			SyncVariantCheckDto checkDto = SyncVariantCheckDataMapper.INSTANCE.toSyncVariantCheckDto(variantProductsFromUser.get(0));
			for (int i = 1; i < variantProductsFromUser.size(); i++) {
				if (!checkDto.equals(SyncVariantCheckDataMapper.INSTANCE.toSyncVariantCheckDto(variantProductsFromUser.get(i)))) {
					errorMessage = messageSource.getMessage("message339", new String[]{SyncVariantCheckDto.FIELD_NAMES.toString()}, null);
					break;
				}
			}
		}

		//if only editing non-primary sku, non-primary sku should have the same global field with primary sku from Product Master
		if (errorMessage == null && primarySkuFromEditing == null && primarySkuFromProductMaster != null) {
			SyncVariantCheckDto checkDto = SyncVariantCheckDataMapper.INSTANCE.toSyncVariantCheckDto(primarySkuFromProductMaster);
			for (ProductMasterDto product : variantProductsFromUser) {
				if (!checkDto.equals(SyncVariantCheckDataMapper.INSTANCE.toSyncVariantCheckDto(product))) {
					errorMessage = messageSource.getMessage("message339", new String[]{SyncVariantCheckDto.FIELD_NAMES.toString()}, null);
					break;
				}
			}
		}

		return errorMessage;
	}

	@Transactional
	public boolean handlePrimarySkuAndAddVariantProduct(Pair<List<ProductMasterResultDto>, List<ProductMasterResultDto>> productMasterResultPair, List<ProductMasterDto> variantProductsFromUser, SaveProductRecordDo saveProductRecordDo) {
		boolean isPrimarySkuChange = false;
		List<SingleEditProductDto> primaryRecordRows = new ArrayList<>();
		List<ProductMasterResultDto> editingProductFromProductMaster = productMasterResultPair.getLeft();
		List<ProductMasterResultDto> variantProductsWithSameProductCodeList = productMasterResultPair.getRight();
		Map<String, ProductMasterResultDto> skuIdToProductMasterDataMap = editingProductFromProductMaster.stream()
			.collect(Collectors.toMap(ProductMasterProductDto::getSkuId, Function.identity()));

		//single edit's isPrimarySku flag: if N to Y, change other variant's Y to N
		boolean isSingleEditProduct = SaveProductType.SINGLE_EDIT_PRODUCT_TYPE_SET.contains(saveProductRecordDo.getUploadType());
		if (isSingleEditProduct) {
			if (ConstantType.CONSTANT_YES.equalsIgnoreCase(variantProductsFromUser.get(0).getAdditional().getHktv().getIsPrimarySku())) {
				Pair<Boolean, SingleEditProductDto> resultPair = checkPrimarySkuChangedAndCreateRecordRow(variantProductsFromUser.get(0), variantProductsWithSameProductCodeList, saveProductRecordDo);
				isPrimarySkuChange = resultPair.getLeft();
				if (resultPair.getRight() != null) {
					primaryRecordRows.add(resultPair.getRight());
				}
			}
		}

		//create/update record row depend on condition
		for (ProductMasterDto product : variantProductsFromUser) {
			SingleEditProductDto singleEditProduct = checkOfflineRollbackUpdateRecordRow(product, skuIdToProductMasterDataMap.get(product.getSkuId()));
			if (singleEditProduct != null) {
				primaryRecordRows.add(singleEditProduct);
			}
		}

		//create need to update variant record row and handle record row relation
		Pair<Boolean, List<SingleEditProductDto>> variantRecordRowPair = createVariantProductRecordRow(variantProductsFromUser, skuIdToProductMasterDataMap, variantProductsWithSameProductCodeList, saveProductRecordDo);
		if (SaveProductType.CHECK_BATCH_VARIANT_CREATE_HKTV_PRODUCT_TYPE_SET.contains(saveProductRecordDo.getUploadType())) {
			createBatchCreateSaveProductRecordRowRelation(saveProductRecordDo, variantProductsFromUser);
		} else {
			createSaveProductRecordRowRelation(saveProductRecordDo, primaryRecordRows, variantRecordRowPair, isPrimarySkuChange);
		}


		boolean syncVariant = variantRecordRowPair.getLeft();
		if (syncVariant || isPrimarySkuChange) {
			log.info("product id {} variant records created. primary sku change : {}, variant field change : {}, variant product size : {}",
				variantProductsFromUser.get(0).getProductId(), isPrimarySkuChange, syncVariant, primaryRecordRows.size() + variantProductsWithSameProductCodeList.size());
		}
		return syncVariant;
	}

	private void createSaveProductRecordRowRelation(SaveProductRecordDo saveProductRecordDo, List<SingleEditProductDto> primaryRecordRowIds, Pair<Boolean, List<SingleEditProductDto>> variantRecordRowPair, boolean isPrimarySkuChange) {
		List<String> dependType = new ArrayList<>();
		if (variantRecordRowPair.getLeft()) {
			dependType.add(SaveProductRecordRowRelationDo.DEPEND_TYPE_FIELD);
		}
		if (isPrimarySkuChange) {
			dependType.add(SaveProductRecordRowRelationDo.DEPEND_TYPE_PRIMARY);
		}

		if (CollectionUtil.isEmpty(dependType)) {
			return;
		}

		List<SingleEditProductDto> allCreatedRecord = new ArrayList<>(primaryRecordRowIds);
		allCreatedRecord.addAll(variantRecordRowPair.getRight());
		List<SaveProductRecordRowRelationDo> saveProductRecordRowRelationDoList = new ArrayList<>();

		//all record rows are depend on other variant record row to success
		for (SingleEditProductDto singleEditProductDto1 : allCreatedRecord) {
			for (SingleEditProductDto singleEditProductDto2 : allCreatedRecord) {
				Long recordRowId1 = singleEditProductDto1.getProduct().getRecordRowId();
				Long recordRowId2 = singleEditProductDto2.getProduct().getRecordRowId();
				if (!Objects.equals(recordRowId1, recordRowId2)) {
					saveProductRecordRowRelationDoList.add(new SaveProductRecordRowRelationDo(saveProductRecordDo, singleEditProductDto1, recordRowId2, String.join(StringUtil.COMMA, dependType)));
				}
			}
		}

		if(!saveProductRecordRowRelationDoList.isEmpty()) {
			saveProductRecordRowRelationRepository.saveAll(saveProductRecordRowRelationDoList);
		}
	}

	private void createBatchCreateSaveProductRecordRowRelation(SaveProductRecordDo saveProductRecordDo, List<ProductMasterDto> variantProductsFromUser) {
		if (CollectionUtil.isEmpty(variantProductsFromUser)) {
			return;
		}

		ProductMasterDto primarySku = null;
		List<ProductMasterDto> variantSkus = new ArrayList<>();
		for (ProductMasterDto product : variantProductsFromUser) {
			if (ConstantType.CONSTANT_YES.equalsIgnoreCase(product.getAdditional().getHktv().getIsPrimarySku())) {
				primarySku = product;
			} else {
				variantSkus.add(product);
			}
		}

		if (primarySku == null || CollectionUtil.isEmpty(variantSkus)) {
			return;
		}

		List<String> dependType = new ArrayList<>();
		dependType.add(SaveProductRecordRowRelationDo.DEPEND_TYPE_CREATE);
		List<SaveProductRecordRowRelationDo> saveProductRecordRowRelationDoList = new ArrayList<>();

		//all record variant rows are depend on primary sku record row to success
		for (ProductMasterDto variantSku : variantSkus) {
			SaveProductRecordRowRelationDo recordRowRelationDo = new SaveProductRecordRowRelationDo();
			recordRowRelationDo.setRecordId(saveProductRecordDo.getId());
			recordRowRelationDo.setRecordRowId(variantSku.getRecordRowId());
			recordRowRelationDo.setStoreSkuId(variantSku.getAdditional().getHktv().getStoreSkuId());
			recordRowRelationDo.setDependRecordRowId(primarySku.getRecordRowId());
			recordRowRelationDo.setDependType(String.join(StringUtil.COMMA, dependType));
			recordRowRelationDo.setCreatedBy(saveProductRecordDo.getUploadUserId().toString());
			saveProductRecordRowRelationDoList.add(recordRowRelationDo);
		}

		if (CollectionUtil.isNotEmpty(saveProductRecordRowRelationDoList)) {
			saveProductRecordRowRelationRepository.saveAll(saveProductRecordRowRelationDoList);
		}
	}

	private SingleEditProductDto checkOfflineRollbackUpdateRecordRow(ProductMasterDto baseProduct, ProductMasterResultDto baseProductFromProductMaster) {
		SaveProductRecordRowDo row = saveProductRecordRowRepository.findById(baseProduct.getRecordRowId()).orElse(null);
		if (row == null) {
			return null;
		}

		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);

		if (baseProductFromProductMaster == null) {
			return singleEditProductDto;
		}

		//MS-3606
		Boolean isOfflineDueToRollback = baseProductFromProductMaster.getAdditional().getHktv().getOfflineDueToRollback();
		if (Objects.nonNull(isOfflineDueToRollback) && Boolean.TRUE.equals(isOfflineDueToRollback)) {
			log.info("processing of off line due to roll back, product sku uuid : [{}] row id : [{}]", baseProductFromProductMaster.getUuid(), baseProduct.getRecordRowId());
			singleEditProductDto.getProduct().setUuid(baseProductFromProductMaster.getUuid());
		}

		row.setContent(gson.toJson(singleEditProductDto));
		saveProductRecordRowRepository.save(row);
		return singleEditProductDto;
	}

	public Pair<List<ProductMasterResultDto>, List<ProductMasterResultDto>> findVariantProductsFromProductMaster(UserDto userDto, List<ProductMasterDto> variantProducts) {
		//find all variant uuid
		ProductSearchUuidRequestDto productSearchUuidRequestDto = ProductSearchUuidRequestDto
			.generateHktvNotBundleRequestDto(variantProducts.get(0).getProductId(), List.of(variantProducts.get(0).getAdditional().getHktv().getStores()));
		ProductMasterBaseResponseDto<List<String>> resultDto = productMasterHelper.requestProductUuidsByParams(userDto, productSearchUuidRequestDto);
		List<ProductMasterResultDto> productFromUserEdit = new ArrayList<>();
		List<ProductMasterResultDto> otherVariants = new ArrayList<>();
		if (resultDto == null || !StatusCodeEnum.SUCCESS.name().equals(resultDto.getStatus())) {
			return Pair.of(productFromUserEdit, otherVariants);
		}

		//get product detail
		Set<String> editSkuIds = variantProducts.stream().map(ProductMasterProductDto::getSkuId).collect(Collectors.toSet());
		List<ProductMasterResultDto> allVariantSkus = productMasterHelper.requestProductsByUuid(userDto, ProductMasterSearchRequestDto.builder().uuids(resultDto.getData()).build());
		for (ProductMasterResultDto productMasterResultDto : allVariantSkus) {
			Boolean isOfflineDueToRollback = productMasterResultDto.getAdditional().getHktv().getOfflineDueToRollback();
			if (editSkuIds.contains(productMasterResultDto.getSkuId())) {
				productFromUserEdit.add(productMasterResultDto);

			// when OfflineDueToRollback is true that means product status is in rollback and no need to add into product list
			} else if ((Objects.isNull(isOfflineDueToRollback) || Boolean.FALSE.equals(isOfflineDueToRollback))) {
				otherVariants.add(productMasterResultDto);
			}
		}
		return Pair.of(productFromUserEdit, otherVariants);
	}

	public Pair<Boolean, SingleEditProductDto> checkPrimarySkuChangedAndCreateRecordRow(ProductMasterDto baseProduct, List<ProductMasterResultDto> variantProductsWithSameProductCode, SaveProductRecordDo saveProductRecordDo) {
		boolean primarySkuChange = false;
		SingleEditProductDto newVariant = null;
		List<ProductMasterResultDto> primaryProductList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(variantProductsWithSameProductCode)) {
			primaryProductList = filterPrimaryProductList(variantProductsWithSameProductCode);
		}
		if (CollectionUtil.isNotEmpty(primaryProductList)) {
			SingleEditProductDto singleEditProductDto = convertVariantProductFromProductMasterResultDto(saveProductRecordDo, baseProduct, primaryProductList.get(0));
			singleEditProductDto.getProduct().getAdditional().getHktv().setIsPrimarySku(ConstantType.CONSTANT_NO);
			saveProductRecordRowHelper.createProductRecordRowDo(saveProductRecordDo.getId(), singleEditProductDto, SaveProductStatus.PROCESSING, null);
			primarySkuChange = true;
			newVariant = singleEditProductDto;
			// 如果primary sku是有改變的，就將原本的primary sku加進當前的product record一起處理並將其從variant product record移除
			variantProductsWithSameProductCode.remove(primaryProductList.get(0));
		}

		return Pair.of(primarySkuChange, newVariant);
	}

	public List<ProductMasterResultDto> filterPrimaryProductList(List<ProductMasterResultDto> products) {
		return products.stream()
				.filter(product -> product.getAdditional().getHktv() != null && "Y".equalsIgnoreCase(product.getAdditional().getHktv().getIsPrimarySku()))
				.collect(Collectors.toList());
	}

	public SingleEditProductDto convertVariantProductFromProductMasterResultDto(SaveProductRecordDo record, ProductMasterDto baseProduct, ProductMasterResultDto variantProduct) {
		ProductMasterDto productMasterDto = gson.fromJson(gson.toJson(ProductMasterDto.convertFromProductMasterResultDto(variantProduct), ProductMasterDto.class), ProductMasterDto.class);
		setHktvValueToSync(baseProduct, productMasterDto);

		SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
		singleEditProductDto.setProduct(productMasterDto);

		switch (record.getUploadType()) {
			case SaveProductType.SINGLE_CREATE_PRODUCT:
			case SaveProductType.SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT:
			case SaveProductType.SINGLE_EDIT_PRODUCT:
				productPreProcessingHelper.addHktvData(record.getUploadType(), singleEditProductDto, variantProduct);
				break;
		}

		return singleEditProductDto;
	}

}
