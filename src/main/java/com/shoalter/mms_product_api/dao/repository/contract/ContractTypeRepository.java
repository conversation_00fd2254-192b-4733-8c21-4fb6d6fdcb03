package com.shoalter.mms_product_api.dao.repository.contract;

import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractTypeDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ContractTypeRepository extends JpaRepository<ContractTypeDo, Integer> {

    @Query(value = "SELECT ct.* " +
            "FROM CONTRACT_TYPE ct " +
            "INNER JOIN CONTRACT c " +
            "ON ct.ID = c.CONTRACT_TYPE_ID " +
            "WHERE c.ID = :contractId", nativeQuery = true)
    ContractTypeDo findByContractId(Integer contractId);

    @Query(value =
            "SELECT ct.PRODUCT_READY_METHOD " +
            "FROM CONTRACT_PROD_TERMS ct "+
            "INNER JOIN CONTRACT c ON ct.CONTRACT_ID = c.ID "+
            "WHERE c.ID = ( " +
            "SELECT MAX(cc.ID) "+
            "FROM CONTRACT cc "+
            "INNER JOIN CONTRACT_TYPE ct ON cc.CONTRACT_TYPE_ID = ct.ID "+
            "WHERE cc.ID = :contractId OR cc.MASTER_CONTRACT_ID = :contractId AND ct.CODE != 'PC' )",nativeQuery = true)
    List<String> findLatestProductReadyMethodByContractId(Integer contractId);
}
