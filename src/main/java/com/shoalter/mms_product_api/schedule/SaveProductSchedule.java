package com.shoalter.mms_product_api.schedule;

import com.shoalter.mms_product_api.asynctask.CheckRecordProductMasterTask;
import com.shoalter.mms_product_api.asynctask.CheckRecordProductTask;
import com.shoalter.mms_product_api.asynctask.CheckRequestPMRecordProductTask;
import com.shoalter.mms_product_api.config.product.SaveProductProtocol;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class SaveProductSchedule {

	private final Executor checkRecordProductTaskExecutor;
	private final Executor checkQueueProtocolRecordProductTaskExecutor;
	private final Executor checkRequestPMRecordProductTaskExecutor;
	private final Executor checkRecordProductMasterTaskExecutor;

	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;

	private final CheckRecordProductTask checkRecordProductTask;
	private final CheckRecordProductMasterTask checkRecordProductMasterTask;
	private final CheckRequestPMRecordProductTask checkRequestPMRecordProductTask;

	@Scheduled(initialDelay = 1000, fixedRate = 1000)
	public void checkRecord() {
		int queryTaskCount = getQueryTaskCount(checkRecordProductTaskExecutor);
		if (queryTaskCount <= 0) {
			return;
		}

		List<Long> recordId = saveProductRecordRepository.findRecordIdByStatusAndCheckTime(
			SaveProductStatus.PROCESSING, 120, queryTaskCount);

		if (CollectionUtils.isEmpty(recordId)) {
			return;
		}

		String checkId = UUID.randomUUID().toString();
		saveProductRecordRepository.updateCheckId(checkId, SaveProductStatus.PROCESSING, 120, recordId);

		List<SaveProductRecordDo> recordList = saveProductRecordRepository.findByCheckId(checkId);
		if (recordList.isEmpty()) {
			return;
		}
		log.info("Start Processing PROCESSING Records : {}",
			recordList.stream().map(SaveProductRecordDo::getId).collect(Collectors.toList()));
		for (SaveProductRecordDo record : recordList) {
			record.setStatus(SaveProductStatus.CHECKING_PRODUCT);
			record.setCheckTime(null);
			saveProductRecordRowRepository.updateRowStatus(record.getId(), SaveProductStatus.PROCESSING,
				SaveProductStatus.CHECKING_PRODUCT);
			saveProductRecordRepository.save(record);
		}
	}

	@Scheduled(initialDelay = 1000, fixedRate = 3000)
	public void checkHttpProtocolRecordProduct() {
		int queryTaskCount = getQueryTaskCount(checkRecordProductTaskExecutor);
		if (queryTaskCount <= 0) {
			return;
		}

		List<Long> hktvRecordIds = getHktvHttpCheckingProductRecordId(queryTaskCount);
		if (CollectionUtils.isNotEmpty(hktvRecordIds)) {
			log.info("HKTV HTTP Checking Product Record IDs: {}", hktvRecordIds);
		}

		List<Long> littleMallRecordIds = getLittleMallHttpCheckingProductRecordId(queryTaskCount);
		if (CollectionUtils.isNotEmpty(littleMallRecordIds)) {
			log.info("LittleMall HTTP Checking Product Record IDs: {}", littleMallRecordIds);
		}

		List<Long> recordIds = new ArrayList<>(
			CollectionUtils.union(hktvRecordIds, littleMallRecordIds));

		if (CollectionUtils.isEmpty(recordIds)) {
			return;
		}

		String checkId = UUID.randomUUID().toString();
		saveProductRecordRepository.updateCheckId(checkId, SaveProductStatus.CHECKING_PRODUCT, 600,
			recordIds);

		List<SaveProductRecordDo> recordList = saveProductRecordRepository.findByCheckId(checkId);

		if (recordList.isEmpty()) {
			return;
		}

		log.info("Start Processing HTTP protocol CHECKING_PRODUCT Records : {}",
			recordList.stream().map(SaveProductRecordDo::getId).collect(Collectors.toList()));
		for (SaveProductRecordDo record : recordList) {
			log.info("Processing record: {}, check time: {}", record.getId(), record.getCheckTime());
			checkRecordProductTask.startWithCheckRecordProductTaskExecutor(record);
		}
	}

	@Scheduled(initialDelay = 1000, fixedRate = 3000)
	public void checkQueueProtocolRecordProduct() {
		int queryTaskCount = getQueryTaskCount(checkQueueProtocolRecordProductTaskExecutor);
		if (queryTaskCount <= 0) {
			return;
		}

		String checkId = UUID.randomUUID().toString();
		List<Long> recordId = saveProductRecordRepository.findRecordIdByStatusAndProtocolAndCheckTime(
			SaveProductStatus.CHECKING_PRODUCT, SaveProductProtocol.QUEUE, 600, queryTaskCount);
		saveProductRecordRepository.updateCheckId(checkId, SaveProductStatus.CHECKING_PRODUCT, 600,
			recordId);
		List<SaveProductRecordDo> recordList = saveProductRecordRepository.findByCheckId(checkId);
		if (recordList.isEmpty()) {
			return;
		}
		log.info("Start Processing QUEUE protocol CHECKING_PRODUCT Records : {}",
			recordList.stream().map(SaveProductRecordDo::getId).collect(Collectors.toList()));
		for (SaveProductRecordDo record : recordList) {
			log.info("Processing record: {}, check time: {}", record.getId(), record.getCheckTime());
			checkRecordProductTask.startWithCheckQueueProtocolRecordProductTaskExecutor(record);
		}
	}

	@Scheduled(initialDelay = 1000, fixedRate = 3000)
	public void checkRequestPMRecordProduct() {
		int queryTaskCount = getQueryTaskCount(checkRequestPMRecordProductTaskExecutor);
		if (queryTaskCount <= 0) {
			return;
		}

		List<Long> recordId = saveProductRecordRepository.findRecordIdByStatusAndCheckTime(
			SaveProductStatus.REQUESTING_PM, 600, queryTaskCount);

		if (CollectionUtils.isEmpty(recordId)) {
			return;
		}

		String checkId = UUID.randomUUID().toString();
		saveProductRecordRepository.updateCheckId(checkId, SaveProductStatus.REQUESTING_PM, 600, recordId);
		List<SaveProductRecordDo> recordList = saveProductRecordRepository.findByCheckId(checkId);
		if (recordList.isEmpty()) {
			return;
		}
		log.info("Start Processing REQUESTING_PM Records : {}",
			recordList.stream().map(SaveProductRecordDo::getId).collect(Collectors.toList()));
		for (SaveProductRecordDo record : recordList) {
			checkRequestPMRecordProductTask.start(record);
		}
	}

	@Scheduled(initialDelay = 1000, fixedRate = 1000)
	public void checkRecordProductMaster() {
		int queryTaskCount = getQueryTaskCount(checkRecordProductMasterTaskExecutor);
		if (queryTaskCount <= 0) {
			return;
		}

		String checkId = UUID.randomUUID().toString();
		List<Long> recordId = saveProductRecordRepository.findRecordIdByStatusAndCheckTime(
			SaveProductStatus.CHECKING_PM, 30, queryTaskCount);
		saveProductRecordRepository.updateCheckId(checkId, SaveProductStatus.CHECKING_PM, 30, recordId);
		List<SaveProductRecordDo> recordList = saveProductRecordRepository.findByCheckId(checkId);
		if (recordList.isEmpty()) {
			return;
		}
		log.info("Start Processing CHECKING_PM Records : {}",
			recordList.stream().map(SaveProductRecordDo::getId).collect(Collectors.toList()));
		for (SaveProductRecordDo record : recordList) {
			checkRecordProductMasterTask.start(record);
		}
	}

	private int getQueryTaskCount(Executor checkQueueProtocolRecordProductTaskExecutor) {
		ThreadPoolTaskExecutor threadPoolTaskExecutor = (ThreadPoolTaskExecutor) checkQueueProtocolRecordProductTaskExecutor;
		int corePoolSize = threadPoolTaskExecutor.getCorePoolSize();
		int activeCount = threadPoolTaskExecutor.getActiveCount();
		return (corePoolSize - activeCount) * 4;
	}

	private List<Long> getHktvHttpCheckingProductRecordId(int queryTaskCount) {
		return getHttpCheckingProductRecordIds(queryTaskCount,
			SaveProductType.HKTV_MALL_TYPE_SET_FOR_CHECK_RECORD);
	}

	private List<Long> getLittleMallHttpCheckingProductRecordId(int queryTaskCount) {
		return getHttpCheckingProductRecordIds(queryTaskCount,
			SaveProductType.LITTLE_MALL_TYPE_SET_FOR_CHECK_RECORD);
	}

	private List<Long> getHttpCheckingProductRecordIds(int queryTaskCount,
													   Collection<Integer> uploadTypes) {
		return saveProductRecordRepository.findRecordIdByStatusAndProtocolAndCheckTimeAndUploadType(
			SaveProductStatus.CHECKING_PRODUCT, SaveProductProtocol.HTTP, 600, queryTaskCount,
			uploadTypes);
	}
}
