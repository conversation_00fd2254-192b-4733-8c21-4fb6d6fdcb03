package com.shoalter.mms_product_api.service.bundle.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.service.product.pojo.CategoryInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class HktvBundleDto{
    // hktv 特有的產品資料
    @JsonProperty("contract_no")
	private Integer contractNo;
    private String stores;
    @JsonProperty("product_ready_method")
	private String productReadyMethod;
    @JsonProperty("delivery_method")
	private String deliveryMethod;
    @Schema(hidden = true)
    @JsonProperty("store_sku_id")
	private String storeSkuId;
    @JsonProperty("product_type_info")
	private List<CategoryInfoDto> productTypeInfo;
    @JsonProperty("primary_category_info")
	private CategoryInfoDto primaryCategoryInfo;
    @JsonProperty("is_primary_sku")
	private String isPrimarySku;
    private String visibility;
    @JsonProperty("sku_short_description_en")
	private String skuShortDescriptionEn;
    @JsonProperty("sku_short_description_ch")
	private String skuShortDescriptionCh;
	@JsonProperty("sku_short_description_sc")
	private String skuShortDescriptionSc;
    @JsonProperty("sku_long_description_en")
	private String skuLongDescriptionEn;
    @JsonProperty("sku_long_description_ch")
	private String skuLongDescriptionCh;
	@JsonProperty("sku_long_description_sc")
	private String skuLongDescriptionSc;
    @JsonProperty("feature_start_time")
    private String featureStartTime;
    @JsonProperty("feature_end_time")
    private String featureEndTime;
    @JsonProperty("voucher_type")
    private String voucherType;
    @JsonProperty("voucher_display_type")
    private String voucherDisplayType;
    @JsonProperty("expiry_type")
    private String expiryType;
    @JsonProperty("redeem_start_date")
    private String redeemStartDate;
    @JsonProperty("voucher_template_type")
    private String voucherTemplateType;
    @JsonProperty("fixed_redemption_date")
    private String fixedRedemptionDate;
    @JsonProperty("upon_purchase_date")
    private Integer uponPurchaseDate;
    @JsonProperty("fine_print_en")
    private String finePrintEn;
    @JsonProperty("fine_print_ch")
    private String finePrintCh;
	@JsonProperty("fine_print_sc")
	private String finePrintSc;
    @JsonProperty("term_name")
    private String termName;
    private String currency;
    private BigDecimal cost;
    @JsonProperty("selling_price")
    private BigDecimal sellingPrice;
    private String style;
    @JsonProperty("discount_text_en")
    private String discountTextEn;
    @JsonProperty("discount_text_ch")
    private String discountTextCh;
	@JsonProperty("discount_text_sc")
	private String discountTextSc;
    @JsonProperty("mall_dollar")
    private BigDecimal mallDollar;
    @JsonProperty("vip_mall_dollar")
    private BigDecimal vipMallDollar;
    @JsonProperty("user_max")
    private Long userMax;
    @JsonProperty("main_photo")
    private String mainPhoto;
    @JsonProperty("main_video")
    private String mainVideo;
    @JsonProperty("variant_product_photo")
    private List<String> variantProductPhoto;
    @JsonProperty("other_photo")
    private List<String> otherPhoto;
    @JsonProperty("advertising_photo")
    private String advertisingPhoto;
    @JsonProperty("video_link")
    private String videoLink;
    @JsonProperty("video_link_text_en")
    private String videoLinkTextEn;
    @JsonProperty("video_link_text_ch")
    private String videoLinkTextCh;
	@JsonProperty("video_link_text_sc")
	private String videoLinkTextSc;
	@JsonProperty("video_link2")
	private String videoLink2;
	@JsonProperty("video_link_text_en2")
	private String videoLinkTextEn2;
	@JsonProperty("video_link_text_ch2")
	private String videoLinkTextCh2;
	@JsonProperty("video_link_text_sc2")
	private String videoLinkTextSc2;
	@JsonProperty("video_link3")
	private String videoLink3;
	@JsonProperty("video_link_text_en3")
	private String videoLinkTextEn3;
	@JsonProperty("video_link_text_ch3")
	private String videoLinkTextCh3;
	@JsonProperty("video_link_text_sc3")
	private String videoLinkTextSc3;
	@JsonProperty("video_link4")
	private String videoLink4;
	@JsonProperty("video_link_text_en4")
	private String videoLinkTextEn4;
	@JsonProperty("video_link_text_ch4")
	private String videoLinkTextCh4;
	@JsonProperty("video_link_text_sc4")
	private String videoLinkTextSc4;
	@JsonProperty("video_link5")
	private String videoLink5;
	@JsonProperty("video_link_text_en5")
	private String videoLinkTextEn5;
	@JsonProperty("video_link_text_ch5")
	private String videoLinkTextCh5;
	@JsonProperty("video_link_text_sc5")
	private String videoLinkTextSc5;
    @JsonProperty("warehouse_id")
    private Integer warehouseId;
	@JsonProperty("warehouse_code")
	//web request data = storefront store code + sequence number
	//example:"H123-98"
	private String warehouseCode;
    @JsonProperty("packing_spec_en")
    private String packingSpecEn;
    @JsonProperty("packing_spec_ch")
    private String packingSpecCh;
	@JsonProperty("packing_spec_sc")
	private String packingSpecSc;
    @JsonProperty("invoice_remarks_en")
    private String invoiceRemarksEn;
    @JsonProperty("invoice_remarks_ch")
    private String invoiceRemarksCh;
	@JsonProperty("invoice_remarks_sc")
	private String invoiceRemarksSc;
    @JsonProperty("return_days")
    private Integer returnDays;
    @JsonProperty("product_ready_days")
    private String productReadyDays;
    @JsonProperty("pickup_days")
    private String pickupDays;
    @JsonProperty("pickup_timeslot")
    private String pickupTimeslot;
    private String urgent;
    private String warranty;
    @JsonProperty("need_removal_services")
    private String needRemovalServices;
    @JsonProperty("goods_type")
    private String goodsType;
    @JsonProperty("warranty_period_unit")
    private String warrantyPeriodUnit;
    @JsonProperty("warranty_period")
    private Integer warrantyPeriod;
    @JsonProperty("warranty_supplier_en")
    private String warrantySupplierEn;
    @JsonProperty("warranty_supplier_ch")
    private String warrantySupplierCh;
	@JsonProperty("warranty_supplier_sc")
	private String warrantySupplierSc;
    @JsonProperty("service_centre_address_en")
    private String serviceCentreAddressEn;
    @JsonProperty("service_centre_address_ch")
    private String serviceCentreAddressCh;
	@JsonProperty("service_centre_address_sc")
	private String serviceCentreAddressSc;
    @JsonProperty("service_centre_email")
    private String serviceCentreEmail;
    @JsonProperty("service_centre_contact")
    private String serviceCentreContact;
    @JsonProperty("warranty_remark_en")
    private String warrantyRemarkEn;
    @JsonProperty("warranty_remark_ch")
    private String warrantyRemarkCh;
	@JsonProperty("warranty_remark_sc")
	private String warrantyRemarkSc;
    @JsonProperty("online_status")
    private OnlineStatusEnum onlineStatus;
    @JsonProperty("rm_code")
    private String rmCode;
    @JsonProperty("virtual_store")
    private String virtualStore;
	/**
	 * set to hybris replicateImage field,
	 * if true : hybris will sync primary/variant variant_product_photo with same images
	 * product master only save, unused
	 */
    @JsonProperty("replicate_to_other_variants_skus")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean replicateToOtherVariantsSkus;
    @JsonProperty("presell_fruit")
    private String preSellFruit;
    @JsonProperty("physical_store")
    private String physicalStore;
    @JsonProperty("thumbnail_video")
    private String thumbnailVideo;
    @JsonProperty("storage_type")
    private String storageType;
	@Schema(hidden = true)
    @JsonProperty("commission_rate")
    private BigDecimal commissionRate;
	@JsonProperty("store_id")
	private Integer storeId;
	@JsonProperty("video_filename")
	private String videoFilename;
	@JsonProperty("delivery_district")
	private List<String> deliveryDistrict;
    @Schema(hidden = true)
    @JsonProperty("status")
    private String status;
    @Schema(hidden = true)
    @JsonProperty("hasEditing")
    private Boolean hasEditing;

	public static HktvBundleDto convertFromHktvProductDto(HktvProductDto hktvProductDto){

		return HktvBundleDto.builder()
				.contractNo(hktvProductDto.getContractNo())
				.stores(hktvProductDto.getStores())
				.productReadyMethod(hktvProductDto.getProductReadyMethod())
				.deliveryMethod(hktvProductDto.getDeliveryMethod())
				.storeSkuId(hktvProductDto.getStoreSkuId())
				.isPrimarySku(hktvProductDto.getIsPrimarySku())
				.visibility(hktvProductDto.getVisibility())
				.skuShortDescriptionEn(hktvProductDto.getSkuShortDescriptionEn())
				.skuShortDescriptionCh(hktvProductDto.getSkuShortDescriptionCh())
				.skuShortDescriptionSc(hktvProductDto.getSkuShortDescriptionSc())
				.skuLongDescriptionEn(hktvProductDto.getSkuLongDescriptionEn())
				.skuLongDescriptionCh(hktvProductDto.getSkuLongDescriptionCh())
				.skuLongDescriptionSc(hktvProductDto.getSkuLongDescriptionSc())
				.featureStartTime(hktvProductDto.getFeatureStartTime())
				.featureEndTime(hktvProductDto.getFeatureEndTime())
				.voucherType(hktvProductDto.getVoucherType())
				.voucherDisplayType(hktvProductDto.getVoucherDisplayType())
				.expiryType(hktvProductDto.getExpiryType())
				.redeemStartDate(hktvProductDto.getRedeemStartDate())
				.voucherTemplateType(hktvProductDto.getVoucherTemplateType())
				.fixedRedemptionDate(hktvProductDto.getFixedRedemptionDate())
				.uponPurchaseDate(hktvProductDto.getUponPurchaseDate())
				.finePrintEn(hktvProductDto.getFinePrintEn())
				.finePrintCh(hktvProductDto.getFinePrintCh())
				.finePrintSc(hktvProductDto.getFinePrintSc())
				.termName(hktvProductDto.getTermName())
				.currency(hktvProductDto.getCurrency())
				.cost(hktvProductDto.getCost())
				.sellingPrice(hktvProductDto.getSellingPrice())
				.style(hktvProductDto.getStyle())
				.discountTextEn(hktvProductDto.getDiscountTextEn())
				.discountTextCh(hktvProductDto.getDiscountTextCh())
				.discountTextSc(hktvProductDto.getDiscountTextSc())
				.mallDollar(hktvProductDto.getMallDollar())
				.vipMallDollar(hktvProductDto.getMallDollar())
				.userMax(hktvProductDto.getUserMax())
				.mainPhoto(hktvProductDto.getMainPhoto())
				.mainVideo(hktvProductDto.getMainVideo())
				.variantProductPhoto(hktvProductDto.getVariantProductPhoto())
				.otherPhoto(hktvProductDto.getOtherPhoto())
				.advertisingPhoto(hktvProductDto.getAdvertisingPhoto())
				.videoLink(hktvProductDto.getVideoLink())
				.videoLinkTextEn(hktvProductDto.getVideoLinkTextEn())
				.videoLinkTextCh(hktvProductDto.getVideoLinkTextCh())
				.videoLinkTextSc(hktvProductDto.getVideoLinkTextSc())
				.videoLink2(hktvProductDto.getVideoLink2())
				.videoLinkTextEn2(hktvProductDto.getVideoLinkTextEn2())
				.videoLinkTextCh2(hktvProductDto.getVideoLinkTextCh2())
				.videoLinkTextSc2(hktvProductDto.getVideoLinkTextSc2())
				.videoLink3(hktvProductDto.getVideoLink3())
				.videoLinkTextEn3(hktvProductDto.getVideoLinkTextEn3())
				.videoLinkTextCh3(hktvProductDto.getVideoLinkTextCh3())
				.videoLinkTextSc3(hktvProductDto.getVideoLinkTextSc3())
				.videoLink4(hktvProductDto.getVideoLink4())
				.videoLinkTextEn4(hktvProductDto.getVideoLinkTextEn4())
				.videoLinkTextCh4(hktvProductDto.getVideoLinkTextCh4())
				.videoLinkTextSc4(hktvProductDto.getVideoLinkTextSc4())
				.videoLink5(hktvProductDto.getVideoLink5())
				.videoLinkTextEn5(hktvProductDto.getVideoLinkTextEn5())
				.videoLinkTextCh5(hktvProductDto.getVideoLinkTextCh5())
				.videoLinkTextSc5(hktvProductDto.getVideoLinkTextSc5())
				.warehouseId(hktvProductDto.getWarehouseId())
				.warehouseCode(hktvProductDto.getWarehouseCode())
				.packingSpecEn(hktvProductDto.getPackingSpecEn())
				.packingSpecCh(hktvProductDto.getPackingSpecCh())
				.packingSpecSc(hktvProductDto.getPackingSpecSc())
				.invoiceRemarksEn(hktvProductDto.getInvoiceRemarksEn())
				.invoiceRemarksCh(hktvProductDto.getInvoiceRemarksCh())
				.invoiceRemarksSc(hktvProductDto.getInvoiceRemarksSc())
				.returnDays(hktvProductDto.getReturnDays())
				.productReadyDays(hktvProductDto.getProductReadyDays())
				.pickupDays(hktvProductDto.getPickupDays())
				.pickupTimeslot(hktvProductDto.getPickupTimeslot())
				.urgent(hktvProductDto.getUrgent())
				.warranty(hktvProductDto.getWarranty())
				.needRemovalServices(hktvProductDto.getNeedRemovalServices())
				.goodsType(hktvProductDto.getGoodsType())
				.warrantyPeriodUnit(hktvProductDto.getWarrantyPeriodUnit())
				.warrantyPeriod(hktvProductDto.getWarrantyPeriod())
				.warrantySupplierEn(hktvProductDto.getWarrantySupplierEn())
				.warrantySupplierCh(hktvProductDto.getWarrantySupplierCh())
				.warrantySupplierSc(hktvProductDto.getWarrantySupplierSc())
				.serviceCentreAddressEn(hktvProductDto.getServiceCentreAddressEn())
				.serviceCentreAddressCh(hktvProductDto.getServiceCentreAddressCh())
				.serviceCentreAddressSc(hktvProductDto.getServiceCentreAddressSc())
				.serviceCentreEmail(hktvProductDto.getServiceCentreEmail())
				.serviceCentreContact(hktvProductDto.getServiceCentreContact())
				.warrantyRemarkEn(hktvProductDto.getWarrantyRemarkEn())
				.warrantyRemarkCh(hktvProductDto.getWarrantyRemarkCh())
				.warrantyRemarkSc(hktvProductDto.getWarrantyRemarkSc())
				.onlineStatus(hktvProductDto.getOnlineStatus())
				.rmCode(hktvProductDto.getRmCode())
				.virtualStore(hktvProductDto.getVirtualStore())
				.replicateToOtherVariantsSkus(hktvProductDto.getReplicateToOtherVariantsSkus())
				.preSellFruit(hktvProductDto.getPreSellFruit())
				.physicalStore(hktvProductDto.getPhysicalStore())
				.thumbnailVideo(hktvProductDto.getThumbnailVideo())
				.storageType(hktvProductDto.getStorageType())
				.commissionRate(hktvProductDto.getCommissionRate())
				.storeId(hktvProductDto.getStoreId())
				.videoFilename(hktvProductDto.getVideoFilename())
				.status(hktvProductDto.getStatus())
				.hasEditing(hktvProductDto.getHasEditing())
				.deliveryDistrict(hktvProductDto.getDeliveryDistrict())
				.build();
	}
}
