package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRelationRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowTempRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowRelationDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowTempDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleSingleCreateDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleSingleEditDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@RequiredArgsConstructor
@Service
public class SaveProductRecordRowHelper {

    private final SaveProductRecordRowRepository saveProductRecordRowRepository;
    private final SaveProductRecordRowRelationRepository saveProductRecordRowRelationRepository;
    private final SaveProductRecordRowTempRepository saveProductRecordRowTempRepository;

    private final JdbcTemplate jdbcTemplate;
    private final Gson gson;

    public SaveProductRecordRowDo createProductRecordRowDo(Long recordId, SingleEditProductDto productRequestDto, int status, String errorMessage) {
        SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
        saveProductRecordRowDo.setRecordId(recordId);
        saveProductRecordRowDo.setSku(productRequestDto.getProduct().getSkuId());
        saveProductRecordRowDo.setUuid(productRequestDto.getProduct().getUuid());
        saveProductRecordRowDo.setStatus(status);
        saveProductRecordRowDo = saveProductRecordRowRepository.save(saveProductRecordRowDo);
        productRequestDto.getProduct().setRecordRowId(saveProductRecordRowDo.getId());
        saveProductRecordRowDo.setContent(gson.toJson(productRequestDto));
        if (StringUtil.isNotEmpty(errorMessage)) {
            saveProductRecordRowDo.setErrorMessage(errorMessage);
        }
        return saveProductRecordRowRepository.save(saveProductRecordRowDo);
    }

	public SaveProductRecordRowDo createProductRecordRowDo(Long recordId, BundleSingleEditDto bundleSingleEditDto, int status, String errorMessage) {
		SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
		saveProductRecordRowDo.setRecordId(recordId);
		saveProductRecordRowDo.setSku(bundleSingleEditDto.getProduct().getSkuId());
		saveProductRecordRowDo.setUuid(bundleSingleEditDto.getProduct().getUuid());
		saveProductRecordRowDo.setStatus(status);
		saveProductRecordRowDo = saveProductRecordRowRepository.save(saveProductRecordRowDo);
		bundleSingleEditDto.getProduct().setRecordRowId(saveProductRecordRowDo.getId());
		saveProductRecordRowDo.setContent(gson.toJson(bundleSingleEditDto));
		if (StringUtil.isNotEmpty(errorMessage)) {
			saveProductRecordRowDo.setErrorMessage(errorMessage);
		}
		return saveProductRecordRowRepository.save(saveProductRecordRowDo);
	}

	public SaveProductRecordRowDo createProductRecordRowDo(Long recordId, BundleSingleCreateDto bundleSingleCreateDto, int status, String errorMessage) {
		SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
		saveProductRecordRowDo.setRecordId(recordId);
		saveProductRecordRowDo.setSku(bundleSingleCreateDto.getProduct().getSkuId());
		saveProductRecordRowDo.setStatus(status);
		saveProductRecordRowDo = saveProductRecordRowRepository.save(saveProductRecordRowDo);
		bundleSingleCreateDto.getProduct().setRecordRowId(saveProductRecordRowDo.getId());
		saveProductRecordRowDo.setContent(gson.toJson(bundleSingleCreateDto));
		if (StringUtil.isNotEmpty(errorMessage)) {
			saveProductRecordRowDo.setErrorMessage(errorMessage);
		}
		return saveProductRecordRowRepository.save(saveProductRecordRowDo);
	}

    public SaveProductRecordRowDo createProductRecordRowDo(Long recordId, BundleSingleEditDto productRequestDto) {
        SaveProductRecordRowDo saveProductRecordRowDo = createSaveProductRecordRowDo(recordId,
			productRequestDto.getProduct().getSkuId(), productRequestDto.getProduct().getUuid(), SaveProductStatus.PROCESSING);
		productRequestDto.getProduct().setRecordRowId(saveProductRecordRowDo.getId());
        saveProductRecordRowDo.setContent(gson.toJson(productRequestDto));
        return saveProductRecordRowRepository.save(saveProductRecordRowDo);
    }

    public SaveProductRecordRowDo createProductRecordRowDo(Long recordId, String skuId, SingleEditProductDto productRequestDto, int status) {
        SaveProductRecordRowDo saveProductRecordRowDo = createSaveProductRecordRowDo(recordId, skuId, productRequestDto.getProduct().getUuid(), status);
		productRequestDto.getProduct().setRecordRowId(saveProductRecordRowDo.getId());
        saveProductRecordRowDo.setContent(gson.toJson(productRequestDto));
        return saveProductRecordRowRepository.save(saveProductRecordRowDo);
    }

	public SaveProductRecordRowDo generateProductRecordRowDo(Long recordId, SingleEditProductDto productRequestDto, int recordRowStatus, String errorMessage) {
		SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
		saveProductRecordRowDo.setRecordId(recordId);
		saveProductRecordRowDo.setSku(productRequestDto.getProduct().getSkuId());
		saveProductRecordRowDo.setUuid(productRequestDto.getProduct().getUuid());
		saveProductRecordRowDo.setStatus(recordRowStatus);
		saveProductRecordRowDo.setContent(gson.toJson(productRequestDto));
		saveProductRecordRowDo.setErrorMessage(errorMessage);
		return saveProductRecordRowDo;
	}

	private SaveProductRecordRowDo createSaveProductRecordRowDo(Long recordId, String skuId, String uuid, int status) {
		SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
		saveProductRecordRowDo.setRecordId(recordId);
		saveProductRecordRowDo.setSku(skuId);
		saveProductRecordRowDo.setUuid(uuid);
		saveProductRecordRowDo.setStatus(status);
		return saveProductRecordRowRepository.save(saveProductRecordRowDo);
	}

    public void batchSaveSaveProductRecordRowDo(List<SaveProductRecordRowDo> rowList){
        int batchSize = 500;
        IntStream.range(0, (rowList.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> rowList.subList(i * batchSize, Math.min((i + 1) * batchSize, rowList.size())))
                .forEach(this::insertBatch);
    }

    private void insertBatch(List<SaveProductRecordRowDo> rowSubList) {
        String sql = "INSERT INTO mms.SAVE_PRODUCT_RECORD_ROW (RECORD_ID, SKU, UUID, STATUS, CONTENT, ERROR_MESSAGE, PARENT_PRODUCT_ROW_ID) VALUES(?, ?, ?, ?, ?, ?, ?)";

        jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setLong(1, rowSubList.get(i).getRecordId());
                ps.setString(2, rowSubList.get(i).getSku());
                ps.setString(3, rowSubList.get(i).getUuid());
                ps.setInt(4, rowSubList.get(i).getStatus());
                ps.setString(5, rowSubList.get(i).getContent());
                ps.setString(6, rowSubList.get(i).getErrorMessage());
                ps.setObject(7, rowSubList.get(i).getParentProductRowId());
            }

            @Override
            public int getBatchSize() {
                return rowSubList.size();
            }
        });
    }

    public Map<Long, Set<Long>> findDependRecordRowIdMap(SaveProductRecordDo saveProductRecordDo, Set<Long> recordRowIds) {
        List<SaveProductRecordRowRelationDo> relations = saveProductRecordRowRelationRepository.findByRecordIdAndRecordRowIdIn(saveProductRecordDo.getId(), recordRowIds);
        return relations.stream()
                .collect(Collectors.groupingBy(SaveProductRecordRowRelationDo::getRecordRowId,
                        Collectors.mapping(
                                SaveProductRecordRowRelationDo::getDependRecordRowId,
                                Collectors.toSet()
                        )));
    }

	@Retryable(value = NoDataException.class, backoff = @Backoff(delay = 200))
	public Optional<SaveProductRecordRowTempDo> findSaveProductRecordRowTempByRecordRowIdRetryable(Long recordRowId) {
		Optional<SaveProductRecordRowTempDo> saveProductRecordRowTempDo = saveProductRecordRowTempRepository.findByRecordRowId(recordRowId);
		if (saveProductRecordRowTempDo.isPresent()) {
			return saveProductRecordRowTempDo;
		}
		throw new NoDataException();
	}

	@Recover
	public Optional<SaveProductRecordRowTempDo> recover(NoDataException e, Long recordRowId) {
		log.error("cannot find save product record row temp by traceId {}", recordRowId);
		return Optional.empty();
	}
}
