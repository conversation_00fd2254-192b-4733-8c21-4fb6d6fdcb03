package com.shoalter.mms_product_api.config.product.template;

import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.service.product.template.TemplateInterface;
import lombok.Getter;

@Getter
public enum CommissionApprovalDealColumnEnum implements TemplateInterface<CommissionApprovalDealColumnEnum> {
	APPROVE_ID(0, "Approve ID", null, null, null),
	MERCHANT_NAME(1, "Merchant Name", null, null, null),
	STORE_CODE(2, "Store Code", null, null, null),
	SKU_ID(3, "SKU ID", ExcelValidationName.VALIDATION_OVERSEA_DELIVERY_ACTION, null, null),
	SKU_NAME_CH(4, "SKU Name(CH)", null, null, null),
	SKU_NAME_EN(5, "SKU Name(EN)", null, null, null),
	APPROVE_TYPE(6, "Approve Type", null, null, null),
	PRODUCT_TYPE_CODE(7, "Product Type Code", null, null, null),
	PRIMARY_CATEGORY_CODE(8, "Primary Category Code", null, null, null),
	BRAND_NAME(9, "Brand Name", null, null, null),
	STANDARD_COMMISSION_RATE(10, "Standard Commission Rate(%)", null, null, null),
	TIER_ONE_THRESHOLD(11, "Tier 1 Threshold", null, null, null),
	TIER_ONE_COMMISSION_RATE(12, "Tier 1 Commission Rate(%)", null, null, null),
	TIER_TWO_THRESHOLD(13, "Tier 2 Threshold", null, null, null),
	TIER_TWO_COMMISSION_RATE(14, "Tier 2 Commission Rate(%)", null, null, null),
	PACKING_BOX_TYPE(15, "Packing Box Type", null, null, null),
	APPROVE_PRODUCT_TYPE_CODE(16, "Approve - Product Type Code", null, null, null),
	APPROVE_PRIMARY_CATEGORY_CODE(17, "Approve - Primary Category Code", null, null, null),
	APPROVE_BRAND_NAME(18, "Approve - Brand Name", null, null, null),
	APPROVE_STANDARD_COMMISSION_RATE(19, "Approve - Standard Commission Rate(%)", null, null, null),
	APPROVE_TIER_ONE_THRESHOLD(20, "Approve - Tier 1 Threshold", null, null, null),
	APPROVE_TIER_ONE_COMMISSION_RATE(21, "Approve - Tier 1 Commission Rate(%)", null, null, null),
	APPROVE_TIER_TWO_THRESHOLD(22, "Approve - Tier 2 Threshold", null, null, null),
	APPROVE_TIER_TWO_COMMISSION_RATE(23, "Approve - Tier 2 Commission Rate(%)", null, null, null),
	APPROVE_PACKING_BOX_TYPE(24, "Approve - Packing Box Type", null, null, null),
	APPROVE_STATUS(25, "Approve Status", null, null, null),
	CREATE_TIME(26, "Create Time", null, null, null),
	LAST_UPDATE_TIME(27, "Last Update Time", null, null, null);



	private final Integer columnNumber;
	private final String columnName;
	private final String validationName;
	private final CommissionApprovalDealColumnEnum parent;
	private final String parentSegment;

	CommissionApprovalDealColumnEnum(Integer columnNumber, String columnName, String validationName, CommissionApprovalDealColumnEnum parent, String parentSegment) {
		this.columnNumber = columnNumber;
		this.columnName = columnName;
		this.validationName = validationName;
		this.parent = parent;
		this.parentSegment = parentSegment;
	}

	@Override
	public Integer getParentColumnNumber() {
		return parent.getColumnNumber();
	}
}
