package com.shoalter.mms_product_api.controller;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.LittleMallFlattenProductsService;
import com.shoalter.mms_product_api.service.product.LittleMallQueryFlattenStoresService;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallFlattenProductsRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallQueryFlattenStoresRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallQueryFlattenStoresResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Little_Mall Flatten API")
@RequiredArgsConstructor
@RestController
@PreAuthorize("hasRole('ROLE_MMS_ROLE')")
@Slf4j
@RequestMapping("little-mall/flatten")
public class LittleMallFlattenController {
    private final Gson gson;
	private final LittleMallQueryFlattenStoresService littleMallQueryFlattenStoresService;

//	@Operation(summary = "Query flatten little mall sku (Temporal Function)", description = "status 1代表成功-1代表失敗")
//	@PostMapping("/search")
//	public ResponseDto<LittleMallQueryFlattenStoresResponseDto> queryNeedFlattenStores(@AuthenticationPrincipal UserDto userDto,
//																					   @RequestBody LittleMallQueryFlattenStoresRequestDto request) {
//		log.info("[POST]/little-mall/flatten/search, userDto = {} ,request = {}", gson.toJson(userDto), gson.toJson(request));
//		return littleMallQueryFlattenStoresService.start(userDto, request);
//	}

	private final LittleMallFlattenProductsService littleMallFlattenProductsService;

//	@Operation(summary = "Flatten little mall sku (Temporal Function)", description = "status 1代表成功-1代表失敗")
//	@PostMapping
//	public ResponseDto<Void> flattenProducts(@AuthenticationPrincipal UserDto userDto,
//											 @RequestBody LittleMallFlattenProductsRequestDto request) {
//		log.info("[POST]/little-mall/flatten, userDto = {} ,request = {}", gson.toJson(userDto), gson.toJson(request));
//		return littleMallFlattenProductsService.start(userDto, request);
//	}
}
