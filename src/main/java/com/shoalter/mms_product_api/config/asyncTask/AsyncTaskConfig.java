package com.shoalter.mms_product_api.config.asyncTask;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Configuration
@EnableAsync
public class AsyncTaskConfig {
	@Value("${product.price.alert.onebound.threads:4}")
	private int oneBoundThreadsPoolSize;

    @Bean
    public Executor checkRecordProductTaskExecutor() {
        return generateThreadPoolTaskExecutor(4);
    }

    @Bean
    public Executor checkQueueProtocolRecordProductTaskExecutor() {
        return generateThreadPoolTaskExecutor(2);
    }

    @Bean
    public Executor checkRecordRowProductTaskExecutor() {
        return generateThreadPoolTaskExecutor(8);
    }

    @Bean
    public Executor checkQueueProtocolRecordRowProductTaskExecutor() {
        return generateThreadPoolTaskExecutor(4);
    }

	@Bean
	public Executor checkRequestPMRecordProductTaskExecutor() {
		return generateThreadPoolTaskExecutor(4);
	}

    @Bean
    public Executor checkRecordProductMasterTaskExecutor() {
        return generateThreadPoolTaskExecutor(4);
    }

	@Bean
	public Executor productExportExecutor(){
		return generateThreadPoolTaskExecutor(8);
	}

	@Bean
	public Executor productImageServerExecutor(){
		return generateThreadPoolTaskExecutor(16);
	}

    @Bean
    public Executor createRecordExecutor() {
		return generateThreadPoolTaskExecutor(4);
	}

    @Bean
    public Executor productSyncExecutor() {
		return generateThreadPoolTaskExecutor(4);
	}

	@Bean
	public Executor oneboundTaskExecutor() {
		return generateThreadPoolTaskExecutor(oneBoundThreadsPoolSize, oneBoundThreadsPoolSize);
	}

    private static ThreadPoolTaskExecutor generateThreadPoolTaskExecutor(int corePoolSize) {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(corePoolSize);
        threadPoolTaskExecutor.setMaxPoolSize(32);
        return threadPoolTaskExecutor;
    }

	private static ThreadPoolTaskExecutor generateThreadPoolTaskExecutor(int corePoolSize, int maxPoolSize) {
		ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
		threadPoolTaskExecutor.setCorePoolSize(corePoolSize);
		threadPoolTaskExecutor.setMaxPoolSize(maxPoolSize);
		return threadPoolTaskExecutor;
	}
}
