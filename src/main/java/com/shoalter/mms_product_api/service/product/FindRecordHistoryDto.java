package com.shoalter.mms_product_api.service.product;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Builder
public class FindRecordHistoryDto {
	List<Integer> merchantIdList;
	List<Integer> uploadTypeList;
	List<Integer> statusList;
	List<Integer> filterOutUploadTypeList;
	int userId;
	String skuId;
	String merchantName;
	Date startDate;
	Date endDate;
}
