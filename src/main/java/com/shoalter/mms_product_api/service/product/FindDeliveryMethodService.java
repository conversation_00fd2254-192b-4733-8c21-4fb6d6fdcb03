package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ProductDeliverMethod;
import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.DeliveryMethodResponseData;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.*;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class FindDeliveryMethodService {

	private final SysParmRepository sysParmRepository;

	public ResponseDto<SysParmDo> start(String buCode, String productReadyMethodCode) {
		SysParmDo deliveryMethod = generateDeliverMethod(buCode, productReadyMethodCode);
		return ResponseDto.<SysParmDo>builder().data(deliveryMethod).status(1).build();
	}

	public ResponseDto<List<DeliveryMethodResponseData>> start() {
		List<DeliveryMethodResponseData> results = new ArrayList<>();

		List<SysParmDo> productReadyMethods = sysParmRepository.findBySegmentAndBuCode(SysParmSegmentEnum.PRODUCT_READY_METHOD.name(), BuCodeEnum.HKTV.name());
		productReadyMethods.forEach(productReadyMethod -> {
			DeliveryMethodResponseData deliveryMethodResponseData = new DeliveryMethodResponseData(productReadyMethod.getCode(), new ArrayList<>());
			switch (productReadyMethod.getCode()) {
				case STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE:
				case STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY:
				case CONSIGNMENT:
				case THIRD_PARTY:
				case STANDARD_DELIVERY_SAME_DAY_IN_HUB:
				case HYBRID_DELIVERY_CONSOLIDATED:
					deliveryMethodResponseData.getDeliveryMethodCode().add(ProductDeliverMethod.HKTV_STANDARD_DELIVERY);
					break;
				case OVERSEA_DELIVERY:
					deliveryMethodResponseData.getDeliveryMethodCode().add(ProductDeliverMethod.OVERSEAS_DELIVERY);
					break;
				case NON_STANDARD_DELIVERY:
					deliveryMethodResponseData.getDeliveryMethodCode().add(ProductDeliverMethod.HKTV_NON_STANDARD_DELIVERY);
					break;
				case MERCHANT_DELIVERY:
				case E_VOUCHER:
				case MAINLAND_DELIVERY:
				default:
					deliveryMethodResponseData.getDeliveryMethodCode().add(ProductDeliverMethod.MERCHANT_DELIVERY);
					break;
			}

			results.add(deliveryMethodResponseData);
		});

		return ResponseDto.success(results);
	}

	private SysParmDo generateDeliverMethod(String buCode, String productReadyMethodCode) {
		String code;
		switch (productReadyMethodCode) {
			case STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE:
			case STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY:
			case CONSIGNMENT:
			case THIRD_PARTY:
			case STANDARD_DELIVERY_SAME_DAY_IN_HUB:
			case HYBRID_DELIVERY_CONSOLIDATED:
				code = ProductDeliverMethod.HKTV_STANDARD_DELIVERY;
				break;
			case OVERSEA_DELIVERY:
				code = ProductDeliverMethod.OVERSEAS_DELIVERY;
				break;
			case NON_STANDARD_DELIVERY:
				code = ProductDeliverMethod.HKTV_NON_STANDARD_DELIVERY;
				break;
			case MERCHANT_DELIVERY:
			case E_VOUCHER:
			case MAINLAND_DELIVERY:
			default:
				code = ProductDeliverMethod.MERCHANT_DELIVERY;
				break;
		}
		List<SysParmDo> list = sysParmRepository.findBySegmentAndBuCodeAndCode("DELIVERY_METHOD", buCode, code);
		if (CollectionUtil.isEmpty(list)) {
			return null;
		}
		return list.get(0);
	}
}
