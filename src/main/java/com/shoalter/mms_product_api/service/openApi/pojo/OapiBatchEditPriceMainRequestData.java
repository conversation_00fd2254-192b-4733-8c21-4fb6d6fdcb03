package com.shoalter.mms_product_api.service.openApi.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.shoalter.mms_product_api.config.security.xss.XssStringDeserializer;
import com.shoalter.mms_product_api.service.product.pojo.OapiBatchEditProductBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OapiBatchEditPriceMainRequestData extends OapiBatchEditProductBaseDto {
	private BigDecimal originalPrice;
	private BigDecimal sellingPrice;
	private String style;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountTextEn;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountTextCh;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountTextZhCN;
}
