package com.shoalter.mms_product_api.service.product.helper;

import static com.shoalter.mms_product_api.util.StringUtil.HTML_IMG;
import static com.shoalter.mms_product_api.util.StringUtil.HTML_SRC;
import static com.shoalter.mms_product_api.util.StringUtil.HTML_P;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.PackDimensionUnitEnum;
import com.shoalter.mms_product_api.config.product.ProductFileConfig;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.WeightUnitEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.business.BuProductCategoryRepository;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BuProductCategoryDo;
import com.shoalter.mms_product_api.dao.repository.contract.ContractRepository;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractProdTermsDo;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductImageViewDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductImportViewDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreWarehouseRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchProductImportWrapper;
import com.shoalter.mms_product_api.service.product.pojo.CheckContractProdTermResultDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.EverutsInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.ExchangeRatePriceDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductBarcodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductPartnerInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ThirdPLBUHktvDto;
import com.shoalter.mms_product_api.service.product.pojo.ThirdPLBUInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.ThirdPLBULittleMallDto;
import com.shoalter.mms_product_api.service.product.pojo.ThirdPLProductDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallBatchDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.thirdparty.api.SkuValidateRequestDto;
import com.shoalter.mms_product_api.util.BigDecimalUtil;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.ConvertPackingBoxTypeUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jsoup.nodes.Element;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class ProductPreProcessingHelper {
	private final Gson gson;
	private final MerchantRepository merchantRepository;
	private final StoreRepository storeRepository;
	private final BuProductCategoryRepository buProductCategoryRepository;
	private final ContractRepository contractRepository;
	private final StoreWarehouseRepository storeWarehouseRepository;
	private final ContractHelper contractHelper;
	private final CheckProductHelper checkProductHelper;
	private final ExchangeRateHelper exchangeRateHelper;
	private final ProductImageHelper productImageHelper;

	public CheckProductResultDto preProcessingHktvProduct(UserDto userDto, SaveProductRecordDo record, SaveProductRecordRowDo row, ProductMasterResultDto beforeProduct){
		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		singleEditProductDto.getProduct().setRecordRowId(row.getId());
		generateMasterData(userDto, record, singleEditProductDto);
		addHktvData(record.getUploadType(), singleEditProductDto, beforeProduct);
		addLittleMallData(singleEditProductDto);
		CheckProductResultDto check3plResult = generate3PLAdditional(userDto, singleEditProductDto, beforeProduct);
		row.setContent(gson.toJson(singleEditProductDto));
		return check3plResult;
	}

	public void preProcessingThePlaceProduct(UserDto userDto, SaveProductRecordDo record, SaveProductRecordRowDo row){
		SingleEditProductDto littleMallProduct = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		littleMallProduct.getProduct().setRecordRowId(row.getId());
		generateMasterData(userDto, record, littleMallProduct);
		addLittleMallData(littleMallProduct);
		row.setContent(gson.toJson(littleMallProduct));
	}

	public void preProcessingBundleProduct(UserDto userDto, Integer uploadType, SaveProductRecordRowDo row, ProductMasterResultDto beforeProduct){
		SingleEditProductDto singleEditProductProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		singleEditProductProductDto.getProduct().setRecordRowId(row.getId());
		addMerchantName(singleEditProductProductDto);
		addHktvData(uploadType, singleEditProductProductDto, beforeProduct);
		addCreationDateAndUser(userDto, singleEditProductProductDto, beforeProduct);
		row.setContent(gson.toJson(singleEditProductProductDto));
	}

	public LittleMallBatchDto preProcessingLittleMallBatchDto(
		BatchProductImportWrapper batchProductImportWrapper, Integer merchantId,
		List<ProductImageViewDo> productImageViewDoList, ProductImportViewDo productImportViewDo,
		BigDecimal rmbRate, boolean isHktvMainlandContract) {
		List<String> productOtherPhotos = new ArrayList<>();
		List<String> otherPhotos = new ArrayList<>();
		String mainPhoto = null;
		if (productImageViewDoList != null) {
			for (ProductImageViewDo productImageViewDo : productImageViewDoList) {
				if (ProductFileConfig.IMAGE_TYPE_MAIN.equals(productImageViewDo.getImageType())) {
					mainPhoto = productImageHelper.convertPhotoUrl(productImageViewDo.getFilePath());
				} else if (ProductFileConfig.IMAGE_TYPE_OTHER_A.equals(productImageViewDo.getImageType())) {
					productOtherPhotos.add(
						productImageHelper.convertPhotoUrl(productImageViewDo.getFilePath()));
				} else if (ProductFileConfig.IMAGE_TYPE_OTHER_B.equals(productImageViewDo.getImageType())) {
					otherPhotos.add(productImageHelper.convertPhotoUrl(productImageViewDo.getFilePath()));
				}
			}
		}

		String skuName = !StringUtils.isBlank(productImportViewDo.getBrandName())
			? String.format("%s-%s", productImportViewDo.getBrandName(), productImportViewDo.getSkuName())
			: productImportViewDo.getSkuName();

		String productId = productImportViewDo.getProductCode();

		if (ConstantType.CONSTANT_NO.equals(productImportViewDo.getIsPrimarySku())
			|| batchProductImportWrapper.getExistProductIds().contains(productId)) {
			productId = productId + UUID.randomUUID().toString().substring(0, 5);
		}

		StringBuilder description = new StringBuilder(
			StringUtils.isBlank(productImportViewDo.getLongDescription())
				? productImportViewDo.getShortDescription() : productImportViewDo.getLongDescription());

		otherPhotos.forEach(photoUrl -> {
			Element img = new Element(HTML_IMG).attr(HTML_SRC, photoUrl);
			description.append(new Element(HTML_P).appendChild(img).outerHtml());
		});

		ExchangeRatePriceDto exchangeRatePriceDto = exchangeRateHelper.checkCurrencyAndGeneraLittleMallPrice(
			rmbRate, productImportViewDo.getOriginalPrice(), productImportViewDo.getSellingPrice(),
			isHktvMainlandContract);

		return LittleMallBatchDto.builder()
			.stores(batchProductImportWrapper.getBatchProductImportRequestDto().getToStoreCode())
			.skuId(productImportViewDo.getSkuId())
			.productId(productId)
			.isPrimarySku(true)
			.skuName(skuName)
			.originalPrice(exchangeRatePriceDto.getOriginalPriceHkd())
			.sellingPrice(exchangeRatePriceDto.getSellingPriceHkd())
			.visibility(false)
			.onlineStatus(false)
			.skuLongDescriptionCh(description.toString())
			.mainPhoto(mainPhoto)
			.otherPhoto(productOtherPhotos)
			.merchantId(merchantId)
			.merchantName(batchProductImportWrapper.getMerchantName())
			.displayInHktvmallCategory(List.of(productImportViewDo.getCategoryCode()))
			.build();
	}

	private void generateMasterData(UserDto userDto, SaveProductRecordDo record, SingleEditProductDto singleEditProductDto) {
		singleEditProductDto.getProduct().setMmsModifiedTime(LocalDateTime.ofInstant(record.getUploadTime().toInstant(), ZoneId.systemDefault()));
		singleEditProductDto.getProduct().setMmsModifiedUser(userDto.getUserCode());
		addMerchantName(singleEditProductDto);
		generateStorageTemperatureByPackingBoxType(singleEditProductDto);
		setProductMasterDefaultValue(singleEditProductDto);
		List<ProductBarcodeDto> productBarcodeDtoList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(singleEditProductDto.getProduct().getBarcodes())) {
			productBarcodeDtoList = singleEditProductDto.getProduct().getBarcodes().stream().filter(productBarcodeDto -> StringUtil.isNotEmpty(productBarcodeDto.getEan())).collect(Collectors.toList());
		}
		singleEditProductDto.getProduct().setBarcodes(productBarcodeDtoList);
	}

	private void addMerchantName(SingleEditProductDto singleEditProductDto) {
		String merchantName = merchantRepository.findByMerchantId(singleEditProductDto.getProduct().getMerchantId());
		singleEditProductDto.getProduct().setMerchantName(merchantName);
	}

	private void generateStorageTemperatureByPackingBoxType(SingleEditProductDto singleEditProductDto){
		String packingBoxType = singleEditProductDto.getProduct().getPackingBoxType();
		singleEditProductDto.getProduct().setStorageTemperature(ConvertPackingBoxTypeUtil.generateStorageTemperature(packingBoxType));
	}

	private void setProductMasterDefaultValue(SingleEditProductDto singleEditProductDto){
		ProductMasterDto productMasterDto = singleEditProductDto.getProduct();
		// default string type value is empty string("")
		productMasterDto.setSkuNameEn(StringUtil.generateDefaultStringValue(productMasterDto.getSkuNameEn()));
	}

	public void addHktvData(Integer uploadType, SingleEditProductDto singleEditProductDto, ProductMasterResultDto beforeProduct) {
		HktvProductDto hktvProduct = singleEditProductDto.getProduct().getAdditional().getHktv();
		if (hktvProduct != null) {
			StoreDo storeDo = storeRepository.findHktvStoreByStoreCode(hktvProduct.getStores()).orElse(null);
			if (storeDo != null) {
				addStoreSkuId(singleEditProductDto, storeDo);
				addStorefrontStoreCode(singleEditProductDto, storeDo);
				String contractType = contractRepository.findMainContractTypeInContract(singleEditProductDto.getProduct().getAdditional().getHktv().getContractNo());
				ContractProdTermsDo contractProdTermsDo = addCommissionRateAndOnlineStatus(singleEditProductDto, storeDo, uploadType);
				setTermName(contractType, contractProdTermsDo, singleEditProductDto);
				addInvisible(uploadType, singleEditProductDto);
				checkIsNeedOffLineBundle(uploadType, singleEditProductDto, beforeProduct);
				setPartnerInfo(singleEditProductDto, beforeProduct);
				clearMainlandContractDiscount(singleEditProductDto, contractType);
				switch (uploadType) {
					case SaveProductType.BATCH_CREATE_PRODUCT:
					case SaveProductType.BATCH_EDIT_PRODUCT:
						convertJKContractOriginalPriceByCost(singleEditProductDto, storeDo, contractType);
						break;
					case SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL:
					case SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL:
						convertJKContractOriginalPriceByCost(singleEditProductDto, storeDo, contractType);
						convertToHkdForNonMainlandContract(singleEditProductDto, contractType);
						break;
					default:
				}
			}
		}
	}

	private void addStoreSkuId(SingleEditProductDto singleEditProductDto, StoreDo storeDo) {
		singleEditProductDto.getProduct().getAdditional().getHktv().setStoreSkuId(storeDo.getStorefrontStoreCode() + "_S_" + singleEditProductDto.getProduct().getSkuId());
	}

	private void addStorefrontStoreCode(SingleEditProductDto singleEditProductDto, StoreDo storeDo) {
		singleEditProductDto.getProduct().getAdditional().getHktv().setStorefrontStoreCode(storeDo.getStorefrontStoreCode());
	}

	private void checkIsNeedOffLineBundle(Integer uploadType, SingleEditProductDto singleEditProductDto, ProductMasterResultDto beforeProduct){
		if (!SaveProductType.CHECK_BUNDLE_OFFLINE_PRODUCT_SET.contains(uploadType)) {
			return;
		}

		boolean	isCheckOffLineBundle;
		HktvProductDto editHktvProductDto = singleEditProductDto.getProduct().getAdditional().getHktv();
		HktvProductDto beforeHktvProductDto = beforeProduct.getAdditional().getHktv();
		// check is change online status
		isCheckOffLineBundle = isChangeOnlineStatus(editHktvProductDto.getOnlineStatus(), beforeHktvProductDto.getOnlineStatus());

		// check is change warehouse , BATCH_EDIT_PRODUCT_COMMONLY_USED do not need to check
		if(!isCheckOffLineBundle){
			isCheckOffLineBundle = storeWarehouseRepository.isWarehouseSeqNoDifferent(editHktvProductDto.getWarehouseId(), beforeHktvProductDto.getWarehouseId());
		}
		editHktvProductDto.setCheckOffLineBundle(isCheckOffLineBundle);
	}

	private boolean isChangeOnlineStatus(OnlineStatusEnum editStatus, OnlineStatusEnum beforeStatus){
		// check is change online status
		if(Objects.nonNull(editStatus) && OnlineStatusEnum.ONLINE == beforeStatus) {
			return OnlineStatusEnum.OFFLINE == editStatus;
		}
		return false;
	}

	private ContractProdTermsDo addCommissionRateAndOnlineStatus(SingleEditProductDto singleEditProductDto, StoreDo storeDo, Integer uploadType) {
		HktvProductDto hktvProduct = singleEditProductDto.getProduct().getAdditional().getHktv();
		Optional<BuProductCategoryDo> productCatDoOpt = buProductCategoryRepository.findByProductCatCode(ConstantType.PRODUCT_CATEGORY_STORE, ConstantType.PLATFORM_CODE_HKTV, hktvProduct.getPrimaryCategoryCode());
		ContractProdTermsDo contractProdTermsDo = null;
		if (productCatDoOpt.isPresent()) {
			if (ConstantType.N.equalsIgnoreCase(productCatDoOpt.get().getActiveInd())) {
				log.warn("primary category is inactive. primary category code: {}, skuId: {}, uploadType: {}", hktvProduct.getPrimaryCategoryCode(), singleEditProductDto.getProduct().getSkuId(), uploadType);
			}

			CheckContractProdTermResultDto contractProdTermResultDto =
				checkProductHelper.checkContractProdTerm(hktvProduct.getTermName(), hktvProduct.getContractNo(), storeDo.getId(), hktvProduct.getProductReadyMethod(), singleEditProductDto.getProduct().getSkuId(), productCatDoOpt.get(), singleEditProductDto.getProduct().getBrandId());
			contractProdTermsDo = contractProdTermResultDto.getContractProdTerms();

			if (contractProdTermsDo != null) {
				log.info("sku：{}, storeSkuId：{}, contractProductTermId：{}, commissionRate：{}",
					singleEditProductDto.getProduct().getSkuId(), hktvProduct.getStoreSkuId(), contractProdTermsDo.getId(), contractProdTermsDo.getCommissionRate());

				if (contractProdTermResultDto.getContractProdTerms().getCommissionRate() != null) {
					singleEditProductDto.getProduct().getAdditional().getHktv().setCommissionRate(contractProdTermResultDto.getContractProdTerms().getCommissionRate());
				}

				OnlineStatusEnum inputOnlineStatus = hktvProduct.getOnlineStatus();
				boolean isCreateProductType = SaveProductType.CREATE_HKTV_PRODUCT_TYPE_SET.contains(uploadType);
				if (isCreateProductType) {
					// create product type, if online status is null, set default value: ONLINE
					singleEditProductDto.getProduct().getAdditional().getHktv().setOnlineStatus(inputOnlineStatus == null ? OnlineStatusEnum.ONLINE : inputOnlineStatus);
				} else {
					singleEditProductDto.getProduct().getAdditional().getHktv().setOnlineStatus(inputOnlineStatus);
				}

			} else {
				singleEditProductDto.getProduct().getAdditional().getHktv().setOnlineStatus(OnlineStatusEnum.OFFLINE);
			}
		}
		return contractProdTermsDo;
	}

	private void addInvisible(Integer uploadType, SingleEditProductDto singleEditProductDto) {
		boolean isNotCreateProduct = !SaveProductType.CREATE_HKTV_PRODUCT_TYPE_SET.contains(uploadType);
		if (isNotCreateProduct) {
			return;
		}

		Integer contractNo = singleEditProductDto.getProduct().getAdditional().getHktv().getContractNo();
		int count = contractRepository.countIsBCContract(contractNo);
		if (count > 0) {
			singleEditProductDto.getProduct().getAdditional().getHktv().setVisibility("N");
		}
	}

	private void convertJKContractOriginalPriceByCost(SingleEditProductDto singleEditProductDto, StoreDo storeDo, String contractType) {
		BigDecimal cost = singleEditProductDto.getProduct().getAdditional().getHktv().getCost();
		if (cost != null && StringUtil.isNotEmpty(contractType) && (contractType.equals("KM") || contractType.equals("JK"))) {
			BigDecimal originalPriceValue = checkProductHelper
					.calculateJapanAndKoreaContractOriginalPrice(cost, storeDo.getMarkUpRate(), singleEditProductDto.getProduct().getAdditional().getHktv().getCurrency());
			singleEditProductDto.getProduct().setOriginalPrice(originalPriceValue);
		}
	}

	private void convertToHkdForNonMainlandContract(SingleEditProductDto singleEditProductDto, String contractType) {
		if (!ContractType.MAINLAND_MERCHANT_CONTRACT_SET.contains(contractType)) {
			ProductMasterDto product = singleEditProductDto.getProduct();
			HktvProductDto hktv = product.getAdditional().getHktv();

			if (CurrencyEnum.RMB.name().equals(hktv.getCurrency())) {
				BigDecimal rmbRate = exchangeRateHelper.getExchangeRateByCurrency(CurrencyEnum.RMB);
				product.setOriginalPrice(product.getOriginalPrice().multiply(rmbRate).setScale(4, RoundingMode.HALF_UP));
				Optional.ofNullable(hktv.getSellingPrice())
					.ifPresent(price -> hktv.setSellingPrice(price.multiply(rmbRate).setScale(4, RoundingMode.HALF_UP)));
				hktv.setCurrency(CurrencyEnum.HKD.name());
			}
		}
	}

	private void clearMainlandContractDiscount(SingleEditProductDto singleEditProductDto, String contractType) {
		if (ContractType.MAINLAND_MERCHANT_CONTRACT_SET.contains(contractType)) {
			singleEditProductDto.getProduct().getAdditional().getHktv().setDiscountTextCh(null);
			singleEditProductDto.getProduct().getAdditional().getHktv().setDiscountTextEn(null);
			singleEditProductDto.getProduct().getAdditional().getHktv().setDiscountTextSc(null);
			singleEditProductDto.getProduct().getAdditional().getHktv().setStyle(null);
		}
	}

	private void addLittleMallData(SingleEditProductDto singleEditProductDto) {
		// add store_sku_id (Format : store id_s_sku id)
		LittleMallProductDto littleMallProductDto = singleEditProductDto.getProduct().getAdditional().getLittleMall();
		if (littleMallProductDto != null){
			singleEditProductDto.getProduct().getAdditional().getLittleMall().setStoreSkuId(littleMallProductDto.getStoreCode() + "_S_" + singleEditProductDto.getProduct().getSkuId());
		}
	}

	private CheckProductResultDto generate3PLAdditional(UserDto userDto, SingleEditProductDto singleEditProductDto, ProductMasterResultDto beforeProduct) {
		Pair<Boolean, CheckProductResultDto> isAdd3PLInfo = isAdd3PLInfo(userDto, singleEditProductDto, beforeProduct);
		if (!isAdd3PLInfo.getLeft()) {
			singleEditProductDto.getProduct().getAdditional().setThirdParty(null);
			return isAdd3PLInfo.getRight();
		}

		HktvProductDto hktvProduct = singleEditProductDto.getProduct().getAdditional().getHktv();
		LittleMallProductDto littleMallProduct = singleEditProductDto.getProduct().getAdditional().getLittleMall();
		ThirdPLProductDto thirdPLDto = Optional.ofNullable(singleEditProductDto.getProduct().getAdditional().getThirdParty()).orElse(new ThirdPLProductDto());
		thirdPLDto.setBuInfo(new ThirdPLBUInfoDto());

		if (hktvProduct != null) {
			thirdPLDto.setSkuShortDescriptionCh(hktvProduct.getSkuShortDescriptionCh());
			thirdPLDto.setSkuShortDescriptionEn(hktvProduct.getSkuShortDescriptionEn());
			thirdPLDto.setMainPhoto(hktvProduct.getMainPhoto());
			thirdPLDto.setSellingPrice(hktvProduct.getSellingPrice());
			thirdPLDto.setCommissionRate(hktvProduct.getCommissionRate());

			ThirdPLBUHktvDto thirdPLBUHktvDto = new ThirdPLBUHktvDto();
			thirdPLBUHktvDto.setStoreSkuId(hktvProduct.getStoreSkuId());
			thirdPLBUHktvDto.setProductReadyMethod(hktvProduct.getProductReadyMethod());
			thirdPLBUHktvDto.setPrimaryCategoryCode(hktvProduct.getPrimaryCategoryCode());
			thirdPLDto.getBuInfo().setHktv(thirdPLBUHktvDto);
		} else if (littleMallProduct != null) {
			thirdPLDto.setSkuShortDescriptionCh(littleMallProduct.getSkuLongDescriptionCh());
			// 因為Little mall沒有這個欄位所以塞預設值
			thirdPLDto.setSkuShortDescriptionEn("");
			thirdPLDto.setMainPhoto(littleMallProduct.getMainPhoto());
			thirdPLDto.setSellingPrice(littleMallProduct.getSellingPrice());

			ThirdPLBULittleMallDto thirdPLBULittleMallDto = new ThirdPLBULittleMallDto();
			thirdPLBULittleMallDto.setProductReadyMethod("");
			thirdPLDto.getBuInfo().setLittleMall(thirdPLBULittleMallDto);
		}
		singleEditProductDto.getProduct().getAdditional().setThirdParty(thirdPLDto);
		return isAdd3PLInfo.getRight();
	}

	private Pair<Boolean, CheckProductResultDto> isAdd3PLInfo(UserDto userDto, SingleEditProductDto singleEditProductDto, ProductMasterResultDto beforeProduct) {
		//LM no use 3PL
		if(singleEditProductDto.getProduct().getAdditional().getLittleMall()!=null){
			return Pair.of(false, null);
		}

		boolean tplBuExist = beforeProduct != null && beforeProduct.getAdditional()!=null && beforeProduct.getAdditional().getThirdParty() != null;

		CheckProductResultDto checkPackInfoBy3PLResult = checkPackInfoBy3PL(userDto, singleEditProductDto);
		ProductMasterDto product = singleEditProductDto.getProduct();
		boolean isAdd3PL = !product.isDisableTo3PL() && checkPackInfoBy3PLResult.isResult();

		if (!isAdd3PL &&
				singleEditProductDto.getProduct().getAdditional().getHktv() != null &&
				ProductReadyMethodType.THIRD_PARTY.equals(singleEditProductDto.getProduct().getAdditional().getHktv().getProductReadyMethod())) {
			throw new SystemException(gson.toJson(checkPackInfoBy3PLResult.getErrorMessageList()));
		}

		return Pair.of(tplBuExist || isAdd3PL, checkPackInfoBy3PLResult);
	}

	private CheckProductResultDto checkPackInfoBy3PL(UserDto userDto, SingleEditProductDto singleEditProductDto) {
		ProductMasterDto product = singleEditProductDto.getProduct();
		SkuValidateRequestDto skuValidateRequestDto = getSkuValidateRequestDto(product);
		return checkProductHelper.checkPackInfoBy3PL(userDto, List.of(skuValidateRequestDto), singleEditProductDto.getProduct().getSkuId());
	}

	private static SkuValidateRequestDto getSkuValidateRequestDto(ProductMasterDto product) {
		Integer merchantId = product.getMerchantId();
		String uuid = product.getUuid();
		String packingBoxTypeCode = product.getPackingBoxType();
		List<String> barCodeList = product.getBarcodes().stream().map(ProductBarcodeDto::getEan).collect(Collectors.toList());

		String packingDimensionUnit = Optional.ofNullable(product.getPackingDimensionUnit()).orElse(PackDimensionUnitEnum.MM.name());
		BigDecimal length = BigDecimalUtil.generateDefaultValue(product.getPackingLength());
		BigDecimal height = BigDecimalUtil.generateDefaultValue(product.getPackingHeight());
		BigDecimal width = BigDecimalUtil.generateDefaultValue(product.getPackingDepth());

		String weightUnit = Optional.ofNullable(product.getWeightUnit()).orElse(WeightUnitEnum.G.name());
		BigDecimal weight = BigDecimalUtil.generateDefaultValue(product.getWeight());

		return new SkuValidateRequestDto(merchantId, uuid, length, width, height, weight, packingBoxTypeCode, barCodeList, packingDimensionUnit, weightUnit);
	}

	private void addCreationDateAndUser(UserDto userDto, SingleEditProductDto singleEditProductProductDto, ProductMasterResultDto beforeProduct) {
		if (beforeProduct.getMmsCreateUser() == null) {
			singleEditProductProductDto.getProduct().setMmsCreateTime(LocalDateTime.now());
			singleEditProductProductDto.getProduct().setMmsCreateUser(userDto.getUserCode());
		} else {
			singleEditProductProductDto.getProduct().setMmsCreateUser(beforeProduct.getMmsCreateUser());
			singleEditProductProductDto.getProduct().setMmsCreateTime(beforeProduct.getMmsCreateTime());
		}
	}

	private void setTermName(String contractType, ContractProdTermsDo contractProdTermsDo, SingleEditProductDto singleEditProductDto) {
		if (!ContractType.ANNUAL_INSURANCE_CONTRACT.equals(contractType)) {
			return;
		}
		String termName = contractHelper.getTermName(contractType, contractProdTermsDo);
		singleEditProductDto.getProduct().getAdditional().getHktv().setTermName(termName);
	}

	void setPartnerInfo(SingleEditProductDto singleEditProductDto, ProductMasterResultDto beforeProduct) {
		//if user not set everuts info, use everuts info from Product master product info
		if (beforeProduct == null || beforeProduct.getAdditional() == null) {
			return;
		}

		Long buyerId = null;
		String skuId = null;
		HktvProductDto beforeHktvProductDto = beforeProduct.getAdditional().getHktv();

		Optional<EverutsInfoDto> everutsInfoOpt = Optional.ofNullable(beforeHktvProductDto)
			.map(HktvProductDto::getPartnerInfo)
			.map(ProductPartnerInfoDto::getEveruts);
		if (everutsInfoOpt.isPresent()) {
			buyerId = everutsInfoOpt.get().getBuyerId();
			skuId = everutsInfoOpt.get().getSkuId();
		}

		if (buyerId == null && skuId == null) {
			return;
		}

		HktvProductDto hktvProductDto = singleEditProductDto.getProduct().getAdditional().getHktv();
		if (hktvProductDto.getPartnerInfo() == null) {
			hktvProductDto.setPartnerInfo(new ProductPartnerInfoDto());
		}
		if (hktvProductDto.getPartnerInfo().getEveruts() == null) {
			hktvProductDto.getPartnerInfo().setEveruts(new EverutsInfoDto());
		}

		EverutsInfoDto everutsInfoDto = hktvProductDto.getPartnerInfo().getEveruts();
		if (everutsInfoDto.getBuyerId() == null) {
			everutsInfoDto.setBuyerId(buyerId);
		}
		if (everutsInfoDto.getSkuId() == null) {
			everutsInfoDto.setSkuId(skuId);
		}
	}
}
