package com.shoalter.mms_product_api.config.security.xss;

import com.shoalter.mms_product_api.util.StringUtil;
import io.micrometer.core.instrument.util.StringUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;

@Slf4j
public final class XssUtils {

	private static final List<String> XSS_KEYWORDS = List.of("<script", "<iframe", "<form");

	private XssUtils() {
	}

	public static String escapeHtml(String value) {
		if (StringUtil.isNullOrBlank(value)) {
			return null;
		}
		return StringEscapeUtils.escapeHtml4(value);
	}

	public static String xssClean(String before) {

		if (StringUtils.isBlank(before)) {
			return before;
		}

		if (XSS_KEYWORDS.stream().anyMatch(before.toLowerCase()::contains)) {

			String after = StringEscapeUtils.escapeHtml4(before);
			log.info("Detected potential XSS content'. Before: [{}], After: [{}]", before, after);

			return after;
		}

		return before;
	}
}
