package com.shoalter.mms_product_api.util;

import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Pattern;

@Slf4j
public class DateUtil {

	public static final String DATE_FORMAT_YEAR_MONTH_DAY_HOUR = "yyyy-MM-dd HH:00";
	public static final String DATE_FORMAT_YEAR_MONTH_DAY_HOUR_M = "yyyy-MM-dd HH:mm";
	public static final String ISO8601_UTC = "yyyy-MM-dd'T'HH:mm:ss.SSSz";
	public static final String ISO8601_UTC_WITHOUT_SSSZ = "yyyy-MM-dd'T'HH:mm:ss";
	public static final String DATE_FORMAT_SIMPLE = "yyyy-MM-dd HH:mm:ss";
	public static final String DATE_FORMAT_YEAR_MONTH_DAY = "yyyy-MM-dd";
	public static final String FOREVER_DATE = "2099-12-31 00:00:00";
	public static final Pattern ISO8601_UTC_PATTEN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{3}Z$");
	public static final Pattern ISO8601_UTC_PATTEN_WITHOUT_SSSZ = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}$");


	public static Date changeStringForDate(String str) throws ParseException {
		return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(str);
	}

	public static Date timestampToDate(Long timestamp) {
		return new Timestamp(timestamp);
	}


	public static Date timestampToDateStartTime(Long timestamp) {
		if (timestamp == null) {
			return null;
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(timestamp);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		return calendar.getTime();
	}


	public static Date timestampToDateEndTime(Long timestamp) {
		if (timestamp == null) {
			return null;
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(timestamp);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		return calendar.getTime();
	}

	/**
	 *
	 * @param dateStr      "2024-06-27T23:10:22"
	 * @param inputParseStyle  "yyyy-MM-dd'T'HH:mm:ss"
	 * @param outputFormat "yyyy-MM-dd HH:00"
	 * @return Date 2024-06-27T23:00:00.000+0800
	 */
	public static Date formatToDateFromDateStringAndFormatStyle(String dateStr, String inputParseStyle, String outputFormat) {
		if (StringUtil.isNullOrBlank(dateStr) || StringUtil.isNullOrBlank(inputParseStyle) || StringUtil.isNullOrBlank(outputFormat)) {
			return null;
		}
		Date date = null;
		try {
			String formatDateString = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(inputParseStyle)).format(DateTimeFormatter.ofPattern(outputFormat));
			date = new SimpleDateFormat(outputFormat).parse(formatDateString);
		} catch (Exception e) {
			log.error("formatToDateFromDateStringAndFormatStyle, dateStr: {}, inputFormat: {}, outputFormat: {},  error:",dateStr, inputParseStyle, outputFormat, e);
		}
		return date;
	}

	public static LocalDateTime checkIso8601UtcPatten(String localDateTimeString){
		if (localDateTimeString != null) {
			if (ISO8601_UTC_PATTEN.matcher(localDateTimeString).matches()) {
				try {
					return LocalDateTime.parse(localDateTimeString, DateTimeFormatter.ofPattern(DateUtil.ISO8601_UTC));
				} catch (Exception e) {
					return null;
				}
			} else if (ISO8601_UTC_PATTEN_WITHOUT_SSSZ.matcher(localDateTimeString).matches()) {
				try {
					return LocalDateTime.parse(localDateTimeString, DateTimeFormatter.ofPattern(DateUtil.ISO8601_UTC_WITHOUT_SSSZ));
				} catch (Exception e) {
					return null;
				}
			} else {
				return null;
			}
		}
		return null;
	}
}
