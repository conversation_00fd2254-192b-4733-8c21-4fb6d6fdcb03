package com.shoalter.mms_product_api.service.price_alert;

import com.shoalter.mms_product_api.config.product.MonitorProductPriceStatus;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.product.ThirdPartySourceEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.product.ProductPriceMonitorProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.product.helper.BusUnitHelper;
import com.shoalter.mms_product_api.service.product.helper.MmsThirdPartySkuHelper;
import com.shoalter.mms_product_api.service.product.pojo.BusinessPlatformViewDo;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailResponseDataDto;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailSkuDto;
import lombok.Getter;
import lombok.Setter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for ProductPriceMonitorService
 *
 * These tests focus on the critical logic of the service:
 * - Fetching and updating product prices from third-party platforms
 * - Handling various API response scenarios
 * - Processing different product conditions
 */
@ExtendWith(MockitoExtension.class)
class ProductPriceMonitorServiceTest {

    // Mocks
    @Mock
    private ProductPriceMonitorProductRepository productPriceMonitorProductRepository;

    @Mock
    private MmsThirdPartySkuHelper mmsThirdPartySkuHelper;

	@Mock
	private BusUnitHelper busUnitHelper;

    @InjectMocks
    private ProductPriceMonitorService productPriceMonitorService;

    @Captor
    private ArgumentCaptor<List<ProductPriceMonitorProductDo>> productsCaptor;

    // Test constants
    private static final String HKTV = "HKTV";
    private static final Integer BUSINESS_ID = 1;
    private static final String TARGET_PRODUCT_CODE = "12345";
    private static final String TARGET_SKU_CODE_1 = "12345-1";
    private static final String TARGET_SKU_CODE_2 = "12345-2";
    private static final String TARGET_SKU_NOT_FOUND = "NOT-FOUND-SKU";
    private static final Integer STORE_ID = 1001;
    private static final String PRODUCT_DETAIL_URL = "https://example.com/product/12345";

    // Test data
    private BusinessPlatformViewDoTest businessPlatformViewDo;
    private List<ProductPriceMonitorProductDo> enabledProducts;
    private OneBoundDetailResponseDto successResponse;
    private OneBoundDetailResponseDto errorResponse;

    /**
     * Test implementation of BusinessPlatformViewDo for unit testing
     */
    @Getter
    @Setter
    static class BusinessPlatformViewDoTest implements BusinessPlatformViewDo {
        Integer businessId;
        String businessCode;
        String businessName;
        String businessType;
        String platformCode;
        Integer platformId;
    }

    @BeforeEach
    void setUp() {
        setupBusinessPlatform();
        setupTestProducts();
        setupApiResponses();
    }

    /**
     * Set up business platform data
     */
    private void setupBusinessPlatform() {
        businessPlatformViewDo = new BusinessPlatformViewDoTest();
        businessPlatformViewDo.setBusinessId(BUSINESS_ID);
    }

    /**
     * Set up test product data
     */
    private void setupTestProducts() {
        enabledProducts = new ArrayList<>();

        // First product
        ProductPriceMonitorProductDo product1 = createTestProduct(1, TARGET_PRODUCT_CODE, TARGET_SKU_CODE_1);
        enabledProducts.add(product1);

        // Second product with same target product code but different SKU
        ProductPriceMonitorProductDo product2 = createTestProduct(2, TARGET_PRODUCT_CODE, TARGET_SKU_CODE_2);
        enabledProducts.add(product2);
    }

    /**
     * Create a test product with specified ID and codes
     */
    private ProductPriceMonitorProductDo createTestProduct(int id, String productCode, String skuCode) {
        ProductPriceMonitorProductDo product = new ProductPriceMonitorProductDo();
        product.setId(id);
        product.setTargetProductCode(productCode);
        product.setTargetSkuCode(skuCode);
        product.setStoreId(STORE_ID);
        product.setActiveInd(1);
        product.setTargetPlatform(ThirdPartySourceEnum.TMALL.name());
        product.setBusUnitId(BUSINESS_ID);
        return product;
    }

    /**
     * Set up API response test data
     */
    private void setupApiResponses() {
        // Set up success response
        successResponse = new OneBoundDetailResponseDto();
        successResponse.setStatus(StatusCodeEnum.SUCCESS.getCode());

        OneBoundDetailResponseDataDto detailData = new OneBoundDetailResponseDataDto();
        detailData.setDetailUrl(PRODUCT_DETAIL_URL);

        List<OneBoundDetailSkuDto> skus = createTestSkus();
        detailData.setSkus(skus);
        successResponse.setData(detailData);

        // Set up error response
        errorResponse = new OneBoundDetailResponseDto();
        errorResponse.setStatus(StatusCodeEnum.FAIL.getCode());
        errorResponse.setErrorMessageList(Collections.singletonList("API error"));
    }

    /**
     * Create test SKU data
     */
    private List<OneBoundDetailSkuDto> createTestSkus() {
        List<OneBoundDetailSkuDto> skus = new ArrayList<>();

        // First SKU
        OneBoundDetailSkuDto sku1 = new OneBoundDetailSkuDto();
        sku1.setSkuCode(TARGET_SKU_CODE_1);
        sku1.setOriginalPrice(new BigDecimal("100.00"));
        sku1.setPrice(new BigDecimal("90.00"));
        skus.add(sku1);

        // Second SKU
        OneBoundDetailSkuDto sku2 = new OneBoundDetailSkuDto();
        sku2.setSkuCode(TARGET_SKU_CODE_2);
        sku2.setOriginalPrice(new BigDecimal("110.00"));
        sku2.setPrice(new BigDecimal("95.00"));
        skus.add(sku2);

        return skus;
    }

    /**
     * Setup standard repository mocks for tests
     */
    private void setupCommonMocks() {
		when(busUnitHelper.findBusinessId(HKTV)).thenReturn(BUSINESS_ID);
        when(productPriceMonitorProductRepository.findByActiveIndAndTargetPlatformAndBusUnitId(
                anyInt(), anyString(), eq(BUSINESS_ID))).thenReturn(enabledProducts);
    }

    /**
     * Verify product has correct updated price information
     */
    private void verifyUpdatedPrice(ProductPriceMonitorProductDo product, BigDecimal originalPrice,
                                   BigDecimal sellingPrice, int status) {
        assertEquals(originalPrice, product.getTargetOriginalPrice());
        assertEquals(sellingPrice, product.getTargetSellingPrice());
        assertEquals(status, product.getPriceStatus());
        assertEquals(PRODUCT_DETAIL_URL, product.getTargetUrl());
        assertNotNull(product.getTargetPriceUpdatedDate());
    }

    @Nested
    @DisplayName("Success Scenarios")
    class SuccessTests {
        @Test
        @DisplayName("Should successfully update prices when API returns valid data")
        void updateMonitorProductPrice_Success() {
            // Given
            setupCommonMocks();
            when(mmsThirdPartySkuHelper.fetchSkuOneBoundDetail(any(OneBoundDetailRequestDto.class)))
                    .thenReturn(successResponse);

            // When
            productPriceMonitorService.updateMonitorProductPrice();

            // Then
            verify(productPriceMonitorProductRepository).findByActiveIndAndTargetPlatformAndBusUnitId(1, ThirdPartySourceEnum.TMALL.name(), BUSINESS_ID);
            verify(mmsThirdPartySkuHelper).fetchSkuOneBoundDetail(any(OneBoundDetailRequestDto.class));
            verify(productPriceMonitorProductRepository).saveAll(productsCaptor.capture());

            List<ProductPriceMonitorProductDo> updatedProducts = productsCaptor.getValue();
            assertEquals(2, updatedProducts.size());

            // Verify products were updated correctly
            ProductPriceMonitorProductDo updatedProduct1 = findProductById(updatedProducts, 1);
            ProductPriceMonitorProductDo updatedProduct2 = findProductById(updatedProducts, 2);

            verifyUpdatedPrice(updatedProduct1,
                new BigDecimal("100.00"),
                new BigDecimal("90.00"),
                MonitorProductPriceStatus.PRICE_UPDATED.getValue());

            verifyUpdatedPrice(updatedProduct2,
                new BigDecimal("110.00"),
                new BigDecimal("95.00"),
                MonitorProductPriceStatus.PRICE_UPDATED.getValue());
        }

        @Test
        @DisplayName("Should handle SKU not found in API response")
        void updateMonitorProductPrice_SkuNotFound() {
            // Given
            // Create a product with a SKU code that isn't in the API response
            ProductPriceMonitorProductDo notFoundProduct = createTestProduct(3, TARGET_PRODUCT_CODE, TARGET_SKU_NOT_FOUND);

            List<ProductPriceMonitorProductDo> productsWithNotFound = new ArrayList<>(enabledProducts);
            productsWithNotFound.add(notFoundProduct);

			when(busUnitHelper.findBusinessId(HKTV)).thenReturn(BUSINESS_ID);
            when(productPriceMonitorProductRepository.findByActiveIndAndTargetPlatformAndBusUnitId(
                    anyInt(), anyString(), eq(BUSINESS_ID))).thenReturn(productsWithNotFound);
            when(mmsThirdPartySkuHelper.fetchSkuOneBoundDetail(any(OneBoundDetailRequestDto.class)))
                    .thenReturn(successResponse);

            // When
            productPriceMonitorService.updateMonitorProductPrice();

            // Then
            verify(productPriceMonitorProductRepository).saveAll(productsCaptor.capture());

            List<ProductPriceMonitorProductDo> updatedProducts = productsCaptor.getValue();
            assertEquals(3, updatedProducts.size());

            // Find the not found product in updated products
            ProductPriceMonitorProductDo updatedNotFoundProduct = findProductById(updatedProducts, 3);

            assertNotNull(updatedNotFoundProduct);
            assertNull(updatedNotFoundProduct.getTargetOriginalPrice());
            assertNull(updatedNotFoundProduct.getTargetSellingPrice());
            assertEquals(MonitorProductPriceStatus.PRICE_NOT_FOUND.getValue(), updatedNotFoundProduct.getPriceStatus());
            assertNotNull(updatedNotFoundProduct.getTargetPriceUpdatedDate());
        }
    }

    @Nested
    @DisplayName("Error Handling Scenarios")
    class ErrorHandlingTests {
        @Test
        @DisplayName("Should handle API error response")
        void updateMonitorProductPrice_ApiError() {
            // Given
            setupCommonMocks();
            when(mmsThirdPartySkuHelper.fetchSkuOneBoundDetail(any(OneBoundDetailRequestDto.class)))
                    .thenReturn(errorResponse);

            // When
            productPriceMonitorService.updateMonitorProductPrice();

            // Then
            verify(productPriceMonitorProductRepository).saveAll(productsCaptor.capture());

            List<ProductPriceMonitorProductDo> updatedProducts = productsCaptor.getValue();
            assertEquals(2, updatedProducts.size());

            // Verify all products have error status
            for (ProductPriceMonitorProductDo product : updatedProducts) {
                assertEquals(MonitorProductPriceStatus.THIRD_PARTY_SERVER_ERROR.getValue(), product.getPriceStatus());
                assertNotNull(product.getLastUpdatedDate());
            }
        }

        @Test
        @DisplayName("Should handle API returning null response")
        void updateMonitorProductPrice_NullResponse() {
            // Given
            setupCommonMocks();
            when(mmsThirdPartySkuHelper.fetchSkuOneBoundDetail(any(OneBoundDetailRequestDto.class)))
                    .thenReturn(null);

            // When
            productPriceMonitorService.updateMonitorProductPrice();

            // Then
            verify(productPriceMonitorProductRepository).saveAll(productsCaptor.capture());

            List<ProductPriceMonitorProductDo> updatedProducts = productsCaptor.getValue();
            assertEquals(2, updatedProducts.size());

            // Verify all products have error status
            for (ProductPriceMonitorProductDo product : updatedProducts) {
                assertEquals(MonitorProductPriceStatus.THIRD_PARTY_SERVER_ERROR.getValue(), product.getPriceStatus());
            }
        }

        @Test
        @DisplayName("Should handle exception during processing")
        void updateMonitorProductPrice_ExceptionHandling() {
            // Given
            setupCommonMocks();
            when(mmsThirdPartySkuHelper.fetchSkuOneBoundDetail(any(OneBoundDetailRequestDto.class)))
                    .thenThrow(new RuntimeException("Test exception"));

            // When
            productPriceMonitorService.updateMonitorProductPrice();

            // Then
            verify(productPriceMonitorProductRepository).saveAll(productsCaptor.capture());

            List<ProductPriceMonitorProductDo> updatedProducts = productsCaptor.getValue();
            assertEquals(2, updatedProducts.size());

            // Verify all products have error status
            for (ProductPriceMonitorProductDo product : updatedProducts) {
                assertEquals(MonitorProductPriceStatus.THIRD_PARTY_SERVER_ERROR.getValue(), product.getPriceStatus());
            }
        }

        @Test
        @DisplayName("Should do nothing when no enabled products found")
        void updateMonitorProductPrice_NoEnabledProducts() {
            // Given
			when(busUnitHelper.findBusinessId(HKTV)).thenReturn(BUSINESS_ID);
            when(productPriceMonitorProductRepository.findByActiveIndAndTargetPlatformAndBusUnitId(
                    anyInt(), anyString(), eq(BUSINESS_ID))).thenReturn(Collections.emptyList());

            // When
            productPriceMonitorService.updateMonitorProductPrice();

            // Then
            verify(productPriceMonitorProductRepository, times(0)).saveAll(anyList());
        }
    }

    /**
     * Helper method to find a product by ID in a list
     */
    private ProductPriceMonitorProductDo findProductById(List<ProductPriceMonitorProductDo> products, int id) {
        return products.stream()
                .filter(p -> p.getId() == id)
                .findFirst()
                .orElse(null);
    }
}
