package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FindGoodTypeService {

    public ResponseDto<List<String>> start() {
        List<String> list = List.of("authorized goods", "parallel goods");
        return ResponseDto.<List<String>>builder().data(list).status(1).build();
    }
}
