package com.shoalter.mms_product_api.service.product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.edit_column.TemplateTypeEnum;
import com.shoalter.mms_product_api.config.product.template.EditProductCommonlyUsedTemplateEnum;
import com.shoalter.mms_product_api.config.product.template.EditProductPackingInfoTemplateColumnEnum;
import com.shoalter.mms_product_api.dao.mapper.api.SaveProductRecordRowMapper;
import com.shoalter.mms_product_api.dao.mapper.api.pojo.ChildErrorMessage;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.bundle.helper.BundleErrorReportTemplateHelper;
import com.shoalter.mms_product_api.service.product.helper.EditLittleMallProductTemplateHelper;
import com.shoalter.mms_product_api.service.product.helper.EditProductCommonlyUsedTemplateHelper;
import com.shoalter.mms_product_api.service.product.helper.EditProductPackingInfoTemplateHelper;
import com.shoalter.mms_product_api.service.product.helper.LittleMallProductImportTemplateHelper;
import com.shoalter.mms_product_api.service.product.helper.LittleMallProductTemplateHelper;
import com.shoalter.mms_product_api.service.product.helper.EditProductTemplateHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductUploadExcelHelper;
import com.shoalter.mms_product_api.service.bundle.helper.SyncOfflineBundleTemplateHelper;
import com.shoalter.mms_product_api.service.product.helper.TmallProductExcelHelper;
import com.shoalter.mms_product_api.service.product.pojo.EditProductCommonlyUsedInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.CartonSizeDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbookType;
import org.springframework.context.MessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@Slf4j
@RequiredArgsConstructor
public class DownloadErrorReportService extends AbstractReport {

	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final StoreRepository storeRepository;

	private final SaveProductRecordRowMapper saveProductRecordRowMapper;
	private final EditProductCommonlyUsedTemplateHelper editProductCommonlyUsedTemplateHelper;
	private final LittleMallProductTemplateHelper littleMallProductTemplateHelper;
	private final EditLittleMallProductTemplateHelper editLittleMallProductTemplateHelper;
	private final LittleMallProductImportTemplateHelper littleMallProductImportTemplateHelper;
	private final ProductUploadExcelHelper productUploadExcelHelper;

	public final EditProductPackingInfoTemplateHelper editProductPackingInfoTemplateHelper;

	private final SyncOfflineBundleTemplateHelper syncOfflineBundleTemplateHelper;
	private final BundleErrorReportTemplateHelper bundleErrorReportTemplateHelper;
	private final TmallProductExcelHelper tmallProductExcelHelper;
	private final EditProductTemplateHelper editProductTemplateHelper;

	private final Gson gson;
	private final MessageSource messageSource;

	public HttpEntity<ByteArrayResource> start(UserDto userDto, Long recordId, Integer uploadType) {
		SaveProductRecordDo record = checkParameter(recordId, uploadType);

		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		Workbook workbook;
		switch (uploadType) {
			case SaveProductType.BATCH_CREATE_PRODUCT:
			case SaveProductType.SINGLE_CREATE_PRODUCT:
			case SaveProductType.SINGLE_CREATE_LITTLE_MALL_PRODUCT:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT:
				workbook = getCreateProductErrorReport(userDto, record);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT:
			case SaveProductType.SINGLE_EDIT_PRODUCT:
			case SaveProductType.SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT:
			case SaveProductType.SINGLE_EDIT_LITTLE_MALL_PRODUCT:
			case SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL:
			case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT:
				workbook = generateEditProductErrorReport(userDto, record);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_COMMONLY_USED:
				workbook = generateProductCommonlyUsedErrorReport(record);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_INFO:
				workbook = generateBatchEditProductPackingInfoErrorReport(record);
				break;
			case SaveProductType.SINGLE_CREATE_BUNDLE:
			case SaveProductType.SINGLE_EDIT_BUNDLE:
				workbook = generateBundleErrorReport(record);
				break;
			case SaveProductType.SYNC_OFFLINE_BUNDLE:
				workbook = generateSyncOfflineBundleErrorReport(record);
				break;
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL:
				workbook = getCreateShopLineProductErrorReport(record);
				break;
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL:
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL:
				workbook = getCreateTmallProductErrorReport(userDto, record);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION:
				workbook = editProductTemplateHelper.generateEditProductErrorReport(TemplateTypeEnum.PACKING_DIMENSION, record);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
			case SaveProductType.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB:
				workbook = editProductTemplateHelper.generateEditProductErrorReport(TemplateTypeEnum.SKU_PRICE, record);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
				workbook = editProductTemplateHelper.generateEditProductErrorReport(TemplateTypeEnum.ONLINE_STATUS, record);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY:
				workbook = editProductTemplateHelper.generateEditProductErrorReport(TemplateTypeEnum.VISIBILITY, record);
				break;
			case SaveProductType.BATCH_CRATE_BINDING_EXTENDED_WARRANTY:
			case SaveProductType.BATCH_DELETE_BINDING_EXTENDED_WARRANTY:
				workbook = editProductTemplateHelper.generateEditProductErrorReport(TemplateTypeEnum.EXTENDED_WARRANTY, record);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION:
				workbook = editProductTemplateHelper.generateEditProductErrorReport(TemplateTypeEnum.OVERSEA_RESERVE_REGION, record);
				break;
			case SaveProductType.LITTLE_MALL_FLATTEN_PRODUCTS:
				workbook = editLittleMallProductTemplateHelper.generateEditFlattenErrorReport(record);
				break;
			case SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE:
				workbook = editProductTemplateHelper.generateEditProductErrorReport(TemplateTypeEnum.HKTV_PRODUCT_TRANSLATE, record);
				break;
			case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT_COMMONLY_USED:
			default:
				throw new SystemException(messageSource.getMessage("message75", null, null));
		}

		//export
		try {
			workbook.write(outputStream);
			workbook.close();
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		}
		String xlsxFileName = record.getFileName().substring(0, record.getFileName().lastIndexOf(".") + 1) + XSSFWorkbookType.XLSX.getExtension();
		return new ResponseEntity<>(new ByteArrayResource(outputStream.toByteArray()), getResponseHeader(xlsxFileName), HttpStatus.OK);
	}

	private SaveProductRecordDo checkParameter(Long recordId, Integer uploadType) {
		return saveProductRecordRepository.findByIdAndUploadType(recordId, uploadType).orElseThrow(() -> new SystemException(messageSource.getMessage("message59", null, null)));
	}

	private Workbook getCreateProductErrorReport(UserDto userDto, SaveProductRecordDo record) {
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findErrorRowByRecordId(record.getId());
		if (CollectionUtil.isEmpty(rowList)) {
			throw new NoDataException();
		}

		List<SingleEditProductDto> singleEditProductDtoList = generateProductErrorReportData(record);
		SingleEditProductDto singleEditProductDto = singleEditProductDtoList.get(0);

		Workbook workbook;
		if (isLittleMallProduct(singleEditProductDto)) {
			String storefrontStoreCode = singleEditProductDto.getProduct().getAdditional().getLittleMall().getStoreCode();
			workbook = littleMallProductTemplateHelper.downloadLittleMallErrorReport(userDto, storefrontStoreCode, false, singleEditProductDtoList);
		} else {
			HktvProductDto hktvProductDto = singleEditProductDto.getProduct().getAdditional().getHktv();
			List<String> uuidList = singleEditProductDtoList.stream().map(singleEditProduct -> singleEditProduct.getProduct().getUuid()).collect(Collectors.toList());
			StoreDo storeDo = storeRepository.findHktvStoreByStoreCode(hktvProductDto.getStores()).orElseThrow(() -> new SystemI18nException("message69"));

			workbook = productUploadExcelHelper.start(userDto, storeDo.getId(), hktvProductDto.getContractNo(), uuidList,
					singleEditProductDtoList, new ArrayList<>(), true);
		}
		return workbook;
	}

	private Workbook generateProductCommonlyUsedErrorReport(SaveProductRecordDo record) {
		Workbook workbook = editProductCommonlyUsedTemplateHelper.start();
		addErrorColumn(workbook, EditProductCommonlyUsedTemplateEnum.values().length, false, true);
		setProductCommonlyUsedData(workbook, record);
		return workbook;
	}

	private Workbook generateBundleErrorReport(SaveProductRecordDo record) {
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findErrorRowByRecordId(record.getId());
		if (CollectionUtil.isEmpty(rowList)) {
			throw new NoDataException();
		}
		Workbook workbook = bundleErrorReportTemplateHelper.start(rowList);
		return workbook;
	}

	private Workbook generateSyncOfflineBundleErrorReport(SaveProductRecordDo record) {
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findErrorRowByRecordId(record.getId());
		if (CollectionUtil.isEmpty(rowList)) {
			throw new NoDataException();
		}
		Workbook workbook = syncOfflineBundleTemplateHelper.start(rowList);
		return workbook;
	}

	private Workbook generateEditProductErrorReport(UserDto userDto, SaveProductRecordDo record) {
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findErrorRowByRecordId(record.getId());
		if (CollectionUtil.isEmpty(rowList)) {
			throw new NoDataException();
		}

		List<SingleEditProductDto> singleEditProductDtoList = generateProductErrorReportData(record);
		SingleEditProductDto singleEditProductDto = singleEditProductDtoList.get(0);

		Workbook workbook;
		if (isLittleMallProduct(singleEditProductDto)) {
			String storefrontStoreCode = singleEditProductDto.getProduct().getAdditional().getLittleMall().getStoreCode();
			workbook = littleMallProductTemplateHelper.downloadLittleMallErrorReport(userDto, storefrontStoreCode, true, singleEditProductDtoList);
		} else {
			HktvProductDto hktvProductDto = singleEditProductDto.getProduct().getAdditional().getHktv();
			List<String> uuidList = singleEditProductDtoList.stream().map(singleEditProduct -> singleEditProduct.getProduct().getUuid()).collect(Collectors.toList());
			StoreDo storeDo = storeRepository.findHktvStoreByStoreCode(hktvProductDto.getStores()).orElseThrow(() -> new SystemI18nException("message69"));

			workbook = productUploadExcelHelper.start(userDto, storeDo.getId(), hktvProductDto.getContractNo(), uuidList,
					singleEditProductDtoList, new ArrayList<>(), true);
		}

		return workbook;
	}

	private Workbook generateBatchEditProductPackingInfoErrorReport(SaveProductRecordDo record) {
		Workbook workbook = editProductPackingInfoTemplateHelper.start();
		addErrorColumn(workbook, EditProductPackingInfoTemplateColumnEnum.values().length, false, true);
		setProductPackingInfoData(workbook, record);
		return workbook;
	}

	private void setProductCommonlyUsedData(Workbook workbook, SaveProductRecordDo record) {
		Sheet dataSheet = workbook.getSheet(EditProductCommonlyUsedTemplateHelper.SHEET_NAME);
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findErrorRowByRecordId(record.getId());

		CellStyle bodyStyle = ExcelUtil.createBodyStyle(workbook, HorizontalAlignment.CENTER, false, false, false, false, true);
		int rowIndex = 1;

		for (SaveProductRecordRowDo recordRow : rowList) {
			SingleEditProductDto singleEditProductDto = gson.fromJson(recordRow.getContent(), SingleEditProductDto.class);
			if (singleEditProductDto.getProduct() != null) {
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductCommonlyUsedTemplateEnum.SKU_CODE.getColumnNumber(), recordRow.getSku());
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductCommonlyUsedTemplateEnum.PRICE.getColumnNumber(), String.valueOf(singleEditProductDto.getProduct().getOriginalPrice()));
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductCommonlyUsedTemplateEnum.INVISIBLE.getColumnNumber(), singleEditProductDto.getProduct().getAdditional().getHktv().getVisibility());
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductCommonlyUsedTemplateEnum.ONLINE.getColumnNumber(), singleEditProductDto.getProduct().getAdditional().getHktv().getOnlineStatus().name());
			} else {
				EditProductCommonlyUsedInfoDto editProductCommonlyUsedInfoDto = gson.fromJson(recordRow.getContent(), EditProductCommonlyUsedInfoDto.class);
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductCommonlyUsedTemplateEnum.SKU_CODE.getColumnNumber(), recordRow.getSku());
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductCommonlyUsedTemplateEnum.PRICE.getColumnNumber(), String.valueOf(editProductCommonlyUsedInfoDto.getPrice()));
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductCommonlyUsedTemplateEnum.INVISIBLE.getColumnNumber(), editProductCommonlyUsedInfoDto.getInvisible());
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductCommonlyUsedTemplateEnum.ONLINE.getColumnNumber(), editProductCommonlyUsedInfoDto.getOnline().name());
			}
			setCellValue(dataSheet, bodyStyle, rowIndex, EditProductCommonlyUsedTemplateEnum.values().length, recordRow.getErrorMessage());
			rowIndex++;
		}
	}

	private List<SingleEditProductDto> generateProductErrorReportData(SaveProductRecordDo record) {
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findErrorRowByRecordId(record.getId());
		List<ChildErrorMessage> childErrorMessageList = saveProductRecordRowMapper.findParentErrorMessage(record.getId());
		Map<Long, String> errorMessage = childErrorMessageList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ChildErrorMessage::getParentProductRowId, ChildErrorMessage::getErrorMessage));

		return rowList.stream().map(recordRow -> {
			SingleEditProductDto singleEditProductDto = gson.fromJson(recordRow.getContent(), SingleEditProductDto.class);

			String childErrorMessage = errorMessage.get(recordRow.getId());
			if (StringUtils.isNotEmpty(childErrorMessage)) {
				recordRow.setErrorMessage(recordRow.getErrorMessage() + "\n" + childErrorMessage);
			}
			singleEditProductDto.setErrorMessage(recordRow.getErrorMessage());
			return singleEditProductDto;
		}).collect(Collectors.toList());
	}

	public HttpHeaders getResponseHeader(String fileName) {
		HttpHeaders header = new HttpHeaders();
		header.setContentType(new MediaType("application", "octet-stream"));
		header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
		return header;
	}

	private boolean isLittleMallProduct(SingleEditProductDto singleEditProductDto) {
		return singleEditProductDto.getProduct().getAdditional().getLittleMall() != null;
	}

	private void setProductPackingInfoData(Workbook workbook, SaveProductRecordDo record) {
		Sheet dataSheet = workbook.getSheet(EditProductPackingInfoTemplateHelper.SHEET_NAME);
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findErrorRowByRecordId(record.getId());

		CellStyle bodyStyle = ExcelUtil.createBodyStyle(workbook, HorizontalAlignment.CENTER, false, false, false,false,true);
		int rowIndex = 1;

		for (SaveProductRecordRowDo recordRow : rowList) {
			SingleEditProductDto singleEditProductDto = gson.fromJson(recordRow.getContent(), SingleEditProductDto.class);
			if (singleEditProductDto.getProduct() != null) {

				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.SKU_ID.getColumnNumber(), recordRow.getSku());
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.PACKING_SPEC_ENG.getColumnNumber(), singleEditProductDto.getProduct().getAdditional().getHktv().getPackingSpecEn());
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.PACKING_SPEC_CHI.getColumnNumber(), singleEditProductDto.getProduct().getAdditional().getHktv().getPackingSpecCh());
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.PACKING_SPEC_SC.getColumnNumber(), singleEditProductDto.getProduct().getAdditional().getHktv().getPackingSpecSc());
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.PACKING_HEIGHT.getColumnNumber(), String.valueOf(singleEditProductDto.getProduct().getPackingHeight()));
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.PACKING_LENGTH.getColumnNumber(), String.valueOf(singleEditProductDto.getProduct().getPackingLength()));
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.PACKING_DEPTH.getColumnNumber(), String.valueOf(singleEditProductDto.getProduct().getPackingDepth()));
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.PACKING_DIMENSION_UNIT.getColumnNumber(), singleEditProductDto.getProduct().getPackingDimensionUnit());
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.WEIGHT.getColumnNumber(), String.valueOf(singleEditProductDto.getProduct().getWeight()));
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.WEIGHT_UNIT.getColumnNumber(), singleEditProductDto.getProduct().getWeightUnit());
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.PACKING_BOX_TYPE.getColumnNumber(), singleEditProductDto.getProduct().getPackingBoxType());

				if(CollectionUtil.isNotEmpty(singleEditProductDto.getProduct().getCartonSizeList())) {
					CartonSizeDto cartonSizeDto = singleEditProductDto.getProduct().getCartonSizeList().get(0);
					setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.CARTON_HEIGHT.getColumnNumber(), String.valueOf(cartonSizeDto.getHeight()));
					setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.CARTON_DEPTH.getColumnNumber(), String.valueOf(cartonSizeDto.getWidth()));
					setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.CARTON_LENGTH.getColumnNumber(), String.valueOf(cartonSizeDto.getLength()));
				}else{
					setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.CARTON_HEIGHT.getColumnNumber(), "");
					setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.CARTON_DEPTH.getColumnNumber(), "");
					setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.CARTON_LENGTH.getColumnNumber(), "");
				}
				setCellValue(dataSheet, bodyStyle, rowIndex, EditProductPackingInfoTemplateColumnEnum.values().length, recordRow.getErrorMessage());

				rowIndex++;
			}
		}
	}

	private Workbook getCreateShopLineProductErrorReport(SaveProductRecordDo record) {
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findErrorRowByRecordId(record.getId());
		if (CollectionUtil.isEmpty(rowList)) {
			throw new NoDataException();
		}
		List<SingleEditProductDto> singleEditProductDtoList = generateProductErrorReportData(record);
		return littleMallProductImportTemplateHelper.downloadShopLineErrorReport(singleEditProductDtoList);
	}

	private Workbook getCreateTmallProductErrorReport(UserDto userDto, SaveProductRecordDo record) {
		List<SaveProductRecordRowDo> rowList = saveProductRecordRowRepository.findErrorRowByRecordId(record.getId());
		if (CollectionUtil.isEmpty(rowList)) {
			throw new NoDataException();
		}

		List<SingleEditProductDto> singleEditProductDtoList = generateProductErrorReportData(record);
		SingleEditProductDto singleEditProductDto = singleEditProductDtoList.get(0);

		HktvProductDto hktvProductDto = singleEditProductDto.getProduct().getAdditional().getHktv();
		List<String> uuidList = singleEditProductDtoList.stream().map(singleEditProduct -> singleEditProduct.getProduct().getUuid()).collect(Collectors.toList());
		StoreDo storeDo = storeRepository.findHktvStoreByStoreCode(hktvProductDto.getStores()).orElseThrow(() -> new SystemI18nException("message69"));

		return tmallProductExcelHelper.generateFromHktvData(userDto, storeDo.getId(), hktvProductDto.getContractNo(), uuidList, singleEditProductDtoList, true);
	}
}
