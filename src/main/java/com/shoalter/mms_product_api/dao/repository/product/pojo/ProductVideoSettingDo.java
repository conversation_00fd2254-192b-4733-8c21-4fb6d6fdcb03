package com.shoalter.mms_product_api.dao.repository.product.pojo;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
@Entity
@Table(name = "PRODUCT_VIDEO_SETTING")
@Data
public class ProductVideoSettingDo {
    @Id
    @Column(name = "FILE_TYPE")
    private String fileType;
    @Column(name = "START_AT")
    private String startAt;
    @Column(name = "END_AT")
    private String endAt;
    @Column(name = "IS_ENABLEAUDIO", columnDefinition="BIT")
    private Boolean isEnableaudio;
    @Column(name = "BITRATE")
    private Long bitrate;
    @Column(name = "HEIGHT")
    private Long height;
    @Column(name = "LIMIT_WEIGHT")
    private Integer limitWeight;
    @Column(name = "LIMIT_HEIGHT")
    private Integer limitHeight;
    @Column(name = "LIMIT_SIZE")
    private Integer limitSize;
    @Column(name = "MIN_DURATION")
    private Long minDuration;
    @Column(name = "MAX_DURATION")
    private Long maxDuration;
}

