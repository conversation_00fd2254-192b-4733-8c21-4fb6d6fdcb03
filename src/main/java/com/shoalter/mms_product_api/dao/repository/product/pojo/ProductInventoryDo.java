package com.shoalter.mms_product_api.dao.repository.product.pojo;

import java.util.Date;
import java.math.BigDecimal;
import lombok.Data;
import javax.persistence.*;
@Entity
@Table(name = "PRODUCT_INVENTORY")
@Data
public class ProductInventoryDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID")
    private Integer id;
    @Column(name = "PRODUCT_STORE_STATUS_ID")
    private Integer productStoreStatusId;
    @Column(name = "AUTO_REDISTRIBUTION")
    private String autoRedistribution;
    @Column(name = "REDISTRIBUTION_RATIO")
    private Integer redistributionRatio;
    @Column(name = "CREATED_BY")
    private String createdBy;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "LAST_UPDATED_BY")
    private String lastUpdatedBy;
    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;
    @Column(name = "STOCK_ROUNDING_FLAG")
    private String stockRoundingFlag;
    @Column(name = "SHARE_MODE", columnDefinition="TINYINT")
    private Integer shareMode;
}
