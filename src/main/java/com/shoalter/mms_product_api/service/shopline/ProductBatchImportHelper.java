package com.shoalter.mms_product_api.service.shopline;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.template.HktvUploadProductExcelReportEnum;
import com.shoalter.mms_product_api.config.product.template.LittleMallProductColumnEnum;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.ProductImageHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdByLittleMallStoreRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdResultDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallProductDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMigrationDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.UploadImageResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.UploadImageResultDataDto;
import com.shoalter.mms_product_api.service.product.pojo.UploadImageUrlResultData;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.BatchLittleMallProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallBatchDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductBatchImportHelper {

	private final ProductImageHelper productImageHelper;
	private final ProductMasterHelper productMasterHelper;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final Gson gson;
	private final MessageSource messageSource;
	private final Executor productImageServerExecutor;

	private static final String PM_SKU_ID_EXIST_ERROR_CODE = "SPMS-B-0009";


	public ResponseDto<Set<String>> checkLittleMallSkuIdFromProductMaster(UserDto userDto, BatchLittleMallProductRequestDto batchLittleMallProductRequestDto) {
		Set<String> repeatedSkuIds = new HashSet<>();
		List<String> errorMessages = new ArrayList<>();

		Map<String, List<String>> storeToSkuIdsMap = batchLittleMallProductRequestDto.getLittleMallBatchDtoList().stream()
			.filter(sku -> StringUtil.isNotEmpty(sku.getSkuId()))
			.collect(Collectors.groupingBy(LittleMallBatchDto::getStores, Collectors.mapping(LittleMallBatchDto::getSkuId, Collectors.toList())));

		//one store per request
		for (Map.Entry<String, List<String>> entry : storeToSkuIdsMap.entrySet()) {
			String storeCode = entry.getKey();
			List<String> skuIds = entry.getValue();
			CheckSkuIdByLittleMallStoreRequestDto request = new CheckSkuIdByLittleMallStoreRequestDto(storeCode, skuIds);
			CheckSkuIdResultDto checkSkuIdResultDto = productMasterHelper.requestCheckSkuIdByLittleMallStore(userDto, request);
			if (checkSkuIdResultDto == null) {
				errorMessages.add(messageSource.getMessage("message10", new String[]{ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_ERROR}, null));
				break;
			}

			if (checkSkuIdResultDto.getExistsSkus() != null) {
				repeatedSkuIds.addAll(checkSkuIdResultDto.getExistsSkus());
			}
		}
		return ResponseDto.generate(repeatedSkuIds, errorMessages);
	}

	public void handleSpecialRecordTypeUploadedImages(SaveProductRecordDo record, UserDto userDto, List<SaveProductRecordRowDo> saveProductRecordRows, SaveProductResultDto productMasterResult) {
		List<String> successUploadedImageIds = new ArrayList<>();

		if (SaveProductType.MIGRATION_PRODUCT_TYPE_SET.contains(record.getUploadType())) {
			saveProductRecordRows.forEach(row -> {
				SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
				successUploadedImageIds.addAll(singleEditProductDto.getProductMigration().getSuccessImgIds());
			});
		}

		if (successUploadedImageIds.isEmpty()) {
			return;
		}

		if (productMasterResult != null && StatusCodeEnum.SUCCESS.name().equalsIgnoreCase(productMasterResult.getStatus())) {
			productImageHelper.updateServerImageStatus(userDto.getUserCode(), successUploadedImageIds);
		}
	}

	public ResponseDto<Void> addLittleMallProductMigrationContent(UserDto userDto, SaveProductRecordRowDo row) {
		//prepare data
		SingleEditProductDto modifiedProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		LittleMallProductDto modifiedLittleMallProductDto = modifiedProductDto.getProduct().getAdditional().getLittleMall();
		String mainPhotoUrl = modifiedLittleMallProductDto.getMainPhoto();
		List<String> otherPhotos = modifiedLittleMallProductDto.getOtherPhoto();
		Document skuLongDescriptionDoc = Jsoup.parseBodyFragment(modifiedLittleMallProductDto.getSkuLongDescriptionCh());
		Elements imageElements = skuLongDescriptionDoc.select(StringUtil.HTML_IMG);
		List<Pair<String, LittleMallProductColumnEnum>> originalImageUrls = generateRequestImageUrls(mainPhotoUrl, otherPhotos, imageElements);

		//handle image urls
		ResponseDto<Map<String, UploadImageUrlResultData>> uploadResult = uploadImages(BuCodeEnum.LITTLE_MALL, row, userDto, modifiedProductDto, originalImageUrls);
		if (uploadResult.getStatus() == StatusCodeEnum.FAIL.getCode()) {
			return ResponseDto.fail(uploadResult.getErrorMessageList());
		}

		//replace content
		replaceImageUrls(modifiedLittleMallProductDto, uploadResult.getData(), mainPhotoUrl, imageElements, skuLongDescriptionDoc, otherPhotos);
		updateRecordRowContent(uploadResult, row, modifiedProductDto);

		return ResponseDto.success(null);
	}

	public ResponseDto<Void> addHktvProductMigrationContent(UserDto userDto, SaveProductRecordRowDo row) {
		//prepare data
		SingleEditProductDto modifiedProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		HktvProductDto hktvProductDto = modifiedProductDto.getProduct().getAdditional().getHktv();
		List<Pair<String, HktvUploadProductExcelReportEnum>> originalImageUrls = generateRequestImageUrls(hktvProductDto);

		//handle image urls
		ResponseDto<Map<String, UploadImageUrlResultData>> uploadResult = uploadImages(BuCodeEnum.HKTV, row, userDto, modifiedProductDto, originalImageUrls);
		if (uploadResult.getStatus() == StatusCodeEnum.FAIL.getCode()) {
			return ResponseDto.fail(uploadResult.getErrorMessageList());
		}

		//replace content
		replaceImageUrls(hktvProductDto, uploadResult.getData());
		updateRecordRowContent(uploadResult, row, modifiedProductDto);

		return ResponseDto.success(null);
	}

	private <T> ResponseDto<Map<String, UploadImageUrlResultData>> uploadImages(BuCodeEnum buCodeEnum, SaveProductRecordRowDo row, UserDto userDto, SingleEditProductDto modifiedProductDto,
																				List<Pair<String, T>> originalImageUrls) {
		UploadImageResponseDto requestData = productImageHelper.generateUploadImage(row.getSku(), modifiedProductDto.getProduct().getMerchantId(), buCodeEnum.name());
		ResponseDto<Map<String, UploadImageUrlResultData>> uploadResult = callImageServerAsync(userDto, originalImageUrls, requestData);

		if (uploadResult.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
			return ResponseDto.success(uploadResult.getData());
		}

		if (!uploadResult.getData().isEmpty()) {
			List<String> uploadedImageIdes = uploadResult.getData().values().stream()
				.map(UploadImageUrlResultData::getImageId)
				.collect(Collectors.toList());
			productImageHelper.requestDeleteImageByUrlIds(userDto.getUserCode(), uploadedImageIdes);
		}

		return ResponseDto.fail(uploadResult.getErrorMessageList());
	}

	private void updateRecordRowContent(ResponseDto<Map<String, UploadImageUrlResultData>> uploadResult, SaveProductRecordRowDo row,
										SingleEditProductDto modifiedProductDto) {
		Set<String> uploadedImageIdes = uploadResult.getData().values().stream()
			.map(UploadImageUrlResultData::getImageId)
			.collect(Collectors.toSet());
		SingleEditProductDto originalProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		originalProductDto.setProductMigration(new ProductMigrationDto(modifiedProductDto.getProduct(), uploadedImageIdes));
		row.setContent(gson.toJson(originalProductDto));
		saveProductRecordRowRepository.save(row);
	}

	private <T> ResponseDto<Map<String, UploadImageUrlResultData>> callImageServerAsync(UserDto userDto, List<Pair<String, T>> originalImageUrls, UploadImageResponseDto requestData) {
		//call image server, once per picture (async)
		List<CompletableFuture<Pair<String, UploadImageUrlResultData>>> futures = originalImageUrls.stream()
			.map(imageUrl -> CompletableFuture.supplyAsync(() -> callImageServer(userDto, imageUrl, requestData), productImageServerExecutor))
			.collect(Collectors.toList());
		CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
		CompletableFuture<List<Pair<String, UploadImageUrlResultData>>> imageResultFutures = allDoneFuture
			.thenApply(v -> futures.stream()
				.map(CompletableFuture::join)
				.collect(Collectors.toList()));
		List<Pair<String, UploadImageUrlResultData>> imageResults = imageResultFutures.join();

		//handle image server result
		Map<String, UploadImageUrlResultData> successImageUrlMap = new HashMap<>();
		List<String> errorMessages = new ArrayList<>();
		for (Pair<String, UploadImageUrlResultData> imageResult : imageResults) {
			String originalImageUrl = imageResult.getLeft();
			if (imageResult.getRight().getImageId() == null) {
				LittleMallProductColumnEnum littleMallImageType = imageResult.getRight().getLittleMallProductColumnEnum();
				HktvUploadProductExcelReportEnum hktvImageType = imageResult.getRight().getHktvUploadProductExcelReportEnum();
				List<String> singleMessageErrorMessages = imageResult.getRight().getErrorMessages();
				String columnName = littleMallImageType != null ? littleMallImageType.getColNameEnglish() : hktvImageType.getColumnName();
				errorMessages.add(messageSource.getMessage("message167", new String[]{originalImageUrl, columnName, singleMessageErrorMessages.toString()}, null));
			} else {
				successImageUrlMap.put(originalImageUrl, imageResult.getRight());
			}
		}

		return ResponseDto.generate(successImageUrlMap, errorMessages);
	}

	private <T> Pair<String, UploadImageUrlResultData> callImageServer(UserDto userDto, Pair<String, T> originalImageUrls, UploadImageResponseDto requestData) {
		String oldImageUrl = originalImageUrls.getLeft();
		ResponseDto<UploadImageResultDataDto> requestResult;

		UploadImageUrlResultData result;
		if (originalImageUrls.getRight() instanceof LittleMallProductColumnEnum) {
			LittleMallProductColumnEnum imageType = (LittleMallProductColumnEnum) originalImageUrls.getRight();
			requestResult = productImageHelper.imageUrlsUpload(userDto, oldImageUrl, requestData);
			result = UploadImageUrlResultData.builder()
				.littleMallProductColumnEnum(imageType)
				.build();
		} else {
			HktvUploadProductExcelReportEnum imageType = (HktvUploadProductExcelReportEnum) originalImageUrls.getRight();
			requestResult = productImageHelper.imageUrlsUpload(userDto, oldImageUrl, requestData);
			result = UploadImageUrlResultData.builder()
				.hktvUploadProductExcelReportEnum(imageType)
				.build();
		}

		if (StatusCodeEnum.FAIL.getCode() == requestResult.getStatus()) {
			result.setErrorMessages(requestResult.getErrorMessageList());
			return Pair.of(oldImageUrl, result);
		}

		result.setImageId(requestResult.getData().getId());
		result.setNewUrl(requestResult.getData().getUrl());
		return Pair.of(oldImageUrl, result);
	}

	private List<Pair<String, LittleMallProductColumnEnum>> generateRequestImageUrls(String mainPhotoUrl, List<String> otherPhotos, Elements imageElements) {
		List<Pair<String, LittleMallProductColumnEnum>> originalImageUrls = new ArrayList<>();
		originalImageUrls.add(Pair.of(mainPhotoUrl, LittleMallProductColumnEnum.MAIN_PHOTO));
		if (CollectionUtil.isNotEmpty(otherPhotos)) {
			otherPhotos.forEach(otherPhoto -> originalImageUrls.add(Pair.of(otherPhoto, LittleMallProductColumnEnum.OTHER_PHOTO)));
		}

		if (imageElements != null) {
			imageElements.stream()
				.map(img -> img.attr(StringUtil.HTML_SRC))
				.forEach(img -> originalImageUrls.add(Pair.of(img, LittleMallProductColumnEnum.SKU_LONG_DESCRIPTION_CHI)));
		}

		return originalImageUrls;
	}

	private List<Pair<String, HktvUploadProductExcelReportEnum>> generateRequestImageUrls(HktvProductDto hktvProductDto) {
		List<Pair<String, HktvUploadProductExcelReportEnum>> originalImageUrls = new ArrayList<>();
		originalImageUrls.add(Pair.of(hktvProductDto.getMainPhoto(), HktvUploadProductExcelReportEnum.MAIN_PHOTO_HKTVMALL));
		if (CollectionUtil.isNotEmpty(hktvProductDto.getOtherPhoto())) {
			hktvProductDto.getOtherPhoto().forEach(otherPhoto -> originalImageUrls.add(Pair.of(otherPhoto, HktvUploadProductExcelReportEnum.OTHER_PHOTO_HKTVMALL)));
		}
		if (CollectionUtil.isNotEmpty(hktvProductDto.getVariantProductPhoto())) {
			hktvProductDto.getVariantProductPhoto().forEach(otherPhoto -> originalImageUrls.add(Pair.of(otherPhoto, HktvUploadProductExcelReportEnum.OTHER_PRODUCT_PHOTO_HKTVMALL)));
		}
		if (StringUtil.isNotEmpty(hktvProductDto.getAdvertisingPhoto())) {
			originalImageUrls.add(Pair.of(hktvProductDto.getAdvertisingPhoto(), HktvUploadProductExcelReportEnum.ADVERTISING_PHOTO_HKTVMALL));
		}

		return originalImageUrls;
	}

	private void replaceImageUrls(LittleMallProductDto modifiedLittleMallProductDto, Map<String, UploadImageUrlResultData> successImageUrlMap,
								  String mainPhotoUrl, Elements imageElements, Document skuLongDescriptionDoc,
								  List<String> otherPhotos) {
		modifiedLittleMallProductDto.setMainPhoto(successImageUrlMap.get(mainPhotoUrl).getNewUrl());

		if (imageElements != null && skuLongDescriptionDoc != null) {
			imageElements.stream()
				.filter(img -> successImageUrlMap.containsKey(img.attr(StringUtil.HTML_SRC)))
				.forEach(img -> img.attr(StringUtil.HTML_SRC, successImageUrlMap.get(img.attr(StringUtil.HTML_SRC)).getNewUrl()));

			String modifiedHtml = StringUtil.replaceEmojiWithDecimalNCRs(skuLongDescriptionDoc.body().html());
			modifiedLittleMallProductDto.setSkuLongDescriptionCh(modifiedHtml);
		}

		if (CollectionUtil.isNotEmpty(otherPhotos)) {
			List<String> newOtherPhotos = otherPhotos.stream()
				.filter(successImageUrlMap::containsKey)
				.map(originalPhotoUrl -> successImageUrlMap.get(originalPhotoUrl).getNewUrl())
				.collect(Collectors.toList());
			modifiedLittleMallProductDto.setOtherPhoto(newOtherPhotos);
		}
	}

	private void replaceImageUrls(HktvProductDto hktvProductDto, Map<String, UploadImageUrlResultData> successImageUrlMap) {
		hktvProductDto.setMainPhoto(successImageUrlMap.get(hktvProductDto.getMainPhoto()).getNewUrl());

		if (StringUtil.isNotEmpty(hktvProductDto.getAdvertisingPhoto()) && successImageUrlMap.containsKey(hktvProductDto.getAdvertisingPhoto())) {
			hktvProductDto.setAdvertisingPhoto(successImageUrlMap.get(hktvProductDto.getAdvertisingPhoto()).getNewUrl());
		}

		if (CollectionUtil.isNotEmpty(hktvProductDto.getOtherPhoto())) {
			List<String> newOtherPhotos = hktvProductDto.getOtherPhoto().stream()
				.filter(successImageUrlMap::containsKey)
				.map(originalPhotoUrl -> successImageUrlMap.get(originalPhotoUrl).getNewUrl())
				.collect(Collectors.toList());
			hktvProductDto.setOtherPhoto(newOtherPhotos);
		}

		if (CollectionUtil.isNotEmpty(hktvProductDto.getVariantProductPhoto())) {
			List<String> newVariantPhotos = hktvProductDto.getVariantProductPhoto().stream()
				.filter(successImageUrlMap::containsKey)
				.map(variantPhotoUrl -> successImageUrlMap.get(variantPhotoUrl).getNewUrl())
				.collect(Collectors.toList());
			hktvProductDto.setVariantProductPhoto(newVariantPhotos);
		}
	}

}
