package com.shoalter.mms_product_api.config.impact;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ShippingLengthUnitEnum {

	IN("in"),
	CM("cm");

	private final String field;

	@JsonValue
	public String value() {
		return field;
	}

	@JsonCreator
	public static ShippingLengthUnitEnum of(String value) {
		for (ShippingLengthUnitEnum target : ShippingLengthUnitEnum.values()) {
			if (Objects.equals(target.value(), value)) {
				return target;
			}
		}
		return null;
	}

	public static boolean contains(String value) {
		if (value == null) {
			return false;
		}
		return Arrays.stream(ShippingLengthUnitEnum.values()).anyMatch(target -> Objects.equals(target.field, value));
	}
}
