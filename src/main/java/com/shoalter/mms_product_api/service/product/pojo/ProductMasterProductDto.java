package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.config.security.xss.XssStringDeserializer;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleSettingRequestDto;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class ProductMasterProductDto implements Serializable {

	private String uuid;
	@JsonProperty("brand_id")
	@SerializedName("brand_id")
	private Integer brandId;
	@JsonProperty("merchant_id")
	@SerializedName("merchant_id")
	private Integer merchantId;
	@JsonProperty("sku_id")
	@SerializedName("sku_id")
	private String skuId;
	@JsonProperty("manufactured_country")
	@SerializedName("manufactured_country")
	private String manufacturedCountry;
	@JsonProperty("colour_families")
	@SerializedName("colour_families")
	private String colourFamilies;
	private String color;
	@JsonProperty("size_system")
	@SerializedName("size_system")
	private String sizeSystem;
	private String size;
	private String option1;
	@JsonProperty("option1_value")
	@SerializedName("option1_value")
	private String option1Value;
	private String option2;
	@JsonProperty("option2_value")
	@SerializedName("option2_value")
	private String option2Value;
	private String option3;
	@JsonProperty("option3_value")
	@SerializedName("option3_value")
	private String option3Value;
	private List<ProductBarcodeDto> barcodes;
	@JsonProperty("packing_height")
	@SerializedName("packing_height")
	private BigDecimal packingHeight;
	@JsonProperty("packing_length")
	@SerializedName("packing_length")
	private BigDecimal packingLength;
	@JsonProperty("packing_depth")
	@SerializedName("packing_depth")
	private BigDecimal packingDepth;
	@JsonProperty("packing_dimension_unit")
	@SerializedName("packing_dimension_unit")
	private String packingDimensionUnit;
	private BigDecimal weight;
	@JsonProperty("weight_unit")
	@SerializedName("weight_unit")
	private String weightUnit;
	@JsonProperty("storage_temperature")
	@SerializedName("storage_temperature")
	private String storageTemperature;
	@JsonProperty("packing_box_type")
	@SerializedName("packing_box_type")
	private String packingBoxType;
	@JsonProperty("original_price")
	@SerializedName("original_price")
	private BigDecimal originalPrice;
	@JsonProperty("product_id")
	@SerializedName("product_id")
	private String productId;
	@JsonProperty("minimum_shelf_life")
	@SerializedName("minimum_shelf_life")
	private Integer minimumShelfLife;
	@JsonProperty("merchant_name")
	@SerializedName("merchant_name")
	private String merchantName;
	@JsonProperty("sku_name_en")
	@SerializedName("sku_name_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuNameEn;
	@JsonProperty("sku_name_ch")
	@SerializedName("sku_name_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuNameCh;
	@JsonProperty("sku_name_sc")
	@SerializedName("sku_name_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuNameSc;
	@JsonProperty("applicable_service")
	@SerializedName("applicable_service")
	private List<String> applicableService;
	@JsonProperty("carton_size")
	@SerializedName("carton_size")
	private List<CartonSizeDto> cartonSizeList;

	// 其他bu特有資料
	private BuProductDto additional;

	//bundle product 特有欄位
	@JsonProperty("bundle_setting")
	@SerializedName("bundle_setting")
	private BundleSettingRequestDto bundleSetting;

	//bundle product 特有欄位
	@JsonProperty("mms_create_user")
	@SerializedName("mms_create_user")
	private String mmsCreateUser;

	//bundle product 特有欄位
	@JsonProperty("mms_create_time")
	@SerializedName("mms_create_time")
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	private LocalDateTime mmsCreateTime;

	@JsonProperty("mms_modified_user")
	@SerializedName("mms_modified_user")
	private String mmsModifiedUser;

	@JsonProperty("mms_modified_time")
	@SerializedName("mms_modified_time")
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	private LocalDateTime mmsModifiedTime;
}
