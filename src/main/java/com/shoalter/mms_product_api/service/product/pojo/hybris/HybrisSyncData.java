package com.shoalter.mms_product_api.service.product.pojo.hybris;

import com.shoalter.mms_product_api.config.hybris.HybrisAction;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.pojo.BusinessPlatformDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreStatusHistoryDo;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.ExchangeRatePriceDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterMqDto;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HybrisSyncData {

	private UserDto userDto;
	private BusinessPlatformDo busUnitModelDo;
	private ProductDo productDo;
	private ProductMasterMqDto productMasterMqDto;
	private boolean isPromotionChanged;
	private String action;
	private ProductStoreStatusHistoryDo productStoreStatusHistoryDo;
	private ExchangeRatePriceDto exchangeRatePriceDto;


	public HybrisSyncData generateCreateSyncData(UserDto userDto, BusinessPlatformDo busUnitModel, ProductDo productDo, ProductMasterMqDto productMasterMqDto,
												 Integer uploadType, ExchangeRatePriceDto exchangeRatePriceDto) {
		return HybrisSyncData.builder()
			.userDto(userDto)
			.busUnitModelDo(busUnitModel)
			.productDo(productDo)
			.productMasterMqDto(productMasterMqDto)
			.isPromotionChanged(isPromotionChanged(productMasterMqDto, new ProductDo()))
			.action(HybrisAction.getAction(uploadType))
			.productStoreStatusHistoryDo(null)
			.exchangeRatePriceDto(exchangeRatePriceDto)
			.build();
	}

	public HybrisSyncData generateUpdateSyncData(UserDto userDto, BusinessPlatformDo busUnitModel, ProductDo productDo, ProductMasterMqDto productMasterMqDto,
												 Integer uploadType, ProductStoreStatusHistoryDo productStoreStatusHistoryDo, ExchangeRatePriceDto exchangeRatePriceDto) {

		return HybrisSyncData.builder()
			.userDto(userDto)
			.busUnitModelDo(busUnitModel)
			.productDo(productDo)
			.productMasterMqDto(productMasterMqDto)
			.isPromotionChanged(this.isPromotionChanged)
			.action(HybrisAction.getAction(uploadType))
			.productStoreStatusHistoryDo(productStoreStatusHistoryDo)
			.exchangeRatePriceDto(exchangeRatePriceDto)
			.build();
	}

	public HybrisSyncData generateBundleSyncData(UserDto userDto, BusinessPlatformDo busUnitModel, ProductDo productDo, ProductMasterMqDto productMasterMqDto,
												 String action, ExchangeRatePriceDto exchangeRatePriceDto) {
		return HybrisSyncData.builder()
			.userDto(userDto)
			.busUnitModelDo(busUnitModel)
			.productDo(productDo)
			.productMasterMqDto(productMasterMqDto)
			.isPromotionChanged(false)
			.action(action)
			.productStoreStatusHistoryDo(null)
			.exchangeRatePriceDto(exchangeRatePriceDto)
			.build();
	}


	public boolean isPromotionChanged(ProductMasterMqDto productMasterMqDto, ProductDo productDo) {
		BigDecimal sellingPrice = productDo.getSellingPrice() != null ? productDo.getSellingPrice() : new BigDecimal("0");
		String discountText = StringUtil.getValidationValue(productDo.getDiscountText());
		String discountTextChi = StringUtil.getValidationValue(productDo.getDiscountTextTchi());
		String discountTextSchi = StringUtil.getValidationValue(productDo.getDiscountTextSchi());
		String style = StringUtil.getValidationValue(productDo.getStyle());
		BigDecimal originalPrice = productDo.getOriginalPrice() != null ? productDo.getOriginalPrice() : new BigDecimal("0");

		BigDecimal newSellingPrice = productMasterMqDto.getSellingPrice() != null ? productMasterMqDto.getSellingPrice() : new BigDecimal("0");
		String currDiscountText = StringUtil.getValidationValue(productMasterMqDto.getDiscountTextEn());
		String currDiscountTextChi = StringUtil.getValidationValue(productMasterMqDto.getDiscountTextCh());
		String currDiscountTextSchi = StringUtil.getValidationValue(productMasterMqDto.getDiscountTextSc());
		String currStyle = StringUtil.getValidationValue(productMasterMqDto.getStyle());
		BigDecimal currOriginalPrice = productMasterMqDto.getOriginalPrice() != null ? productMasterMqDto.getOriginalPrice() : new BigDecimal("0");

		return newSellingPrice.compareTo(sellingPrice) != 0
			|| !discountText.equalsIgnoreCase(currDiscountText)
			|| !discountTextChi.equals(currDiscountTextChi)
			|| !discountTextSchi.equals(currDiscountTextSchi)
			|| !style.equals(currStyle)
			|| originalPrice.compareTo(currOriginalPrice) != 0;
	}

	public boolean isNeedToUpdateDiscountPriceAndSyncHybris() {
		return HybrisAction.UPDATE_DISCOUNT_ACTIONS.contains(this.action) && this.isPromotionChanged;
	}
}
