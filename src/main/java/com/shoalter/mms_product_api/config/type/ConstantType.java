package com.shoalter.mms_product_api.config.type;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@SuppressWarnings("all")
public class ConstantType {
	private ConstantType() {

	}

	public static final String EXCEL_VALIDATION_FORMAT = "%s : %s";
	public static final String EXCEL_VALIDATION_CATEGORY_FORMAT = "%s_%s : %s_%s";

	// Notification
	public static final String HKTVMALL = "HKTVmall";
	public static final String PRODUCT_INVENTORY = "product_inventory";
	public static final String LANGUAGE_EN = "en";
	public static final String LANGUAGE_ZH_HK = "zh-hk";
	public static final String LANGUAGE_ZH_CN = "zh-cn";
	public static final String NOTIFICATION_MAIL_FROM_NAME = "mms";
	public static final String NOTIFICATION_MAIL_FROM_ADDRESS = "<EMAIL>";
	public static final String NOTIFICATION_MAIL_REPORT_TO_NAME = "Daily Report";
	public static final String APP_ID = "merchant.threepl";
	public static final String BUTTON_CLICK_NOW_EN = "Check Now";
	public static final String BUTTON_CLICK_NOW_ZH = "立即查看";
	public static final String BUTTON_CLICK_NOW_CN = "立即查看";
	public static final String FILE_TEMP_PROPERTY = "java.io.tmpdir";
	public static final String DATE_FORMAT_YMD = "yyyyMMdd";
	public static final String PARAMETER_KEY_DATE_YMD = "YYYYMMDD";

	//constant yes or no
	public static final String CONSTANT_YES = "Y";
	public static final String CONSTANT_FULL_YES = "Yes";
	public static final String CONSTANT_NO = "N";
	public static final String CONSTANT_FULL_NO = "No";

	//product status
	public static final String PRODUCT_STATUS_APPROVED = "APPROVED";

	// error  type
	public static final String ERROR_TYPE_DANGER = "danger";
	public static final String ERROR_TYPE_SUCCESS = "success";
	public static final String ERRORMESSAGE_SUCCESS = "Saved_Successfully";

	public static final String ERROR_RESPONSE_SUCCESS = "success";
	public static final String ERROR_RESPONSE_FAIL = "fail";
	public static final String ERROR_RESPONSE_FAILED = "failed";

	public static final String ACTIVE = "Active";

	public static final String FAIL = "FAIL";
	public static final String READY = "READY";
	public static final String PROCESSING = "PROCESSING";
	// version
	public static final String VERSION_STAGING = "STAGING";
	public static final String VERSION_ONLINE = "ONLINE";


	//user for _BARCODE or IMAGE
	//PRODUCT STATUS APPROVED_IN_RETEK--A UNAPPROVED_IN_RETEK--F
	public static final String PRODUCT_STATUS_APPROVED_IN_RETEK = "APPROVED_IN_RETEK";
	public static final String PRODUCT_STATUS_UNAPPROVED_IN_RETEK = "UNAPPROVED_IN_RETEK";
	//A Accepted; F Failed; P Processing
	public static final String PRODUCT_STATUS_RETEK_A = "A";
	public static final String PRODUCT_STATUS_RETEK_F = "F";
	public static final String PRODUCT_STATUS_RETEK_P = "P";

	public static final String PRODUCT_IMAGE_TYPE = "PRODUCT_IMAGE";
	//swatch
	public static final String SWATCH_IMAGE_TYPE = "SWATCH_IMAGE";
	public static final String JOB_USER_NAME = "JOB";

	public static final String LOGIN_FAILURE_TYPE = "LOGIN_FAILURE_TYPE";
	public static final String LOGIN_FAILURE_TYPE_DISABLED = "DISABLED";
	public static final String LOGIN_FAILURE_TYPE_BADCREDENTIALS = "BADCREDENTIALS";

	public static final String PROCESS = "process";
	public static final String ARCHIVE = "archive";
	public static final String ERROR = "error";
	public static final String SUPPLIER_DIRECT_DELIVERY = "SUPPLIER_DIRECT_DELIVERY";
	public static final String CONSIGNMENT_VIA_WAREHOUSE = "CONSIGNMENT_VIA_WAREHOUSE";
	public static final String CONSIGNMENT = "CONSIGNMENT";

	public static final String LOGIN_FAILURE_NAME = "LOGIN_FAILURE_NAME";
	public static final String LOGIN_LANGUAGE = "LOGIN_LANGUAGE";

	public static final String LOGIN_CAPTCHA = "loginCaptcha";

	public static final String SYSTEM_SUPPLIER_LIST = "SYSTEM_SUPPLIER_LIST";
	public static final String SYSTEM_SUPPLIER_MAP = "SYSTEM_SUPPLIER_MAP";

	public static final String SYSTEM_SHOP_LIST = "SYSTEM_SHOP_LIST";
	public static final String SYSTEM_SHOP_MAP = "SYSTEM_SHOP_MAP";

	//OPP
	public static final String DRAFT = "D";
	public static final String SUBMITTED = "S";
	public static final String REJECT = "R";
	public static final String APPROVED = "A";
	public static final String ACKNOWLEDGE = "AC";
	public static final String COMPLETED = "C";
	public static final String NEW = "N";
	public static final String CANCEL = "CL";
	public static final String PAYMENT = "P";

	//role type
	public static final String ROLE_TYPE_SYSTEM = "S";
	public static final String ROLE_TYPE_MERCHANT = "M";

	public static final String FOOD_BRAND_ADMIN = "FOOD_BRAND_ADMIN";
	public static final String FOOD_SUPER_MERCHANT = "FOOD_SUPER_MERCHANT";
	public static final String WORKFLOW_TYPE_OPP = "OPP";
	public static final String WORKFLOW_TYPE_PRODUCT = "PRODUCT";
	public static final String WORKFLOW_PRODUCT_APPROVAL = "Product Approval";

	/*
		Product 相关
	 */
	public static final String HKTV = "HKTV";
	public static final String HKB = "HKB";
	public static final String Y = "Y";
	public static final String N = "N";
	public static final String PRODUCT_CATEGORY_MMS = "MMS";
	public static final String PRODUCT_CATEGORY_STORE = "STORE";
	public static final String BATCH_UPLOAD_HKTV_CATEGORYIDS = "HKTVCATEGORYIDS";
	public static final String BATCH_UPLOAD_HKB_CATEGORYIDS = "HKBCATEGORYIDS";
	public static final String BATCH_UPLOAD_HKTV_PRIMARY_CATEGORYIDS = "HKTVPRIMARYCATEGORYIDS";
	public static final String BATCH_UPLOAD_HKB_PRIMARY_CATEGORYIDS = "HKBPRIMARYCATEGORYIDS";
	public static final String BATCH_UPLOAD_BRANDS = "BATCHUPLOADBRANDS";
	public static final String BATCH_UPLOAD_MANUCOUNTRIES = "BATCHUPLOADMANUCOUNTRIES";
	public static final int PRODUCT_UPLOAD_MAX_SIZE = 10000;

	//group
	public static final String GROUP_CATEGORY_TREE_PROUICT_TERMS_ = "GROUP_CATEGORY_TREE_PROUICT_TERMS_";
	public static final String GROUP_TREE_LIST_FOR_STORE = "GROUP_TREE_LIST_FOR_STORE";
	public static final String GROUP_TREE_LIST_FOR_PRODUCT = "GROUP_TREE_LIST_FOR_PRODUCT";
	public static final String GROUP_BU_CATEGORY_LIST = "GROUP_BU_CATEGORY_LIST";
	public static final String GROUP_CATEGORY_LIST = "GROUP_CATEGORY_LIST";
	public static final String GROUP_PACKAGE_COLOR_LIST = "GROUP_PACKAGE_COLOR_LIST";
	public static final String GROUP_BRAND_LIST = "GROUP_BRAND_LIST";
	public static final String GROUP_VIEW_BRAND = "GROUP_VIEW_BRAND";
	public static final String ADD = "ADD";
	public static final String EDIT = "EDIT";

	public static int SEC_24_HOURS = 86400;
	public static int SEC_8_HOURS = 28800;
	public static int SEC_4_HOURS = 14400;
	public static int SEC_12_HOURS = 43200;
	public static int SEC_2_HOUR = 7200;
	public static int SEC_1_HOUR = 3600;

	public static int SEC_30_MINTUES = 1800;
	public static int MAX_LENGTH_CELL = 32767;

	public static final String GROUP_LOCK_PRODUCT_CODE = "GROUP_LOCK_PRODUCT_CODE";
	public static final String GROUP_PRODUCT_RUN_TIME = "GROUP_PRODUCT_RUN_TIME";


	//job
	public static final String MANAGE_SUPP_JOB = "MANAGE_SUPPLEMENTARY_JOB";
	public static final String MANAGE_RE_ASSIGNMENT = "MANAGE_RE_ASSIGNMENT";
	public static final String MESSAGE_SEND_JOB = "MESSAGE_SEND_JOB";
	public static final String MANAGE_RENEW_CONTRACT_JOB = "MANAGE_RENEW_CONTRACT_JOB";
	public static final String DAILY_ORDER_STATUS_REPORT_JOB = "DAILY_ORDER_STATUS_REPORT_JOB";
	public static final String COMMAND = "COMMAND";


	public static final String JOB_START = "S";
	public static final String JOB_FAIL = "F";
	public static final String JOB_COMPLETED = "C";
	public static final String JOB_SKIP = "SKIP";
	public static final String IMPORT_ORDER_JOB = "ImportOrderJob";
	public static final String UPDATE_ORDER_JOB = "UpdateOrderJob";
	public static final String PCR_JOB = "PCR_JOB";
	public static final String ASSIGN_GOODS_ISSUE_COST_JOB = "AssignGoodsIssueCostJob";
	public static final String PAYMENT_REGISTER_JOB = "PaymentRegisterJob";
	public static final String PROMTIONAL_PRODUCT_JOB = "PromtionalProductJob";
	public static final String DEDUPLICATE_REPORT_JOB = "DeduplicateReportJob";
	public static final String MANAGE_COMMAND_JOB = "ManageCommandJob";
	public static final String SKU_FIXED_COST_REPORT_JOB = "SkuFixedCostReportJob";
	public static final String STORE_PERFORMANCE_REPORT_JOB = "StorePerformanceReportJob";
	public static final String RAW_DATA_REPORT_JOB = "RawDataReportJob";
	public static final String MANAGE_GENERATE_CONTACT_FOR_SALESFORCE = "GenerateContactForSalesforces";
	public static final String MANAGE_GENERATE_REPORT_WITH_CUSTOM_PARAMETER = "ManageGenerateReportWithCustomParameter";
	public static final String MANAGE_COMMAND = "ManageCommand";
	public static final String SYNC_PROMOTION_JOB = "SyncPromotionJob";
	public static final String SUPER_FOLLOW_FINANCE_REPORT_JOB = "SuperFollowFinanceReportJob";
	public static final String GRADE_MERCHANT_REPORT_JOB = "GradeMerchantReportJob";
	public static final String GRADE_ACTIVE_STORE_JOB = "GradeActiveStoreJob";
	public static final String OFFER_SUBMISSION_CAMPAIGN_STATUS_JOB = "OfferSubmissionCampaignStatusJob";
	public static final String OFFER_SUBMISSION_EMAIL_REMINDER_JOB = "OfferSubmissionEmailReminderJob";
	public static final String CLEAR_CACHE_JOB = "ClearCacheJob";
	public static final String SHIPANY_MERCHANT_JOB = "ShipAnyMerchantJob";
	public static final String GENERATE_EESE_MONTHLY_DAMAGE_LOST_FINANCE_REPORT_JOB = "GenerateEESEMonthlyDamageLostFinanceReportJob";
	public static final String MONTHLY_ANNUAL_FEE_MABS_JOB = "MonthlyAnnualFeeMabsJob";
	public static final String RETRY_RABBITMQ_SERVICE = "RetryRabbitMQService";

	public static final String MERCHANT_BANK_STATUS_NEW = "New";
	public static final String MERCHANT_BANK_STATUS_APPROVED = "A";
	public static final String MERCHANT_BANK_STATUS_REJECTED = "R";

	public static final String SYNC_ACTION_INSERT_UPDATE = "INSERT_UPDATE";

	//additional offer
	public static final String ERROR_DATA_TYPE_BATCH = "BATCH";
	public static final String ERROR_DATA_TYPE_RECORD = "RECORD";

	public static final String SYNC_ACTION_DELETE = "DELETE";
	public static final String SYNC_ACTION_UPDATE = "UPDATE";

	public static final String ORDER_OUTSTANDING = "OUTSTANDING";

	public static final String ORDER_COMPLETED = "COMPLETED";
	public static final String ORDER_REDEEMED = "REDEEMED";
	public static final String ORDER_BREAKAGE = "BREAKAGE";
	public static final String ORDER_LATE_REDEMPTION = "LATE_REDEMPTION";
	public static final String ORDER_EXPIRED = "EXPIRED";
	public static final String ORDER_REFUND = "REFUND";

	public static final String ORDER_CANCELLED = "CANCELLED";
	public static final String ORDER_RETURN = "RETURN";

	public static final String ORDER_COMPLETED_AND_CANCEL = "COMPLETED_AND_CANCEL";
	public static final String ORDER_COMPLETED_AND_RETURN = "COMPLETED_AND_RETURN";
	public static final String ORDER_OUTSTANDING_AND_CANCEL = "OUTSTANDING_AND_CANCEL";
	public static final String ORDER_OUTSTANDING_AND_FULLY_CANCEL = "OUTSTANDING_AND_FULLY_CANCEL";

	public static final String ORDER_REDEEM_AND_CANCEL = "REDEEM_AND_CANCEL";
	public static final String ORDER_BREAKAGE_AND_CANCEL = "BREAKAGE_AND_CANCEL";
	public static final String ORDER_LATE_REDEMPTION_AND_CANCEL = "LATE_REDEMPTION_AND_CANCEL";

	public static final List<String> PCR_CANCELLED_STATUS_LIST = Arrays
			.asList(ORDER_CANCELLED, ORDER_COMPLETED_AND_CANCEL, ORDER_OUTSTANDING_AND_CANCEL, ORDER_REDEEM_AND_CANCEL, ORDER_BREAKAGE_AND_CANCEL, ORDER_LATE_REDEMPTION_AND_CANCEL);

	public static final List<String> PCR_COMPLETED_STATUS_LIST = Collections.singletonList(ORDER_COMPLETED);
	public static final List<String> PCR_OUTSTANDING_STATUS_LIST = Collections.singletonList(ORDER_OUTSTANDING);

	public static final String PG_11 = "PG_11";
	public static final String PG_12 = "PG_12";
	public static final String PG_13 = "PG_13";
	public static final String PG_14 = "PG_14";
	public static final String PG_15 = "PG_15";
	public static final String PG_21 = "PG_21";
	public static final String PG_36 = "PG_36";
	public static final String PG_37 = "PG_37";
	public static final String PG_38 = "PG_38";

	public static final String MMS_TO_FDH_FILE_NAME = "MMS-FDH";
	public static final String UNAPPROVED_PCR_REPORT_NAME = "UNAPPROVED_PCR_REPORT";
	public static final String BANK_CHANGES_REPORT_NAME = "BANK_CHANGES_REPORT";
	public static final String MMS_TO_GROUP7_FILE_NAME = "MMS-GROUP7";

	public static final String MMS_TO_FIN_FILE_NAME = "MMS-FINNAS";
	public static final String MMS_TO_EESE_FINNAS = "MMS-EESEFINNAS";
	public static final String MMS_TO_MABS_FILE_NAME = "MMS-MABS";

	public static final int DAY_1 = 1;
	public static final int DAY_11 = 11;
	public static final int DAY_16 = 16;
	public static final int DAY_21 = 21;

	public static final String PAYMENT_GROUP_C = "C";
	public static final String PAYMENT_GROUP_F = "F";
	public static final String PAYMENT_GROUP_M = "M";

	public static final String PAYMENT_TERMS_A = "A"; //Pay by Redemption
	public static final String PAYMENT_TERMS_B = "B";   //Pay by Voucher Sold
	public static final String PAYMENT_TERMS_C = "C"; //Pay by Redemption + Breakage
	public static final String PAYMENT_TERMS_D = "D"; //Pay by Completion, Delivered and Passed Return Period

	// parm
	public static final String REMOVAL_SERVICE = "CATEGORY_FOR_REMOVAL_SERVICE";
	public static final String GOODS_TYPE = "CATEGORY_FOR_GOODS_TYPE";

	public static final String EXCEL_BATCH_UPLOAD_VO_CLASS_NAME_PROMOTION_PRODUCT = "PromotionProductBatchUploadVo";

	public static final String REGEX_CONDITION_NO_SPECIAL_CHARACTER = "[a-zA-Z0-9_-]+";

	//promotion
	public static final String THRESHOLD_TYPE_QUANTITY = "QUANTITY";
	public static final String THRESHOLD_TYPE_AMOUNT = "AMOUNT";
	public static final String DISCOUNT_TYPE_FIXED_AMOUNT = "FIXED_AMOUNT";
	public static final String DISCOUNT_TYPE_PERCENTAGE = "PERCENTAGE";
	public static final String PROMOTION_CODE_PREFIX = "MMS_MD";
	public static final List<String> BATCH_UPLOAD_PROMOTION_PRODUCT_FILE_HEADER = Arrays.asList("SKU ID ( Full SKU ID with Store ID e.g : H0001001_S_ABC )");


	public static final List<String> IMAGE_SIZE = Arrays.asList("30", "65", "96", "300", "365", "365", "515", "1200");

	public static final String REGEX_CONDITION_PRODUCT_CODE = "[a-zA-Z0-9-_]+";


	public static final String FILENAME_MMS = "MMS";
	public static final String FILENAME_HYPHEN = "-";
	public static final String FILENAME_FINANCE_NAS = "FINNAS";

	public static final String SYS_PARM_PROMOTION_STARTDATE = "#promotionStartDate";
	public static final String SYS_PARM_PROMOTION_ENDDATE = "#promotionEndDate";

	// HKTV file prefix
	public static final String ECOM_MMS = "ECOM-MMS";
	public static final String SAP_MMS = "SAP-MMS";
	public static final String RPT_MMS = "RPT-MMS";

	// EESE file prefix
	public static final String EESEECOM_MMS = "EESEECOM-MMS";

	public static final String TSV = ".tsv";

	//HktvFood
	public static final String BATCH_UPLOAD_FILE_SUFFIX_CSV = ".csv";
	public static final String BATCH_UPLOAD_FILE_SUFFIX_PDF = ".pdf";
	public static final String BATCH_UPLOAD_FILE_SUFFIX_JPG = ".jpg";
	public static final String BATCH_UPLOAD_FILE_SUFFIX_PNG = ".png";
	public static final String BATCH_UPLOAD_FILE_SUFFIX_JPEG = ".jpeg";
	public static final String BATCH_UPLOAD_FILE_SUFFIX_ZIP = ".zip";
	public static final List<String> SSO_FOOD_USER_GROUP_NAME = Arrays.asList("Food", "MMS");
	public static final List<String> SSO_MMS_DEFAULT_USER_GROUP = Arrays
			.asList("MMS", "Super Follow", "blog_shopinshop", "COMMS");
	public static final List<String> SSO_HKTV_PAY_USER_GROUP = Collections.singletonList("Pay");

	//DataBank
	public static final List<String> SSO_MMS_DATA_BANK_USER_GROUP = Collections.singletonList("Data Bank");
	public static final String SSO_MMS_DATA_BANK_API_GROUP = "databank_api";

	//PCR type
	public static final String PCR_TYPE_NET = "Net";
	public static final String PCR_TYPE_GROSS = "Gross";

	//PCR orderType
	public static final String PCR_ORDER_TYPE_ORDER = "order";
	public static final String PCR_ORDER_TYPE_ECOUPON = "eCoupon";
	public static final String PCR_ORDER_TYPE_RETURN = "return";
	public static final String PCR_ORDER_TYPE_REDEMPTION = "redemption";

	public static final String COMMISSION_TYPE_NORMAL = "Normal";
	public static final String COMMISSION_TYPE_SUPERFOLLOW = "Superfollow";
	public static final String COMMISSION_TYPE_THRESHOLD = "Threshold";
	public static final String COMMISSION_TYPE_PROMOTION = "Promotion";

	//rabbit mq
	public static final String PACK_BOX_TYPE = "PACK_BOX_TYPE";
	public static String ROUTING_KEY = "mms.packing_box_type";
	public static String EXCHANGE = "mms_topic";

	public static final String HKTV_PHY_PREFIX = "HKTVPHY_";

	public static final String PLATFORM_CODE_HKTV = "HKTV";
	public static final String PLATFORM_CODE_LOWER_CASE_HKTV = "hktv";
	public static final String PLATFORM_CODE_EESE = "EESE";

	public static final String BUSINESS_TYPE_ECOM = "eCommerce";
	public static final String BUSINESS_TYPE_FOOD = "food";
	public static final String BUSINESS_TYPE_PAY = "pay";
	public static final String BUSINESS_TYPE_EXPRESS = "express";

	public static final String SYS_PARAM_SEGMENT_DOC_TYPE = "DOC_TYPE";
	public static final String SYS_PARAM_SEGMENT_PAYMENT_TERM = "PAYMENT_TERM";
	public static final String SYS_PARAM_SEGMENT_PAYMENT_GROUP = "PAYMENT_GROUP";
	public static final String SYS_PARAM_SEGMENT_MERCHANT_DEPARTMENT = "MERCHANT_DEPARTMENT";
	public static final String SYS_PARAM_SEGMENT_REVIEW_STATUS = "REVIEW_STATUS";
	public static final String SYS_PARAM_SEGMENT_PRODUCT_READY_METHOD = "PRODUCT_READY_METHOD";
	public static final String SYS_PARAM_SEGMENT_PICKUP_DAYS = "PICKUP_DAYS";
	public static final String SYS_PARAM_SEGMENT_ADJUSTMENT_TYPE = "ADJUSTMENT_TYPE";
	public static final String SYS_PARAM_SEGMENT_RM_DEPARTMENT = "RM_DEPARTMENT";

	//PRODUCT_INVENTORY
	public static final String PRODUCT_INVENTORY_UPDATE_ACTION_SET = "set";
	public static final String PRODUCT_INVENTORY_UPDATE_ACTION_ADD = "add";
	public static final String PRODUCT_INVENTORY_UPDATE_ACTION_DEDUCT = "deduct";
	public static final String PENDING = "Pending";
	public static final Integer ZERO = 0;

	public static final List<String> PRODUCT_INVENTORY_UPDATE_ACTION_LIST = Arrays
			.asList(PRODUCT_INVENTORY_UPDATE_ACTION_SET, PRODUCT_INVENTORY_UPDATE_ACTION_ADD,
					PRODUCT_INVENTORY_UPDATE_ACTION_DEDUCT);

	public static final String PRODUCT_INVENTORY_AUTO_REDISTRIBUTION_ON = "ON";
	public static final String PRODUCT_INVENTORY_AUTO_REDISTRIBUTION_OFF = "OFF";

	public static final String PRODUCT_INVENTORY_STOCK_STATUS_NOT_SPEC = "notSpecified";
	public static final String PRODUCT_INVENTORY_STOCK_STATUS_FOOS = "forceOutOfStock";
	public static final String PRODUCT_INVENTORY_STOCK_STATUS_FIS = "forceInStock";

	public static final String PRODUCT_INVENTORY_BATCH_UPDATE_TYPE_PHYSICAL = "physical_product";
	public static final String PRODUCT_INVENTORY_BATCH_UPDATE_TYPE_REDISTRIBUTION = "setting_for_linked_product";
	public static final String PRODUCT_INVENTORY_BATCH_UPDATE_TYPE_ECOUPON_SYSTEM = "ecoupon_system";
	public static final String PRODUCT_INVENTORY_BATCH_UPDATE_TYPE_ECOUPON_MERCHANT = "ecoupon_merchant";
	public static final String PRODUCT_INVENTORY_BATCH_UPDATE_TYPE_INSTOCK_STATUS = "instock_status";

	public static final String BATCH_UPDATE_PRODUCT_INVENTORY_STATUS_PENDING = "P";
	public static final String BATCH_UPDATE_PRODUCT_INVENTORY_STATUS_FAIL = "F";
	public static final String BATCH_UPDATE_PRODUCT_INVENTORY_STATUS_SUCCESS = "S";

	public static final String BATCH_UPDATE_PRODUCT_INVENTORY_BU_ALL = "All";

	public static final String RUN_INVENTORY_REPORT_DELETE = "InventoryRepoerDeleteJob";

	public static final String CODE128A = "CODE128A";
	public static final String CODE128B = "CODE128B";
	public static final String CODE128C = "CODE128C";

	//RabbitMq
	public static final String RABBITMQ_EXCHANGE = "mms_topic";

	//MultiThread
	public static final String PROMOTION_CONJOB_MULTI_THREAD = "PROMOTION_CONJOB_MULTI_THREAD";

	public static final int DIVIDE_LIST = 500;

	// Additional Offer
	public static final Integer MAX_ADDITIONAL_PRIORITY = 99999;
	public static final Integer MIN_ADDITIONAL_PRIORITY = 1;

	//upload video
	public static final String THUMBNAIL_VIDEO = "thumbnailVideo";
	public static final String GALLERY_VIDEO = "galleryVideo";

	//batch add/Edit product
	public static final String PRODUCT_BATCH_UPLOAD_TYPE_CTEATE_SKU = "create_sku";
	public static final String PRODUCT_BATCH_UPLOAD_TYPE_UPDATE_SKU_ALL = "update_sku_all";
	public static final String PRODUCT_BATCH_UPLOAD_TYPE_UPDATE_SKU_PHOTOS = "update_sku_photos";
	public static final String PRODUCT_BATCH_UPLOAD_TYPE_UPDATE_SKU_ONOFFLINE = "update_sku_onOffLine";
	public static final String PRODUCT_BATCH_UPLOAD_TYPE_UPDATE_SKU_VISABLE = "update_sku_visible";
	public static final String BATCH_UPDATE_SUCCESS = "Batch upload successfully, please check audit result.";
	//Overseas contract
	public static final String OS_EXCHANGE_RATE_SEGMENT = "EXCHANGE_RATE_FOR_OS_PCR";

	//Date Date Format
	public static final String DATE_TIME_FORMAT_ONLY_NUMBER = "yyyyMMddhhmmss";
	public static final String DATE_TIME_FORMAT_FILE_NAME = "yyyyMMdd_HHmm";

	// DateTimeFormatter
	public static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

	public static final Long MAX_EVOUCHER_USER_MAX = 20L;
	public static final Long MIN_EVOUCHER_USER_MAX = 1L;
	public static final Long MAX_NORMAL_USER_MAX = 99999L;
	public static final Long MIN_USER_MAX = 0L;
	public static final Integer MAX_FILE_LENGTH = 255;
	public static final String PACKING_DIMENSION_UNIT_MM = "mm";
	public static final String PACKING_DIMENSION_UNIT_CM = "cm";
	public static final String PACKING_DIMENSION_UNIT_M = "m";
	public static final String WEIGHT_UNIT_G = "g";
	public static final String WEIGHT_UNIT_KG = "kg";
	public static final int ADVERTISE_PHOTO_MAX = 1;
	public static final int OTHER_PHOTO_MAX = 30;
	public static final int VARIANT_PRODUCT_PHOTO_MAX = 15;
	public static final int UPON_PURCHASE_DATE_MAX = 1000;
	public static final int WARRANTY_PERIOD_MAX = 1000;
	public static final Integer MAX_VIDEO_TEXT_LENGTH = 255;
	public static final BigDecimal PRICE_MAX = BigDecimal.valueOf(200000);
	public static final BigDecimal DIMENSION_MAX = BigDecimal.valueOf(1000000000);

	// Follow the curent length limit of DB field for SKU ID
	public static final Integer MAX_CHARACTERS_244 = 244;
	public static final Integer MAX_CHARACTERS_39 = 39;
	public static final Integer MAX_CHARACTERS_10 = 10;
	public static final Integer MAX_CHARACTERS_20 = 20;
	public static final Integer MAX_CHARACTERS_50 = 50;
	public static final Integer MAX_CHARACTERS_100 = 100;
	public static final Integer MAX_CHARACTERS_200 = 200;
	public static final Integer MAX_CHARACTERS_255 = 255;
	public static final Integer MAX_CHARACTERS_500 = 500;
	public static final Integer MAX_CHARACTERS_1000 = 1000;
	public static final Integer MAX_CHARACTERS_1024 = 1024;
	public static final Integer MAX_CHARACTERS_10000 = 10000;
	public static final Integer MAX_CHARACTERS_30000 = 30000;
	public static final Integer MAX_CHARACTERS_65535 = 65535;
	public static final Integer MAX_PRODUCT_TYPE_CODE_SIZE = 3;
	public static final Integer MAX_PHOTO_NAME_LENGTH = 255;
	public static final BigDecimal MAX_EW_PERCANTAGE_SETTING = BigDecimal.valueOf(100);
	public static final Integer LIMIT_OF_MAIN_PHOTO = 1;
	public static final String VOUCHER_TEMPLATE_TYPE_PREFIX = "Template";

	public static final Integer NON_EXISTENT_MERCHANT_ID = -1;

	public final static int MAX_LOG_LENGTH = 40000;

	// PRICE_ALERT_PRICE_DIFF_RATE
	public static final String PRICE_ALERT_PRICE_DIFF_RATE_CODE_ALIBABA = "ALIBABA";
	public static final String PRICE_ALERT_PRICE_DIFF_RATE_CODE_TAMLL = "TMALL";
	public static final String PRICE_ALERT_PRICE_DIFF_RATE_CODE_WHITELIST = "WHITELIST";
	public static final String PRICE_ALERT_CODE_CHECKING_ACTION_FLAG = "CHECKING_ACTION_FLAG";
	public static final String PRICE_ALERT_CODE_PRICE_UPDATE_THRESHOLD_DAY = "PRICE_UPDATE_THRESHOLD_DAY";
	public static final String PRICE_ALERT_CODE_CHECKING_ACTION_ON = "1";

	public static final String SYNC_PRICE_TO_HYBRIS_RMB_PREFIX = "SYNC_RMB_";

	// Brand Code
	public static final String BRAND_CODE_SHIPPED_FROM_MAINLAND = "ShippedfromMainland";

	public static final String SYSTEM_PARAM_SEGMENT_LIMIT_TMALL_AUTO_CREATE = "LIMIT_TMALL_AUTO_CREATE";
	public static final String SYSTEM_PARAM_CODE_LIMIT_TMALL_AUTO_CREATE = "LIMIT_TMALL_AUTO_CREATE";

}
