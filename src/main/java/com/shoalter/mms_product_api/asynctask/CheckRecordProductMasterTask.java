package com.shoalter.mms_product_api.asynctask;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.PmTractIdStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SystemUserEnum;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.bundle.BundleProductRepository;
import com.shoalter.mms_product_api.dao.repository.bundle.pojo.BundleProductDo;
import com.shoalter.mms_product_api.dao.repository.bundle.pojo.BundleProductMediaData;
import com.shoalter.mms_product_api.schedule.CheckTimeSchedule;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealTypeEnum;
import com.shoalter.mms_product_api.service.approval_deal.helper.ApprovalDealHelper;
import com.shoalter.mms_product_api.service.approval_deal.helper.ApprovalDealStatusHelper;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealSendMessageDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleSingleEditDto;
import com.shoalter.mms_product_api.service.hybris.helper.HybrisHelper;
import com.shoalter.mms_product_api.service.hybris.helper.HybrisMqFlowHelper;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisBundleCreateDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisBundleEditRequestDto;
import com.shoalter.mms_product_api.service.product.SaveProductRecordGroupHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductImageHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductVideoHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.helper.SyncBundleInventoryHelper;
import com.shoalter.mms_product_api.service.product.helper.SyncBundleOfflineHelper;
import com.shoalter.mms_product_api.service.product.helper.TaskExceptionHelper;
import com.shoalter.mms_product_api.service.product.helper.UserHelper;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterStatusProductBuDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterStatusProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterStatusResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOldMultimediaDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@RequiredArgsConstructor
@Service
@Slf4j
public class CheckRecordProductMasterTask {

	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final BundleProductRepository bundleProductRepository;

	private final ProductMasterHelper productMasterHelper;
	private final TaskExceptionHelper taskExceptionHelper;
	private final UserHelper userHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ProductVideoHelper productVideoHelper;
	private final ProductImageHelper productImageHelper;
	private final SyncBundleOfflineHelper syncBundleOfflineHelper;
	private final SyncBundleInventoryHelper syncBundleInventoryHelper;
	private final SaveProductRecordGroupHelper saveProductRecordGroupHelper;
	private final ApprovalDealHelper approvalDealHelper;
	private final ApprovalDealStatusHelper approvalDealStatusHelper;
	private final HybrisMqFlowHelper hybrisMqFlowHelper;

	private final MessageSource messageSource;
	private final HybrisHelper hybrisHelper;
	private final Gson gson;

	@Async("checkRecordProductMasterTaskExecutor")
	public void start(SaveProductRecordDo record) {
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();

		List<SaveProductRecordRowDo> waitCheckRowList =
				saveProductRecordRowRepository.findByRecordIdAndStatus(record.getId(), SaveProductStatus.CHECKING_PM);
		log.info("Task info run record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", record.getId(), SaveProductTypeEnum.getProductTypeName(record.getUploadType()), waitCheckRowList.size(), record.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(SaveProductStatus.CHECKING_PM));
		if (waitCheckRowList.isEmpty()) {
			finallyChangeRecordState(record);
		} else {
			UserDto userDto = userHelper.generateUserDtoByRecord(record);
			checkProductMaster(userDto, record, waitCheckRowList);
			// find success rows approvalDealTemp and insert/update approvalDeal and approvalDealStatusHistory
			if (SaveProductType.CHECKING_APPROVAL_TYPE_SET.contains(record.getUploadType())) {
				ResponseDto<List<ApprovalDealSendMessageDto>> result = approvalDealHelper.createOrCancelApprovalDealAndGenerateMessage(record.getId(), record.getUploadType(), waitCheckRowList, ApprovalDealTypeEnum.COMMISSION_RATE);
				if (result != null && result.getData() != null && result.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
					approvalDealStatusHelper.sendMessagesForApprovalStatusChange(userDto, result.getData());
				}
			}
			syncBundleOfflineHelper.checkAndCreateOfflineBundleRecordAndRow(record, waitCheckRowList);
			if(SaveProductType.SYNC_BUNDLE_INVENTORY_TYPE_SET.contains(record.getUploadType())) {
				syncBundleInventoryHelper.sendBundleInventoryAndUpdateRecordRow(record, waitCheckRowList);
			}
			changeRecordState(record, SaveProductStatus.CHECKING_PM);
		}

		stopWatch.stop();
		log.info("total time for processing record id {} in {} status: {} millisecond", record.getId(), SaveProductStatusEnum.getProductStatusName(SaveProductStatus.CHECKING_PM), stopWatch.getTotalTimeMillis());
	}

	private void changeRecordState(SaveProductRecordDo record, int status) {
		record.setStatus(status);
		record.setCheckTime(null);
		saveProductRecordRepository.save(record);
	}

	private void checkProductMaster(UserDto userDto, SaveProductRecordDo record, List<SaveProductRecordRowDo> rowList) {
		if (record.getPmTractId() == null) {
			log.info("record id: {} pmTractId is null, skip.", record.getId());
			return;
		}

		try {
			HashMap<Long, ProductMasterStatusProductDto> recordRowIdProductStatusHashMap = new HashMap<>();
			CheckTimeSchedule.addProductRecordId(record.getId());
			hybrisMqFlowHelper.handleHybrisMqFlowProcess(record, rowList);

			for (String pmTractId : record.getPmTractId().split(StringUtil.COMMA)) {
				ProductMasterStatusResultDto traceResult = productMasterHelper.requestTraceProduct(userDto, pmTractId);
				if (traceResult == null ||
					!PmTractIdStatusEnum.SUCCESS.name().equalsIgnoreCase(traceResult.getStatus()) ||
					!PmTractIdStatusEnum.READY.name().equalsIgnoreCase(traceResult.getData().getProcessingStatus())) {
					log.info("record id: {} still in progress, skip.", record.getId());
					return;
				}
				for (ProductMasterStatusProductDto productStatus : traceResult.getData().getProducts()) {
					recordRowIdProductStatusHashMap.put(productStatus.getRecordRowId(), productStatus);
				}
			}

			for (SaveProductRecordRowDo row : rowList) {
				ProductMasterStatusProductDto productStatus = recordRowIdProductStatusHashMap.get(row.getId());
				if (productStatus == null) {
					row.setStatus(SaveProductStatus.FAIL);
					row.setErrorMessage(messageSource.getMessage("message21", new String[]{ErrorMessageTypeCode.PRODUCT_MASTER_TRACE_RESULT_NOT_FOUND_ROW}, null));
					log.error("SKU {}, rowId {}: Can't Find Product Status! ", row.getSku(), row.getId());
				} else {
					StringBuilder errorMessageStringBuilder = new StringBuilder();

					List<ProductMasterStatusProductBuDto> hktvList = productStatus.getHktv();
					hktvList.forEach(hktv -> {
						if (hktv != null && hktv.getResult() != null) {
							boolean checkResult = PmTractIdStatusEnum.SUCCESS.name().equalsIgnoreCase(hktv.getResult());
							if (!checkResult && CollectionUtil.isNotEmpty(hktv.getFailReason())) {
								errorMessageStringBuilder.append("HKTV: \n");
								for (String error : hktv.getFailReason()) {
									errorMessageStringBuilder.append(error).append("\n");
								}
							}
						}
					});

					List<ProductMasterStatusProductBuDto> thirdPartyList = productStatus.getThirdPartyLogistics();
					thirdPartyList.forEach(thirdPartyLogistics -> {

						if (thirdPartyLogistics != null && thirdPartyLogistics.getResult() != null) {
							boolean checkResult = PmTractIdStatusEnum.SUCCESS.name().equalsIgnoreCase(thirdPartyLogistics.getResult());
							if (!checkResult && CollectionUtil.isNotEmpty(thirdPartyLogistics.getFailReason())) {
								errorMessageStringBuilder.append("3PL: \n");
								for (String error : thirdPartyLogistics.getFailReason()) {
									errorMessageStringBuilder.append(error).append("\n");
								}
							}
						}
					});
					// get little mall product master trace id status
					List<ProductMasterStatusProductBuDto> littleMallList = productStatus.getLittleMall();
					littleMallList.forEach(littleMall -> {
						if (littleMall != null && littleMall.getResult() != null) {
							boolean checkResult = PmTractIdStatusEnum.SUCCESS.name().equalsIgnoreCase(littleMall.getResult());
							if (!checkResult && CollectionUtil.isNotEmpty(littleMall.getFailReason())) {
								errorMessageStringBuilder.append("ThePlace: \n");
								for (String error : littleMall.getFailReason()) {
									errorMessageStringBuilder.append(error).append("\n");
								}
							}
						}
					});

					List<ProductMasterStatusProductBuDto> mixList = productStatus.getMix();
					mixList.forEach(mix -> {
						if (mix != null && mix.getResult() != null) {
							boolean checkResult = PmTractIdStatusEnum.SUCCESS.name().equalsIgnoreCase(mix.getResult());
							if (!checkResult && CollectionUtil.isNotEmpty(mix.getFailReason())) {
								errorMessageStringBuilder.append("MIX: \n");
								for (String error : mix.getFailReason()) {
									errorMessageStringBuilder.append(error).append("\n");
								}
							}
						}
					});

					List<ProductMasterStatusProductBuDto> iidsList = productStatus.getIids();
					iidsList.forEach(iids -> {
						if (iids != null && iids.getResult() != null) {
							boolean checkResult = PmTractIdStatusEnum.SUCCESS.name().equalsIgnoreCase(iids.getResult());
							if (!checkResult && CollectionUtil.isNotEmpty(iids.getFailReason())) {
								errorMessageStringBuilder.append("IIDS: \n");
								for (String error : iids.getFailReason()) {
									errorMessageStringBuilder.append(error).append("\n");
								}
							}
						}
					});

					//call hybris create bundle api if bundle product is created successfully
					if (record.getUploadType() == SaveProductType.SINGLE_CREATE_BUNDLE) {
						handleCreateBundleFlow(userDto, row, productStatus, errorMessageStringBuilder);
					} else if (errorMessageStringBuilder.length() == 0 && record.getUploadType() == SaveProductType.SINGLE_EDIT_BUNDLE) {
						//call hybris update bundle api if bundle product is updated successfully
						handleEditBundleFlow(userDto, row, productStatus, errorMessageStringBuilder);
					}

					if (errorMessageStringBuilder.length() == 0) {
						// delete old image & old video
						deleteProductOldMultimedia(row);
						row.setStatus(SaveProductStatus.SUCCESS);
						row.setUuid(productStatus.getUuid());
					} else {
						String errorMessage = errorMessageStringBuilder.toString();
						row.setStatus(SaveProductStatus.FAIL);
						row.setErrorMessage(errorMessage);
						log.error("SKU: {}, TractId: {}, ErrorMessage: {} ", row.getSku(), record.getPmTractId(), errorMessage);
					}
				}
				saveProductRecordRowRepository.save(row);
			}

		} catch (Exception e) {
			taskExceptionHelper.start(record, rowList, e);
		} finally {
			CheckTimeSchedule.removeProductRecordId(record.getId());
		}
	}

	private void handleEditBundleFlow(UserDto userDto, SaveProductRecordRowDo row, ProductMasterStatusProductDto productStatus, StringBuilder errorMessageStringBuilder) {
		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		HybrisBundleEditRequestDto hybrisRequestData = HybrisBundleEditRequestDto.generateHybrisBundleEditRequestDto(singleEditProductDto);
		ResponseDto<Void> hybrisEditBundleResponse = hybrisHelper.requestUpdateBundle(userDto, hybrisRequestData, singleEditProductDto.getProduct().getUuid());

		if (hybrisEditBundleResponse.getStatus() != StatusCodeEnum.SUCCESS.getCode()) {
			errorMessageStringBuilder.append(hybrisEditBundleResponse.getErrorMessageList());
			//if send to hybris fail and status is online, save product record to update bundle ,make bundle offline
			if (OnlineStatusEnum.ONLINE == singleEditProductDto.getProduct().getAdditional().getHktv().getOnlineStatus()) {
				BundleSingleEditDto bundleSingleEditDto = gson.fromJson(gson.toJson(singleEditProductDto), BundleSingleEditDto.class);
				bundleSingleEditDto.getProduct().setUuid(productStatus.getUuid());
				bundleSingleEditDto.getProduct().getAdditional().getHktv().setOnlineStatus(OnlineStatusEnum.OFFLINE);
				SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createSaveProductRecord(SystemUserEnum.MMS_PRODUCT_SYSTEM.getSystemId(), singleEditProductDto.getProduct().getMerchantId(),
						SaveProductType.SINGLE_EDIT_BUNDLE, singleEditProductDto.getProduct().getSkuId());
				saveProductRecordRowHelper.createProductRecordRowDo(saveProductRecordDo.getId(), bundleSingleEditDto);
				log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
			}
		} else {
			Optional<BundleProductDo> bundleProduct = bundleProductRepository.findByUuid(singleEditProductDto.getProduct().getUuid());
			BundleProductMediaData imageInfo = bundleProduct.get().getProductInfo().getImageUrls();

			//Delete old image if changed the image during the bundle edit
			if (!imageInfo.getLastUrls().isEmpty()) {
				Set<String> afterDeleteWorkUrlSet = getAfterDeleteWorkUrlSet(userDto, imageInfo);
				imageInfo.setLastUrls(afterDeleteWorkUrlSet);
				bundleProduct.get().getProductInfo().setImageUrls(imageInfo);
				bundleProductRepository.save(bundleProduct.get());
			}
		}
	}

	Set<String> getAfterDeleteWorkUrlSet(UserDto userDto, BundleProductMediaData imageInfo) {
		Set<String> afterDeleteWorkUrlSet = imageInfo.getLastUrls();
		Set<String> deletedUrls = new HashSet<>();

		imageInfo.getLastUrls().forEach(url -> {
			HttpStatus httpStatus = productImageHelper.requestDeleteImageByUrlId(userDto.getUserCode(), productImageHelper.generateUrlId(List.of(url)).get(0));
			if (httpStatus == null || httpStatus.isError()) {
				log.error("Image server delete image FAIL, image url: {}", url);
			} else {
				deletedUrls.add(url);
			}
		});

		afterDeleteWorkUrlSet.removeAll(deletedUrls);

		return afterDeleteWorkUrlSet;
	}

	private void handleCreateBundleFlow(UserDto userDto, SaveProductRecordRowDo row, ProductMasterStatusProductDto productStatus,
										StringBuilder errorMessageStringBuilder) {

		Optional<BundleProductDo> bundleProductDoOpt = bundleProductRepository.findByUuid(productStatus.getUuid());
		if (bundleProductDoOpt.isEmpty()) {
			errorMessageStringBuilder.append(messageSource.getMessage("message165", new String[]{productStatus.getUuid()}, null));
			return;
		}
		BundleProductDo bundleProductDo = bundleProductDoOpt.get();

		//if there is any system fail to create product, mark failed.
		if (errorMessageStringBuilder.length() != 0) {
			bundleProductDo.setSyncSystem(false);
		} else {
			//try to call hybris to import bundle set
			SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
			HybrisBundleCreateDto hybrisRequestData = HybrisBundleCreateDto.generateHybrisBundleCreateDto(singleEditProductDto);
			ResponseDto<Void> hybrisCreateBundleResponse = hybrisHelper.requestCreateBundle(userDto, hybrisRequestData, singleEditProductDto.getProduct().getUuid());

			//handle response
			if (hybrisCreateBundleResponse.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
				bundleProductDo.setSyncSystem(true);
			} else {
				bundleProductDo.setSyncSystem(false);
				errorMessageStringBuilder.append(hybrisCreateBundleResponse.getErrorMessageList());

				//if send to hybris fail and status is online, save product record to update bundle ,make bundle offline
				if (OnlineStatusEnum.ONLINE == singleEditProductDto.getProduct().getAdditional().getHktv().getOnlineStatus()) {
					BundleSingleEditDto bundleSingleEditDto = gson.fromJson(row.getContent(), BundleSingleEditDto.class);
					bundleSingleEditDto.getProduct().setUuid(productStatus.getUuid());
					bundleSingleEditDto.getProduct().getAdditional().getHktv().setOnlineStatus(OnlineStatusEnum.OFFLINE);
					SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createSaveProductRecord(SystemUserEnum.MMS_PRODUCT_SYSTEM.getSystemId(), singleEditProductDto.getProduct().getMerchantId(),
							SaveProductType.SINGLE_EDIT_BUNDLE, singleEditProductDto.getProduct().getSkuId());
					saveProductRecordRowHelper.createProductRecordRowDo(saveProductRecordDo.getId(), bundleSingleEditDto);
					log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
				}
			}
		}

		bundleProductRepository.save(bundleProductDo);
	}

	private void finallyChangeRecordState(SaveProductRecordDo record) {
		int failCount = saveProductRecordRowRepository.countByRecordIdAndStatus(record.getId(), SaveProductStatus.FAIL);
		if (failCount == 0) {
			record.setStatus(SaveProductStatus.SUCCESS);
		} else {
			record.setStatus(SaveProductStatus.FAIL);
		}
		saveProductRecordRepository.save(record);
		saveProductRecordGroupHelper.finallyChangeGroupState(record.getGroupId());
	}

	private void deleteProductOldMultimedia(SaveProductRecordRowDo row) {
		try {
			SingleEditProductDto rowContent = gson.fromJson(row.getContent(), SingleEditProductDto.class);
			ProductOldMultimediaDto productOldMultimedia = rowContent.getProductOldMultimedia();
			if (productOldMultimedia != null) {
				log.info("delete productOldMultimedia: {}", gson.toJson(productOldMultimedia));
				for (String fileName : productOldMultimedia.getOldVideoFileNames()) {
					productVideoHelper.requestDeleteVideoByFileName(productOldMultimedia.getUserCode(), fileName);
				}
				// TODO delete image url need to checking none of the other variants are using it.
//				for (String deleteURLId : productOldMultimedia.getOldImageUrlIds()) {
//					productImageHelper.requestDeleteImageByUrlId(productOldMultimedia.getUserCode(), deleteURLId);
//				}
			}
		} catch (Exception e) {
			log.error("delete product old multimedia row id: {}, error: {}", row.getId(), e.getMessage(), e);
		}
	}
}
