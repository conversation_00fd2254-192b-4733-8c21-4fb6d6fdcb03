package com.shoalter.mms_product_api.config.adapter;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.type.ConstantType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@RequiredArgsConstructor
@ControllerAdvice
public class LogRequestBodyAdviceAdapter extends RequestBodyAdviceAdapter {

	private final Gson gson;
	private final HttpServletRequest request;

	@Override
	public boolean supports(MethodParameter methodParameter, Type type,
							Class<? extends HttpMessageConverter<?>> aClass) {
		return true;
	}

	@Override
	public Object afterBodyRead(Object body, HttpInputMessage inputMessage,
								MethodParameter parameter, Type targetType,
								Class<? extends HttpMessageConverter<?>> converterType) {
		String method = request.getMethod();
		String uri = request.getRequestURI();

		String requestBody = gson.toJson(body);
		AtomicInteger idx = new AtomicInteger(0);

		Arrays.stream(requestBody.split("(?<=\\G.{" + ConstantType.MAX_LOG_LENGTH + "})"))
			.forEach(str -> log.info("[{}] {}, request body-{}: {}",
				method, uri, idx.getAndIncrement(), str));

		return super.afterBodyRead(body, inputMessage, parameter, targetType, converterType);
	}
}
