package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ProductFileConfig;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SystemUserEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductImagesRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.DeprecatedProductImageViewDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductInfoDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreInfoDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdByHktvStoreProductMasterRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdResultDto;
import com.shoalter.mms_product_api.service.product.pojo.DeprecatedProductImageRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class DeprecatedProductImageService {
	private final StoreRepository storeRepository;
	private final ProductRepository productRepository;
	private final ProductImagesRepository productImagesRepository;
	private final ProductMasterHelper productMasterHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;


	@Transactional
	public ResponseDto<Void> start(DeprecatedProductImageRequestDto requestDto) {

		if (CollectionUtil.isEmpty(requestDto.getSkuUuids()) || StringUtils.isBlank(requestDto.getImageUrl())) {
			throw new NoDataException();
		}

		List<ProductInfoDo> productStoreInfoDoList = productRepository.findProductStoreInfoBySkuUuids(requestDto.getSkuUuids());
		Map<String, List<ProductInfoDo>> storeCodeToProductStoreInfoMap = productStoreInfoDoList.stream()
			.collect(Collectors.groupingBy(ProductInfoDo::getStoreCode));

		Set<String> updateProductFlowSkuUuids = new HashSet<>();
		//check if product master have sku
		for (Map.Entry<String, List<ProductInfoDo>> entry : storeCodeToProductStoreInfoMap.entrySet()) {
			ProductInfoDo oneProductInfoDo = entry.getValue().get(0);
			if (!BuCodeEnum.HKTV.name().equals(oneProductInfoDo.getBuCode())) {
				log.error("store code: {} is not hktv mall store, skip", entry.getKey());
				continue;
			}

			List<String> skuIds = entry.getValue().stream().map(ProductInfoDo::getSkuCode).collect(Collectors.toList());
			CheckSkuIdResultDto checkSkuIdResultDto = productMasterHelper.requestCheckSkuIdByHktvStore(UserDto.builder().build(), new CheckSkuIdByHktvStoreProductMasterRequestDto(oneProductInfoDo.getStoreCode(), skuIds));
			if (checkSkuIdResultDto == null) {
				log.error("Unable to request product master.");
				continue;
			}

			List<String> existSkuCodes = checkSkuIdResultDto.getExistsSkus() == null ? List.of() : checkSkuIdResultDto.getExistsSkus();
			updateProductFlowSkuUuids.addAll(entry.getValue().stream()
				.filter(productInfoDo -> existSkuCodes.contains(productInfoDo.getSkuCode()))
				.map(ProductInfoDo::getUuid)
				.collect(Collectors.toList()));
		}

		if (CollectionUtil.isNotEmpty(updateProductFlowSkuUuids)) {
			updateProductFlow(requestDto, updateProductFlowSkuUuids);
		}

		Set<String> updateImageFlowSkuUuids = requestDto.getSkuUuids().stream().filter(skuUuids -> !updateProductFlowSkuUuids.contains(skuUuids)).collect(Collectors.toSet());
		if (CollectionUtil.isNotEmpty(updateImageFlowSkuUuids)) {
			int count = productImagesRepository.updateImagePathByStoreSkuIds(updateImageFlowSkuUuids, requestDto.getImageUrl());
			log.info("replace: {} file paths", count);
		}

		return ResponseDto.success(null);
	}


	private void updateProductFlow(DeprecatedProductImageRequestDto requestDto, Set<String> updateFlowSkus) {
		SaveProductRecordDo editProductRecord = saveProductRecordHelper
			.createSaveProductRecord(SystemUserEnum.MMS_PRODUCT_SYSTEM.getSystemId(), ConstantType.NON_EXISTENT_MERCHANT_ID,
				SaveProductType.BATCH_EDIT_DEPRECATED_PRODUCT_IMAGES, String.format("deprecated_product_%d", System.currentTimeMillis()), SaveProductStatus.REQUESTING_PM);

		log.info("create BATCH_EDIT_DEPRECATED_PRODUCT_IMAGES record: {}", editProductRecord.getId());

		List<DeprecatedProductImageViewDo> imageViewDoList = productImagesRepository.findByStoreSkuIds(updateFlowSkus);
		Map<String, List<DeprecatedProductImageViewDo>> skuToImagesMap = imageViewDoList.stream()
			.collect(Collectors.groupingBy(DeprecatedProductImageViewDo::getStoreSkuId));

		for (Map.Entry<String, List<DeprecatedProductImageViewDo>> entry : skuToImagesMap.entrySet()) {
			if (CollectionUtil.isEmpty(entry.getValue())) {
				log.info("store sku id {} have no images, skip replace image precess", entry.getKey());
				continue;
			}
			SingleEditProductDto singleEditProductDto = generateUpdateProductDto(entry.getValue(), requestDto.getImageUrl());
			saveProductRecordRowHelper.createProductRecordRowDo(editProductRecord.getId(), entry.getKey().split(StringUtil.PRODUCT_SEPARATOR)[1], singleEditProductDto, SaveProductStatus.REQUESTING_PM);
		}
	}

	private SingleEditProductDto generateUpdateProductDto(List<DeprecatedProductImageViewDo> imageViewDoList, String imageUrl) {
		DeprecatedProductImageViewDo imageViewDo = imageViewDoList.get(0);
		Map<String, Long> imageCountMap = imageViewDoList.stream()
			.map(DeprecatedProductImageViewDo::getImageType)
			.collect(Collectors.groupingBy(e -> e, Collectors.counting()));

		ProductMasterDto productMasterDto = new ProductMasterDto();
		productMasterDto.setBuToSend(List.of(BuCodeEnum.HKTV.name()));
		productMasterDto.setUuid(imageViewDo.getUuid());

		HktvProductDto hktvProductDto = new HktvProductDto();
		hktvProductDto.setStores(imageViewDo.getStoreCode());
		hktvProductDto.setMainPhoto(imageUrl);

		if (imageCountMap.containsKey(ProductFileConfig.IMAGE_TYPE_OTHER_A)) {
			hktvProductDto.setVariantProductPhoto(new ArrayList<>());
			for (long i = 0; i < imageCountMap.get(ProductFileConfig.IMAGE_TYPE_OTHER_A); i++) {
				hktvProductDto.getVariantProductPhoto().add(imageUrl);
			}
		}

		if (imageCountMap.containsKey(ProductFileConfig.IMAGE_TYPE_OTHER_B)) {
			hktvProductDto.setOtherPhoto(new ArrayList<>());
			for (long i = 0; i < imageCountMap.get(ProductFileConfig.IMAGE_TYPE_OTHER_B); i++) {
				hktvProductDto.getOtherPhoto().add(imageUrl);
			}
		}

		if (imageCountMap.containsKey(ProductFileConfig.IMAGE_TYPE_ADVERTISING)) {
			hktvProductDto.setAdvertisingPhoto(imageUrl);
		}

		BuProductDto buProductDto = new BuProductDto();
		buProductDto.setHktv(hktvProductDto);

		SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
		singleEditProductDto.setProduct(productMasterDto);
		singleEditProductDto.getProduct().setAdditional(buProductDto);

		return singleEditProductDto;
	}
}
