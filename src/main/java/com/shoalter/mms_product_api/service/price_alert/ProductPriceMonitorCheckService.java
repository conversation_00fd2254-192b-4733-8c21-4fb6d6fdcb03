package com.shoalter.mms_product_api.service.price_alert;

import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.config.product.*;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.mapper.product.ProductStorePromotionMapper;
import com.shoalter.mms_product_api.dao.mapper.product.pojo.ProductStorePromotionDto;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.merchant.UserStoreRepository;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserStoreRoleDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductPriceMonitorProductCheckHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductPriceMonitorProductCheckRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductPriceMonitorProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductCheckDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductCheckHistoryDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.helper.TokenHelper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.hybris.helper.HybrisHelper;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateMainlandSamePriceRequestSkuDto;
import com.shoalter.mms_product_api.service.price_alert.pojo.PriceAlertNotificationDto;
import com.shoalter.mms_product_api.service.price_alert.pojo.ProductPriceAlertDto;
import com.shoalter.mms_product_api.service.price_alert.pojo.ProductPriceAlertUserInfoDto;
import com.shoalter.mms_product_api.service.price_alert.pojo.ProductProductPriceAlertMerchantDto;
import com.shoalter.mms_product_api.service.product.helper.BatchCheckHelper;
import com.shoalter.mms_product_api.service.product.helper.BatchEditHelper;
import com.shoalter.mms_product_api.service.product.helper.BusUnitHelper;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.*;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.apache.bcel.classfile.ConstantValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service for checking product prices from monitors and handling price discrepancies
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductPriceMonitorCheckService {

	private final ProductPriceMonitorProductRepository productPriceMonitorProductRepository;
	private final ProductPriceMonitorProductCheckRepository productPriceMonitorProductCheckRepository;
	private final ProductPriceMonitorProductCheckHistoryRepository productPriceMonitorProductCheckHistoryRepository;
	private final ProductMasterHelper productMasterHelper;
	private final SysParmRepository sysParmRepository;
	private final StoreRepository storeRepository;
	private final BatchEditHelper batchEditHelper;
	private final ProductStorePromotionMapper productStorePromotionMapper;
	private final HybrisHelper hybrisHelper;
	private final TokenHelper tokenHelper;
	private final BusUnitHelper busUnitHelper;
	private final ExchangeRateHelper exchangeRateHelper;
	private final ProductPriceAlertNotificationService productPriceAlertNotificationService;
	private final UserStoreRepository userStoreRepository;
	private final SaveProductRecordRepository saveProductRecordRepository;

	private static final int PRODUCT_MASTER_SEARCH_SKU_BATCH_SIZE = 1000;

	@Value("${mms.db.price.alert.data.size}")
	private int mmsDbPriceAlertDataSize;

	@Value("${hybris.api.update.mainland.same.price.size}")
	private int hybrisApiUpdateMainlandSamePriceSize;


	/**
	 * Check product prices between ProductMaster (source) and third-party platform (target)
	 * Will create offline records for products that exceed the threshold
	 */
	@Async
	public void monitorProductPrice() {
		String jobTraceUuid = UUID.randomUUID().toString();
		long startTime = System.currentTimeMillis();
		log.info("[price-monitor/check-price] Starting price check process for job: {}", jobTraceUuid);

		try {
			// support HKTV platform only
			String businessCode = BuCodeEnum.HKTV.name();
			Integer busUnitId = busUnitHelper.findBusinessId(businessCode);

			// Fetch ENABLE + PRICE_NOT_FOUND/THIRD_PARTY_SERVER_ERROR records for TMALL platform
			List<ProductPriceMonitorProductDo> productsWithPriceNotFound = productPriceMonitorProductRepository
				.findByActiveIndAndPriceStatusInAndTargetPlatformAndBusUnitId(ActiveInd.ENABLE.getValue(), Set.of(MonitorProductPriceStatus.PRICE_NOT_FOUND.getValue(), MonitorProductPriceStatus.THIRD_PARTY_SERVER_ERROR.getValue()), ThirdPartySourceEnum.TMALL.name(), busUnitId);

			if (CollectionUtils.isNotEmpty(productsWithPriceNotFound)) {
				log.info("[price-monitor/check-price] Found {} products with price not found/third-party server error status, jobTraceUuid: {}", productsWithPriceNotFound.size(), jobTraceUuid);
				createOrUpdatePriceNotFoundRecords(jobTraceUuid, productsWithPriceNotFound);
			}

			// Fetch ENABLE + PRICE_UPDATED records for TMALL platform
			List<ProductPriceMonitorProductDo> productsToCheck = productPriceMonitorProductRepository
				.findByActiveIndAndPriceStatusAndTargetPlatformAndBusUnitId(ActiveInd.ENABLE.getValue(), MonitorProductPriceStatus.PRICE_UPDATED.getValue(), ThirdPartySourceEnum.TMALL.name(), busUnitId);

			if (CollectionUtils.isEmpty(productsToCheck)) {
				log.info("[price-monitor/check-price] No price updated products found to check");
				return;
			} else {
				log.info("[price-monitor/check-price] Found {} price updated products to check", productsToCheck.size());
			}

			// Group products by merchant ID
			Map<Integer, List<ProductPriceMonitorProductDo>> merchantProductGroups = productsToCheck.stream()
				.collect(Collectors.groupingBy(ProductPriceMonitorProductDo::getMerchantId));

			log.info("[price-monitor/check-price] Found {} merchant groups to process", merchantProductGroups.size());

			// Get the percentage threshold for price comparison
			BigDecimal rmbPercentageThreshold = getPercentageThreshold(ConstantType.PRICE_ALERT_PRICE_DIFF_RATE_CODE_TAMLL);
			BigDecimal hkbPercentageThreshold = getPercentageThreshold(ConstantType.PRICE_ALERT_PRICE_DIFF_RATE_CODE_WHITELIST);

			BigDecimal rmbToHkdExchangeRate = exchangeRateHelper.getExchangeRateByCurrency(CurrencyEnum.RMB);

			PriceConvertContent priceConvertContent = PriceConvertContent.builder()
				.rmbPercentageThreshold(rmbPercentageThreshold)
				.hkdPercentageThreshold(hkbPercentageThreshold)
				.rmbToHkdExchangeRate(rmbToHkdExchangeRate)
				.build();


			SysParmDo sysParmDo = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.PRICE_MONITOR, ConstantType.PRICE_ALERT_CODE_CHECKING_ACTION_FLAG, 1).stream()
				.findFirst()
				.orElseThrow(() -> new SystemException("priceAlertPriceDiffRate not found for code"));
			log.info("PRICE_ALERT_CODE_CHECKING_ACTION_FLAG, CODE = {}, PARM_VALUE = {}", sysParmDo.getCode(), sysParmDo.getParmValue());

			// Process each merchant group
			for (Map.Entry<Integer, List<ProductPriceMonitorProductDo>> entry : merchantProductGroups.entrySet()) {
				Integer merchantId = entry.getKey();
				List<ProductPriceMonitorProductDo> merchantProducts = entry.getValue();

				log.info("[price-monitor/check-price] Processing merchant ID: {} with {} products", merchantId, merchantProducts.size());

				// Process the product group for this merchant: compare prices and update status: PROCESSING/CHECKED
				processProductGroup(jobTraceUuid, merchantProducts, priceConvertContent, businessCode);

				// Create offline records for products marked for offline
				createOfflineRecordsForRecords(jobTraceUuid, merchantId, sysParmDo.getParmValue());
				// Call Hybris to update updateProductMainlandSamePrice flag with DB partition
				updateHybrisProductMainlandSamePricePartition(jobTraceUuid, merchantId, sysParmDo.getParmValue());
			}

			if(sysParmDo.getParmValue().equals(ConstantType.PRICE_ALERT_CODE_CHECKING_ACTION_ON)) {
				long realOfflineSkuCount = productPriceMonitorProductCheckRepository.countByStatusAndJobTraceUuid(Set.of(MonitorProductCheckStatus.OFFLINE_RECORD_CREATE.getValue()
					,MonitorProductCheckStatus.OFFLINE_RECORD_CREATE_FAIL.getValue(),MonitorProductCheckStatus.OFFLINE.getValue(),MonitorProductCheckStatus.OFFLINE_FAIL.getValue()), jobTraceUuid);
				log.info("[price-monitor/check-price] Calculate create offline records,jobTraceUuid: {},realOfflineSkuCount: {}", jobTraceUuid,realOfflineSkuCount);
				notifyUser(jobTraceUuid);
				saveHistoryRecords(jobTraceUuid, MonitorProductCheckStatus.PRICE_CHECK_FINAL_STATUSES);
			}else {
				log.info("[price-monitor/check-price] Skip notify user and save history records for jobTraceUuid: {}", jobTraceUuid);
				long preCalculationOfflineSkuCount = productPriceMonitorProductCheckRepository.countByStatusAndJobTraceUuid(Set.of(MonitorProductCheckStatus.PROCESSING.getValue()), jobTraceUuid);
				log.info("[price-monitor/check-price] PreCalculation create offline records,jobTraceUuid: {},preCalculationOfflineSkuCount: {}", jobTraceUuid,preCalculationOfflineSkuCount);
			}

			long isSamePriceCount = productPriceMonitorProductCheckRepository.countByStatusAndJobTraceUuid(Set.of(MonitorProductCheckStatus.CHECKED.getValue()), jobTraceUuid);
			log.info("[price-monitor/check-price] Calculate mainland same price update,jobTraceUuid: {},isSamePriceCount: {}", jobTraceUuid, isSamePriceCount);

			long endTime = System.currentTimeMillis();
			log.info("[price-monitor/check-price] Completed checking product prices, execution time: {} ms", (endTime - startTime));
		} catch (Exception e) {
			log.error("[price-monitor/check-price] Error checking product prices: {}", e.getMessage(), e);
		}
	}

	// SAVE all records in end state into history table
	private void saveHistoryRecords(String jobTraceUuid, Set<Integer> finalStatuses) {
		log.info("[price-monitor/check-price] Saving history records for jobTraceUuid: {}", jobTraceUuid);

		try {
			// Find all records with the final statuses for this job
			List<ProductPriceMonitorProductCheckDo> checkRecords = productPriceMonitorProductCheckRepository.findByJobTraceUuidAndStatusIn(jobTraceUuid, finalStatuses);

			if (checkRecords.isEmpty()) {
				log.info("[price-monitor/check-price][history] No completed records found for jobTraceUuid: {}", jobTraceUuid);
				return;
			}

			log.info("[price-monitor/check-price][history] Found {} records in final state for jobTraceUuid: {}", checkRecords.size(), jobTraceUuid);

			List<ProductPriceMonitorProductCheckHistoryDo> historyRecords = new ArrayList<>();
			LocalDateTime now = LocalDateTime.now();

			for (ProductPriceMonitorProductCheckDo checkRecord : checkRecords) {
				if (checkRecord.getProductPriceMonitorProduct() == null) {
					log.warn("[price-monitor/check-price] Product data not found for check record ID: {}", checkRecord.getId());
					continue;
				}
				// Create history record
				ProductPriceMonitorProductCheckHistoryDo historyRecord = getProductPriceMonitorProductCheckHistoryDo(checkRecord, now);
				historyRecords.add(historyRecord);
			}

			// Save all history records in a single batch
			if (!historyRecords.isEmpty()) {
				productPriceMonitorProductCheckHistoryRepository.saveAll(historyRecords);
				log.info("[price-monitor/check-price] Successfully saved {} history records for jobTraceUuid: {}", historyRecords.size(), jobTraceUuid);
			}
		} catch (Exception e) {
			log.error("[price-monitor/check-price] Error saving history records for jobTraceUuid: {}", jobTraceUuid, e);
		}
	}

	private ProductPriceMonitorProductCheckHistoryDo getProductPriceMonitorProductCheckHistoryDo(ProductPriceMonitorProductCheckDo checkRecord, LocalDateTime now) {
		ProductPriceMonitorProductDo product = checkRecord.getProductPriceMonitorProduct();
		return ProductPriceMonitorProductCheckHistoryDo.builder()
			// Map fields from ProductPriceMonitorProductCheckDo
			.productPriceMonitorProductCheckId(checkRecord.getId())
			.productPriceMonitorProductId(checkRecord.getProductPriceMonitorProductId())
			.status(checkRecord.getStatus())
			.jobTraceUuid(checkRecord.getJobTraceUuid())
			.recordId(checkRecord.getRecordId())
			.sourceOriginalPrice(checkRecord.getSourceOriginalPrice())
			.sourceSellingPrice(checkRecord.getSourceSellingPrice())
			.targetOriginalPrice(checkRecord.getTargetOriginalPrice())
			.targetSellingPrice(checkRecord.getTargetSellingPrice())
			.targetUrl(checkRecord.getTargetUrl())
			.targetPriceUpdatedDate(checkRecord.getTargetPriceUpdatedDate())
			.samePriceFlag(checkRecord.getSamePriceFlag())
			.errorReason(checkRecord.getErrorReason())
			.errorCode(checkRecord.getErrorCode())

			// Map fields from ProductPriceMonitorProductDo
			.activeInd(product.getActiveInd())
			.priceStatus(product.getPriceStatus())
			.busUnitId(product.getBusUnitId())
			.merchantId(product.getMerchantId())
			.storeId(product.getStoreId())
			.storefrontStoreCode(product.getStorefrontStoreCode())
			.storeSkuId(product.getStoreSkuId())
			.skuCode(product.getSkuCode())
			.skuName(product.getSkuName())
			.sourcePlatform(product.getSourcePlatform())
			.sourceCurrencyCode(product.getSourceCurrencyCode())
			.targetPlatform(product.getTargetPlatform())
			.targetProductCode(product.getTargetProductCode())
			.targetSkuCode(product.getTargetSkuCode())
			.targetCurrencyCode(product.getTargetCurrencyCode())

			// Preserve audit fields
			.createdDate(checkRecord.getCreatedDate())
			.createdBy(checkRecord.getCreatedBy())
			.lastUpdatedDate(checkRecord.getLastUpdatedDate())
			.lastUpdatedBy(checkRecord.getLastUpdatedBy())

			// Add history specific field
			.historyCreatedDate(now)
			.build();
	}

	private void notifyUser(String jobTraceUuid) {
		try {
			log.info("[price-monitor/check-price] Notifying users for jobTraceUuid: {}", jobTraceUuid);
			// 1. Find all records with statuses RECORD_CREATE or RECORD_CREATE_FAIL
			List<ProductPriceMonitorProductCheckDo> records = productPriceMonitorProductCheckRepository.findByJobTraceUuidAndStatusIn(
				jobTraceUuid,
				Set.of(
					MonitorProductCheckStatus.OFFLINE_RECORD_CREATE.getValue(),
					MonitorProductCheckStatus.OFFLINE_RECORD_CREATE_FAIL.getValue()));

			if (CollectionUtils.isNotEmpty(records)) {
				log.info("[price-monitor/check-price] Found {} price monitor records to notify for jobTraceUuid: {}", records.size(), jobTraceUuid);
				// 2. Convert records to DTOs for internal users
				List<ProductPriceAlertDto> alertInternalUserData = records.stream()
					.map(ProductPriceAlertDto::from)
					.collect(Collectors.toList());

				// 3. Process records to associate them with corresponding merchants
				List<ProductProductPriceAlertMerchantDto> alertMerchantsData = getProductProductPriceAlertMerchantDtos(records);

				// 4. Build the notification DTO with both internal and merchant alert data
				PriceAlertNotificationDto dataToSend = PriceAlertNotificationDto.builder()
					.alertMerchantsData(alertMerchantsData)
					.alertInternalUserData(alertInternalUserData)
					.build();

				// 5. Initiate the notification process
				productPriceAlertNotificationService.start(dataToSend);
			} else {
				log.warn("[price-monitor/check-price] No price monitor records to notify for jobTraceUuid: {}", jobTraceUuid);
			}
		} catch (Exception e) {
			log.error("[price-monitor/check-price] Error in notifying users, jobTraceUuid: {}, error: {}",
				jobTraceUuid, e.getMessage(), e);
		}
	}


	/**
	 * Process monitor records to associate them with the corresponding merchants
	 */
	private List<ProductProductPriceAlertMerchantDto> getProductProductPriceAlertMerchantDtos(List<ProductPriceMonitorProductCheckDo> records) {


		// Get all unique storeIds
		Set<Integer> storeIds = records.stream()
			.map(data -> data.getProductPriceMonitorProduct().getStoreId())
			.collect(Collectors.toSet());

		if (storeIds.isEmpty()) {
			return new ArrayList<>();
		}

		// Retrieve roles for the specified store IDs with either MERCHANT or MERCHANT_ADMIN roles
		List<UserStoreRoleDo> userStoreRolesByStoreIdsAndRoleCodes = userStoreRepository.findUserStoreRolesByStoreIdsAndRoleCodes(
			new ArrayList<>(storeIds), List.of(RoleCode.MERCHANT, RoleCode.MERCHANT_ADMIN));

		// Group the merchant roles by storeId
		Map<Integer, List<UserStoreRoleDo>> userStoreRolesByStoreIdMap = userStoreRolesByStoreIdsAndRoleCodes.stream()
			.collect(Collectors.groupingBy(UserStoreRoleDo::getStoreId));

		Map<Integer, List<ProductPriceMonitorProductCheckDo>> recordsByStoreId = records.stream()
			.collect(Collectors.groupingBy(data -> data.getProductPriceMonitorProduct().getStoreId()));

		// Combine records and merchant roles by storeId
		Map<Integer, Pair<List<UserStoreRoleDo>, List<ProductPriceMonitorProductCheckDo>>> storeIdUserStoreRoleRecordsPairMap = storeIds.stream()
			.collect(Collectors.toMap(
				storeId -> storeId,
				storeId -> Pair.of(userStoreRolesByStoreIdMap.getOrDefault(storeId, new ArrayList<>()), recordsByStoreId.get(storeId))));

		List<ProductProductPriceAlertMerchantDto> alertMerchantsData = new ArrayList<>();

		// Process each store grouping to build the DTO
		storeIdUserStoreRoleRecordsPairMap.forEach((storeId, userStoreRoleRecordsPair) -> {
			List<UserStoreRoleDo> userStoreInfoList = userStoreRoleRecordsPair.getLeft();
			List<ProductPriceMonitorProductCheckDo> storeRecordList = userStoreRoleRecordsPair.getRight();

			if (userStoreInfoList != null && storeRecordList != null) {
				alertMerchantsData.add(ProductProductPriceAlertMerchantDto.builder()
					.productPriceAlerts(storeRecordList.stream()
						.map(ProductPriceAlertDto::from)
						.collect(Collectors.toList()))
					.receiverUsers(userStoreInfoList.stream()
						.map(ProductPriceAlertUserInfoDto::from)
						.collect(Collectors.toList()))
					.build());
			}
		});

		return alertMerchantsData;
	}

	private void updateHybrisProductMainlandSamePricePartition(String jobTraceUuid, Integer merchantId, String parmValue) {
		int pageSize = mmsDbPriceAlertDataSize;
		int pageNumber = 0;
		boolean hasNext = true;

		// PRICE_ALERT_CODE_CHECKING_ACTION: Enable(1)/Disable(0)
		if(parmValue.equals(ConstantType.PRICE_ALERT_CODE_CHECKING_ACTION_ON)) {
			while (hasNext) {
				log.info("[price-monitor/check-price] Updating Hybris product mainland same price jobTraceUuid: {}, pageNumber: {} ", jobTraceUuid, pageNumber);

				Pageable pageable = PageRequest.of(pageNumber, pageSize);
				Page<ProductPriceMonitorProductCheckDo> page =
					productPriceMonitorProductCheckRepository.findPageByStatusAndJobTraceUuidWithProductData(
						Set.of(MonitorProductCheckStatus.CHECKED.getValue()), jobTraceUuid, merchantId, pageable);

				// Find records for this job and merchant where samePriceFlag is 0 or null
				List<ProductPriceMonitorProductCheckDo> recordsToUpdatePageList = page.getContent().stream()
					.filter(samePriceRecord -> samePriceRecord.getSamePriceFlag() == null ||
						MonitorSamePriceStatus.NEED_RETRY.contains(samePriceRecord.getSamePriceFlag()))
					.collect(Collectors.toList());

				updateHybrisProductMainlandSamePrice(jobTraceUuid, merchantId, recordsToUpdatePageList);
				hasNext = page.hasNext();
				pageNumber++;
			}
		} else {
			log.info("[price-monitor/check-price] Skip Hybris product mainland same price jobTraceUuid: {}", jobTraceUuid);
		}
	}

	/**
	 * Call Hybris to update updateProductMainlandSamePrice flag
	 */
	private void updateHybrisProductMainlandSamePrice(String jobTraceUuid, Integer merchantId, List<ProductPriceMonitorProductCheckDo> recordsToUpdate) {
		long startTime = System.currentTimeMillis();
		log.info("[price-monitor/check-price] Updating Hybris product mainland same price for jobTraceUuid: {}, merchantId: {}", jobTraceUuid, merchantId);

		try {
			// Create a system user for API calls
			UserDto systemUser = UserDto.builder().userCode(SystemUserEnum.SYSTEM.name()).build();

			if (CollectionUtils.isEmpty(recordsToUpdate)) {
				log.info("[price-monitor/check-price] No records need mainland same price update for jobTraceUuid: {}, merchantId: {}",
					jobTraceUuid, merchantId);
				return;
			}

			log.info("[price-monitor/check-price] Found {} records requiring mainland same price update for merchantId: {}",
				recordsToUpdate.size(), merchantId);

			// Process in batches of hybrisApiUpdateMainlandSamePriceSize
			List<List<ProductPriceMonitorProductCheckDo>> batches = ListUtils.partition(recordsToUpdate,hybrisApiUpdateMainlandSamePriceSize);

			for (List<ProductPriceMonitorProductCheckDo> batch : batches) {
				// Prepare request data
				List<HybrisUpdateMainlandSamePriceRequestSkuDto> skuInfoList = batch.stream()
					.map(samePriceRecord ->
						// Create hybris request data for each record
						HybrisUpdateMainlandSamePriceRequestSkuDto.builder()
							.skuId(samePriceRecord.getProductPriceMonitorProduct().getStoreSkuId())
							.isMainlandSamePrice(true)
							.build())
					.distinct()
					.collect(Collectors.toList());

				// Call Hybris API
				ResponseDto<List<String>> response = hybrisHelper.requestUpdateProductMainlandSamePrice(systemUser, skuInfoList, jobTraceUuid);

				LocalDateTime now = LocalDateTime.now();

				// Update records based on response
				if (response.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
					List<String> updatedSkuIds = response.getData();
					log.info("[price-monitor/check-price] Successfully updated {} SKUs in Hybris",
						updatedSkuIds.size());

					// Update all records in this batch
					batch.forEach(samePriceRecord -> {
						String storeSkuId = samePriceRecord.getProductPriceMonitorProduct().getStoreSkuId();
						if (updatedSkuIds.contains(storeSkuId)) {
							// Successful update
							samePriceRecord.setSamePriceFlag(MonitorSamePriceStatus.TRUE.getValue());
						} else {
							// SKU was not in success list
							samePriceRecord.setSamePriceFlag(MonitorSamePriceStatus.ERROR.getValue());
							samePriceRecord.setErrorReason("SKU not found in Hybris response success list");
						}
						samePriceRecord.setLastUpdatedDate(now);
						samePriceRecord.setLastUpdatedBy(SystemUserEnum.SYSTEM.name());
					});
				} else {
					// Update all records to failure state
					String errorMessage = StringUtil.generateErrorMessage(response.getErrorMessageList());
					log.error("[price-monitor/check-price] Failed to update SKUs in Hybris: {}", errorMessage);

					batch.forEach(errorRecord -> {
						errorRecord.setSamePriceFlag(MonitorSamePriceStatus.ERROR.getValue());
						errorRecord.setErrorReason(errorMessage);
						errorRecord.setLastUpdatedDate(now);
						errorRecord.setLastUpdatedBy(SystemUserEnum.SYSTEM.name());
					});
				}

				// Save updated records
				productPriceMonitorProductCheckRepository.saveAll(batch);
			}
			long endTime = System.currentTimeMillis();
			log.info("[price-monitor/check-price] Completed updating Hybris product mainland same price, execution time: {} ms", (endTime - startTime));
		} catch (Exception e) {
			log.error("[price-monitor/check-price] Error updating Hybris product mainland same price: {}", e.getMessage(), e);
		}
	}

	private void createOfflineRecordsForRecords(String jobTraceUuid, Integer merchantId, String parmValue) {
		log.info("[price-monitor/check-price] Creating offline records for jobTraceUuid: {}, merchantId: {}", jobTraceUuid, merchantId);
		try {
			UserDto systemUser = tokenHelper.generateSystemUser(SystemUserEnum.SYSTEM);
			// find all PROCESSING records for the merchant
			List<ProductPriceMonitorProductCheckDo> needOfflineRecords = productPriceMonitorProductCheckRepository.findByStatusAndJobTraceUuidWithProductData(
				Set.of(MonitorProductCheckStatus.PROCESSING.getValue()),
				jobTraceUuid,
				merchantId);
			if (CollectionUtils.isEmpty(needOfflineRecords)) {
				return;
			} else {
				log.info("[price-monitor/check-price] Found {} records to create offline records for jobTraceUuid: {}, merchantId: {}", needOfflineRecords.size(), jobTraceUuid, merchantId);
			}

			// PRICE_ALERT_CODE_CHECKING_ACTION: Enable(1)/Disable(0)
			if(parmValue.equals(ConstantType.PRICE_ALERT_CODE_CHECKING_ACTION_ON))
			{
				// Group records by merchantId
				// note: batch edit helper create record for each merchantId, if there's multiple merchantId in one batch offline alert request, it will create multiple records
				// will be challenging to track back to the original record using recordId.
				// Therefore, we group the alerts by merchantId, and create one record for each merchantId
				Map<Integer, List<ProductPriceMonitorProductCheckDo>> recordsGroupedByMerchantId = needOfflineRecords.stream().collect(Collectors.groupingBy(checkDo -> checkDo.getProductPriceMonitorProduct().getMerchantId()));

				// Create offline records for products marked for offline for each merchant
				recordsGroupedByMerchantId.forEach((key, recordsPartition) -> {
					// batch edit helper create limit is 10,000, so we need to partition the alertsPartition into multiple parts
					ListUtils.partition(recordsPartition, BatchCheckHelper.MAXIMUM_10000)
						.forEach(recordsPartitionPartition -> createBatchOfflineRecordForRecords(recordsPartitionPartition, systemUser));
				});

				// save all check records
				productPriceMonitorProductCheckRepository.saveAll(needOfflineRecords);
			}else {
				log.info("[price-monitor/check-price] Skip create offline records for jobTraceUuid: {}", jobTraceUuid);
			}

		} catch (Exception e) {
			log.error("[price-monitor/check-price] Error creating offline records, jobTraceUuid: {}, merchantId: {}", jobTraceUuid, merchantId, e);
		}
	}

	private void createBatchOfflineRecordForRecords(List<ProductPriceMonitorProductCheckDo> recordsPartitionPartition, UserDto systemUser) {
		if (CollectionUtils.isEmpty(recordsPartitionPartition)) {
			return;
		}

		BatchEditProductRequestDto<BatchEditOnlineStatusDto> batchEditProductOfflineRequestDto = getBatchEditProductOfflineRequestDto(recordsPartitionPartition);
		ResponseDto<Set<Long>> batchEditRecord = batchEditHelper.createBatchEditRecord(systemUser, batchEditProductOfflineRequestDto, SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS, ClientIpHolder.getClientIp());

		if (Objects.equals(StatusCodeEnum.FAIL.getCode(), batchEditRecord.getStatus())) {
			// CREATE OFFLINE RECORD FAIL
			log.error("[BatchEditHelper] Error in create batch price monitor offline record: {}", StringUtil.generateErrorMessage(batchEditRecord.getErrorMessageList()));
			recordsPartitionPartition.forEach(failRecord -> {
				failRecord.setStatus(MonitorProductCheckStatus.OFFLINE_RECORD_CREATE_FAIL.getValue());
				failRecord.setErrorReason(StringUtil.generateErrorMessage(batchEditRecord.getErrorMessageList()));
			});
		} else {
			if (CollectionUtils.isNotEmpty(batchEditRecord.getData()) && batchEditRecord.getData().size() > 1) {
				// should not happen, only one record should be created for each batchEditProductRequestDto because already group by merchantId
				log.error("Error in create price-monitor offline record: more than one record created for batchEditProductRequestDto: {}, size: {}", batchEditProductOfflineRequestDto, batchEditRecord.getData().size());
			} else if (CollectionUtils.isEmpty(batchEditRecord.getData())) {
				// none partial record is created
				log.error("Error in create price-monitor offline record: no record created for batchEditProductRequestDto: {}", batchEditProductOfflineRequestDto);
				recordsPartitionPartition.forEach(failRecord -> {
					failRecord.setStatus(MonitorProductCheckStatus.OFFLINE_RECORD_CREATE_FAIL.getValue());
					failRecord.setErrorReason("no offline record created for batchEditProductRequestDto: " + batchEditProductOfflineRequestDto);
				});
			} else {
				// CREATE OFFLINE RECORD SUCCESS
				recordsPartitionPartition.forEach(successRecord -> {
					successRecord.setStatus(MonitorProductCheckStatus.OFFLINE_RECORD_CREATE.getValue());
					// set the recordId to the first element of the batchEditRecord.getData(): it should only have one element
					successRecord.setRecordId(batchEditRecord.getData().iterator().next());
				});
			}
		}
	}

	private BatchEditProductRequestDto<BatchEditOnlineStatusDto> getBatchEditProductOfflineRequestDto(List<ProductPriceMonitorProductCheckDo> recordsPartitionPartition) {
		List<BatchEditOnlineStatusDto> batchOfflineData = getBatchEditOnlineStatusDtos(recordsPartitionPartition, OnlineStatusEnum.OFFLINE);
		Integer merchantId = Optional.ofNullable(recordsPartitionPartition.get(0).getProductPriceMonitorProduct()).map(ProductPriceMonitorProductDo::getMerchantId).orElse(null);
		BatchEditProductRequestDto<BatchEditOnlineStatusDto> batchEditProductRequestDto = new BatchEditProductRequestDto<>();
		batchEditProductRequestDto.setFileName(String.format("price_alert_offline_merchant-%s_%s.xlsx", merchantId, LocalDateTime.now().format(ConstantType.YYYY_MM_DD_HH_MM_SS)));
		batchEditProductRequestDto.setData(batchOfflineData);
		return batchEditProductRequestDto;
	}

	private List<BatchEditOnlineStatusDto> getBatchEditOnlineStatusDtos(List<ProductPriceMonitorProductCheckDo> needOfflineRecords, OnlineStatusEnum onlineStatusEnum) {
		// fetch storefrontStoreCode by storeIds
		List<Integer> storeIds = needOfflineRecords.stream().map(ProductPriceMonitorProductCheckDo::getProductPriceMonitorProduct).map(ProductPriceMonitorProductDo::getStoreId).distinct().collect(Collectors.toList());
		// group by storeId: storefrontStoreCode
		Map<Integer, String> storeInfoMap = storeRepository.findAllByIdIn(storeIds).stream()
			.collect(Collectors.toMap(
				StoreDo::getId,
				StoreDo::getStorefrontStoreCode));
		// convert the records to batchEditProductRequestDto
		return needOfflineRecords.stream().map(needOfflineRecord -> {
			ProductPriceMonitorProductDo priceMonitorProduct = needOfflineRecord.getProductPriceMonitorProduct();
			BatchEditOnlineStatusDto batchEditOnlineStatusDto = new BatchEditOnlineStatusDto();
			batchEditOnlineStatusDto.setStorefrontStoreCode(storeInfoMap.get(priceMonitorProduct.getStoreId()));
			batchEditOnlineStatusDto.setSkuCode(priceMonitorProduct.getSkuCode());
			batchEditOnlineStatusDto.setOnlineStatus(onlineStatusEnum);
			return batchEditOnlineStatusDto;
		}).collect(Collectors.toList());
	}

	/**
	 * Process a group of products belonging to the same merchant
	 */
	protected void processProductGroup(String jobTraceUuid, List<ProductPriceMonitorProductDo> products, PriceConvertContent priceConvertContent, String buCode) {
		if (CollectionUtils.isEmpty(products)) {
			return;
		}

		// Extract storeSkuIds from all products
		List<String> storeSkuIds = products.stream()
			.map(ProductPriceMonitorProductDo::getStoreSkuId)
			.filter(StringUtils::isNotBlank)
			.collect(Collectors.toList());

		if (CollectionUtils.isEmpty(storeSkuIds)) {
			log.info("[price-monitor/check-price] No valid storeSkuIds found for product group");
			return;
		}

		// Fetch product data from ProductMaster in batches to avoid too large requests
		List<List<String>> batchedSkuIds = ListUtils.partition(storeSkuIds, PRODUCT_MASTER_SEARCH_SKU_BATCH_SIZE);

		// Create a system user for API calls
		UserDto systemUser = UserDto.builder().userCode(SystemUserEnum.SYSTEM.name()).build();

		// Process each batch
		try {
			for (List<String> batchStoreSkuIds : batchedSkuIds) {

				// Fetch product data from ProductMaster
				Map<String, ProductMasterResultDto> productMasterDataMap = fetchProductMasterData(systemUser, batchStoreSkuIds, buCode);

				if (productMasterDataMap == null || productMasterDataMap.isEmpty()) {
					log.warn("[price-monitor/check-price] No product master data found for batch");
					continue;
				}
				List<ProductPriceMonitorProductCheckDo> checkRecordsToSave = new ArrayList<>();

				for (ProductPriceMonitorProductDo monitorProductDo : products) {
					if (StringUtils.isBlank(monitorProductDo.getStoreSkuId()) || !batchStoreSkuIds.contains(monitorProductDo.getStoreSkuId())) {
						continue;
					}

					ProductMasterResultDto sourceData = productMasterDataMap.get(monitorProductDo.getStoreSkuId());

					// Create or update check record
					ProductPriceMonitorProductCheckDo checkRecord = createOrUpdateCheckRecord(jobTraceUuid, monitorProductDo, sourceData);

					// Check price against threshold
					if (checkRecord != null && sourceData != null) {
						// Source is HKTV, target is ThirdParty MALL
						BigDecimal percentageThreshold = priceConvertContent.getPercentageThreshold(monitorProductDo.getSourceCurrencyCode());
						BigDecimal exchangeRate = priceConvertContent.getExchangeRate(monitorProductDo.getSourceCurrencyCode());
						// if exchangeRate is not null, convert the source price to target(third-party) currency(HKD-> RMB)
						if (exchangeRate != null) {
							// Note: checkRecord.originalPrice/sellingPrice will be converted to RMB price if the exchangeRate is provided
							handleExchangeRate(exchangeRate, checkRecord);
						}

						BigDecimal sourcePrice = (checkRecord.getSourceSellingPrice() != null && checkRecord.getSourceSellingPrice().compareTo(BigDecimal.ZERO) > 0) ? checkRecord.getSourceSellingPrice() : checkRecord.getSourceOriginalPrice();
						BigDecimal targetPrice = (checkRecord.getTargetSellingPrice() != null && checkRecord.getTargetSellingPrice().compareTo(BigDecimal.ZERO) > 0) ? checkRecord.getTargetSellingPrice() : checkRecord.getTargetOriginalPrice();

						if (sourcePrice != null && targetPrice != null) {
							// Compare prices
							boolean exceedsThreshold = isSourcePriceOverAlertLimitation(sourcePrice, targetPrice, percentageThreshold);

							if (exceedsThreshold) {
								// If over threshold, mark for offline record creation
								log.info("[price-monitor/check-price] Price exceeds threshold for storeSkuId: {}, sourcePrice: {}, targetPrice: {}", monitorProductDo.getStoreSkuId(), sourcePrice, targetPrice);
								checkRecord.setStatus(MonitorProductCheckStatus.PROCESSING.getValue());
							} else {
								// If within threshold, mark as checked
								log.info("[price-monitor/check-price] Price within threshold for storeSkuId: {}, sourcePrice: {}, targetPrice: {}", monitorProductDo.getStoreSkuId(), sourcePrice, targetPrice);
								checkRecord.setStatus(MonitorProductCheckStatus.CHECKED.getValue());
							}
						}
					}

					// add checkRecord to the list to save
					if (checkRecord != null) {
						checkRecordsToSave.add(checkRecord);
					}
				}
				// Save all check records
				if (!checkRecordsToSave.isEmpty()) {
					productPriceMonitorProductCheckRepository.saveAll(checkRecordsToSave);
				}
			}
		} catch (Exception e) {
			log.error("[price-monitor/check-price] Error processing product group, jobTraceUuid: {}, merchantId: {}, skuCount: {}", jobTraceUuid, products.get(0).getMerchantId(), products.size(), e);
		}
	}

	/**
	 * update the source price to target currency(HKD-> RMB)
	 * if the exchangeRate is not null
	 * targetData.getOriginalPrice() = targetData.getOriginalPrice() / exchangeRate
	 */
	protected void handleExchangeRate(BigDecimal exchangeRate, ProductPriceMonitorProductCheckDo checkRecord) {
		checkRecord.setSourceSellingPrice(checkRecord.getSourceSellingPrice() == null ? null : checkRecord.getSourceSellingPrice().divide(exchangeRate, 4, RoundingMode.HALF_UP));
		checkRecord.setSourceOriginalPrice(checkRecord.getSourceOriginalPrice() == null ? null : checkRecord.getSourceOriginalPrice().divide(exchangeRate, 4, RoundingMode.HALF_UP));
	}


	/**
	 * Fetch product data from ProductMaster by storeSkuId list
	 */
	private Map<String, ProductMasterResultDto> fetchProductMasterData(UserDto systemUser, List<String> batchStoreSkuIds, String buCode) {
		try {
			FindStoreSkuIdProductRequestDto requestDto = FindStoreSkuIdProductRequestDto.builder()
				.storeSkuIds(batchStoreSkuIds)
				.buCode(buCode)
				.build();

			ProductMasterStoreSkuIdResponseDto response = productMasterHelper.requestProductByStoreSkuId(systemUser, requestDto);
			if (response == null || response.getStatus().equals(StatusCodeEnum.FAIL.name())) {
				return Map.of();
			}

			List<ProductMasterResultDto> productMasterResults = response.getData();

			if (CollectionUtils.isEmpty(productMasterResults)) {
				return Map.of();
			}

			return productMasterResults.stream()
				.filter(Objects::nonNull)
				.filter(p -> p.getAdditional() != null)
				.filter(p -> p.getAdditional().getHktv() != null)
				.filter(p -> p.getAdditional().getHktv().getStoreSkuId() != null)
				.collect(Collectors.toMap(
					p -> p.getAdditional().getHktv().getStoreSkuId(),
					p -> p,
					(a, b) -> a  // If duplicates, keep the first one
				));

		} catch (Exception e) {
			log.error("[price-monitor/check-price] Error fetching product master data: {}", e.getMessage(), e);
			return Map.of();
		}
	}

	private void createOrUpdatePriceNotFoundRecords(String jobTraceUuid, List<ProductPriceMonitorProductDo> productsWithPriceNotFound) {
		try {
			List<ProductPriceMonitorProductCheckDo> checkRecordsToSave = new ArrayList<>();
			productsWithPriceNotFound.forEach(monitorProductDo -> {
				Optional<ProductPriceMonitorProductCheckDo> existingRecord = productPriceMonitorProductCheckRepository.findByProductPriceMonitorProductId(monitorProductDo.getId());
				LocalDateTime now = LocalDateTime.now();
				if (existingRecord.isPresent()) {
					ProductPriceMonitorProductCheckDo existRecord = existingRecord.get();
					existRecord.setStatus(MonitorProductCheckStatus.NOT_FOUND.getValue());
					existRecord.setJobTraceUuid(jobTraceUuid);
					existRecord.setLastUpdatedDate(now);
					existRecord.setLastUpdatedBy(SystemUserEnum.SYSTEM.name());
					checkRecordsToSave.add(existRecord);
				} else {
					ProductPriceMonitorProductCheckDo checkRecord = ProductPriceMonitorProductCheckDo.builder()
						.productPriceMonitorProductId(monitorProductDo.getId())
						.status(MonitorProductCheckStatus.NOT_FOUND.getValue())
						.jobTraceUuid(jobTraceUuid)
						.createdDate(now)
						.createdBy(SystemUserEnum.SYSTEM.name())
						.lastUpdatedDate(now)
						.lastUpdatedBy(SystemUserEnum.SYSTEM.name())
						.build();
					checkRecordsToSave.add(checkRecord);
				}
			});

			if (!checkRecordsToSave.isEmpty()) {
				productPriceMonitorProductCheckRepository.saveAll(checkRecordsToSave);
			}
		} catch (Exception e) {
			log.error("[price-monitor/check-price] Error creating price not found records, jobTraceUuid: {}", jobTraceUuid, e);
		}
	}

	/**
	 * Create or update a check record for the product
	 */
	private ProductPriceMonitorProductCheckDo createOrUpdateCheckRecord(String jobTraceUuid, ProductPriceMonitorProductDo monitorProductDo, ProductMasterResultDto sourceData) {
		try {
			// Try to find existing check record
			Optional<ProductPriceMonitorProductCheckDo> existingRecord = productPriceMonitorProductCheckRepository.findByProductPriceMonitorProductId(monitorProductDo.getId());

			ProductPriceMonitorProductCheckDo checkRecord;
			LocalDateTime now = LocalDateTime.now();

			if (existingRecord.isPresent()) {
				// Update existing record
				checkRecord = existingRecord.get();
				checkRecord.setStatus(MonitorProductCheckStatus.PENDING.getValue());  // Reset status
				checkRecord.setJobTraceUuid(jobTraceUuid);
				checkRecord.setLastUpdatedDate(now);
				checkRecord.setLastUpdatedBy(SystemUserEnum.SYSTEM.name());

				// Clear price-related fields
				checkRecord.setSourceOriginalPrice(null);
				checkRecord.setSourceSellingPrice(null);
				checkRecord.setErrorReason(null);
				checkRecord.setErrorCode(null);
				checkRecord.setRecordId(null);
			} else {
				// Create new record
				checkRecord = ProductPriceMonitorProductCheckDo.builder()
					.productPriceMonitorProductId(monitorProductDo.getId())
					.status(MonitorProductCheckStatus.PENDING.getValue())
					.jobTraceUuid(jobTraceUuid)
					.createdDate(now)
					.createdBy(SystemUserEnum.SYSTEM.name())
					.lastUpdatedDate(now)
					.lastUpdatedBy(SystemUserEnum.SYSTEM.name())
					.build();
			}

			// Copy data from monitor product record
			checkRecord.setTargetOriginalPrice(monitorProductDo.getTargetOriginalPrice());
			checkRecord.setTargetSellingPrice(monitorProductDo.getTargetSellingPrice());
			checkRecord.setTargetUrl(monitorProductDo.getTargetUrl());
			checkRecord.setTargetPriceUpdatedDate(monitorProductDo.getTargetPriceUpdatedDate());

			// Copy source data if available
			if (sourceData != null) {
				checkRecord.setSourceSellingPrice(getSourcePrice(sourceData));
				checkRecord.setSourceOriginalPrice(sourceData.getOriginalPrice());
			}

			return checkRecord;

		} catch (Exception e) {
			log.error("[price-monitor/check-price] Error creating/updating check record for product {}: {}", monitorProductDo.getId(), e.getMessage(), e);
			return null;
		}
	}


	/**
	 * Get percentage threshold from system parameters
	 */
	private BigDecimal getPercentageThreshold(String code) {
		SysParmDo sysParmDo = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.PRICE_ALERT_PRICE_DIFF_RATE, code, 1).stream()
			.findFirst()
			.orElseThrow(() -> new SystemException("priceAlertPriceDiffRate not found for code: " + code));
		log.info("PRICE_ALERT_PRICE_DIFF_RATE, CODE = {}, RATE = {}", code, sysParmDo.getParmValue());
		return new BigDecimal(sysParmDo.getParmValue());
	}

	/**
	 * Compare if the difference between two prices exceeds the specified threshold percentage
	 */
	protected boolean isSourcePriceOverAlertLimitation(BigDecimal sourceSellingPrice, BigDecimal targetSellingPrice, BigDecimal percentageThreshold) {
		BigDecimal multiplier = BigDecimal.ONE.add(percentageThreshold.divide(new BigDecimal("100"), 10, RoundingMode.HALF_UP));
		return sourceSellingPrice.compareTo(targetSellingPrice.multiply(multiplier)) > 0;
	}

	/**
	 * Retrieves the effective price for the given product.
	 * Check if there is an active promotion for the product. If so and a discount price exists, return that price.
	 * If no valid promotion discount exists, use the selling price.
	 * If the selling price is missing or zero, fall back to the original price.
	 *
	 * @param sourceData the source product information
	 * @return effective product price as BigDecimal
	 */
	protected BigDecimal getSourcePrice(ProductMasterResultDto sourceData) {
		// Get the store SKU ID for promotion lookup
		String storeSkuId = sourceData.getAdditional().getHktv().getStoreSkuId();
		List<ProductStorePromotionDto> promotionsByStoreSkuId = productStorePromotionMapper.findActivePromotionsByStoreSkuId(storeSkuId, ConstantType.ACTIVE);
		// Early return if a valid promotion with a discount price is found
		if (CollectionUtils.isNotEmpty(promotionsByStoreSkuId)) {
			ProductStorePromotionDto promotion = promotionsByStoreSkuId.get(0);
			if (promotion.getDiscountPrice() != null) {
				log.info("Source product is a promotion product, will check promotion price for SKU {}", sourceData.getSkuId());
				return promotion.getDiscountPrice();
			}
		}

		return sourceData.getAdditional().getHktv().getSellingPrice();
	}

	/**
	 * Executes the offline check process for price monitored products
	 * Checks if the offline records have been successfully processed
	 */
	@Async
	public void offlineChecking() {
		log.info("[price-monitor/offline-checking] Starting offline check process");
		long startTime = System.currentTimeMillis();

		try {
			// 1. Retrieve all price monitor records with OFFLINE_RECORD_CREATE status
			List<ProductPriceMonitorProductCheckDo> checkOfflineMonitorRecords = productPriceMonitorProductCheckRepository
				.findByStatusAndActiveInd(MonitorProductCheckStatus.OFFLINE_RECORD_CREATE.getValue(), ActiveInd.ENABLE.getValue());

			if (checkOfflineMonitorRecords.isEmpty()) {
				log.info("[price-monitor/offline-checking] No records found with OFFLINE_RECORD_CREATE status");
				return;
			}

			log.info("[price-monitor/offline-checking] Found {} records with OFFLINE_RECORD_CREATE status", checkOfflineMonitorRecords.size());

			// 2. Get a distinct list of recordIds and retrieve corresponding offline records
			List<Long> offlineRecordIds = checkOfflineMonitorRecords.stream()
				.map(ProductPriceMonitorProductCheckDo::getRecordId)
				.filter(Objects::nonNull)
				.distinct()
				.collect(Collectors.toList());

			// Reminder: if offlineRecordIds is greater than 10000, it may cause performance issues
			List<SaveProductRecordDo> saveProductOfflineRecords = saveProductRecordRepository.findAllByIdIn(offlineRecordIds);
			log.info("[price-monitor/offline-checking] Found {} offline records to check, record_ids: [{}]", saveProductOfflineRecords.size(), saveProductOfflineRecords.stream().map(SaveProductRecordDo::getId).collect(Collectors.toList()));
			Map<Long, SaveProductRecordDo> offlineRecordMap;
			if (saveProductOfflineRecords.isEmpty()) {
				log.info("[price-monitor/offline-checking] No offline records found for the given record IDs");
				return;
			} else {
				offlineRecordMap = saveProductOfflineRecords.stream().collect(Collectors.toMap(SaveProductRecordDo::getId, Function.identity()));
			}

			// 3. Update the status of each record based on the offline record status
			checkOfflineMonitorRecords.stream()
				.filter(offlineRecord -> offlineRecord.getRecordId() != null)
				.filter(offlineRecord -> offlineRecordMap.containsKey(offlineRecord.getRecordId()))
				.forEach(offlineRecord -> updateProductMonitorStatus(offlineRecord, offlineRecordMap.get(offlineRecord.getRecordId())));

			// 4. Return status indicating offline check is completed
			long endTime = System.currentTimeMillis();
			log.info("[price-monitor/offline-checking] Completed offline checking process, execution time: {} ms",
				(endTime - startTime));
		} catch (Exception e) {
			log.error("[price-monitor/offline-checking] Error during offline checking process: {}", e.getMessage(), e);
		}
	}

	/**
	 * Updates the status of a product price monitor record based on the status of its offline record
	 */
	private void updateProductMonitorStatus(ProductPriceMonitorProductCheckDo offlineCheckRecord, SaveProductRecordDo saveProductRecordDo) {
		try {
			if (Objects.equals(SaveProductStatus.SUCCESS, saveProductRecordDo.getStatus())) {
				// Offline record processed successfully
				offlineCheckRecord.setStatus(MonitorProductCheckStatus.OFFLINE.getValue());
				log.info("[price-monitor/offline-checking] Record ID {} successfully processed offline", offlineCheckRecord.getRecordId());
				offlineCheckRecord.setLastUpdatedBy(SystemUserEnum.SYSTEM.name());
				offlineCheckRecord.setLastUpdatedDate(LocalDateTime.now());
				productPriceMonitorProductCheckRepository.save(offlineCheckRecord);
			} else if (Objects.equals(SaveProductStatus.FAIL, saveProductRecordDo.getStatus())) {
				// Offline record processing failed
				offlineCheckRecord.setStatus(MonitorProductCheckStatus.OFFLINE_FAIL.getValue());
				offlineCheckRecord.setErrorReason("Offline record processing failed, record ID: " + offlineCheckRecord.getRecordId());
				log.warn("[price-monitor/offline-checking] Offline record processing failed for record ID: {}", offlineCheckRecord.getRecordId());
				offlineCheckRecord.setLastUpdatedBy(SystemUserEnum.SYSTEM.name());
				offlineCheckRecord.setLastUpdatedDate(LocalDateTime.now());
				productPriceMonitorProductCheckRepository.save(offlineCheckRecord);
			} else {
				// Offline record still in processing
				log.info("[price-monitor/offline-checking] Record ID {} is still in processing with status: {}", offlineCheckRecord.getRecordId(), saveProductRecordDo.getStatus());
			}

			// Save history record if in final state
			if (MonitorProductCheckStatus.OFFLINE_CHECK_FINAL_STATUSES.contains(offlineCheckRecord.getStatus())) {
				ProductPriceMonitorProductCheckHistoryDo historyDo = getProductPriceMonitorProductCheckHistoryDo(offlineCheckRecord, LocalDateTime.now());
				productPriceMonitorProductCheckHistoryRepository.save(historyDo);
			}
		} catch (Exception e) {
			log.error("[price-monitor/offline-checking] Error updating offline record status for record ID {}: {}", offlineCheckRecord.getRecordId(), e.getMessage(), e);
		}
	}
}
