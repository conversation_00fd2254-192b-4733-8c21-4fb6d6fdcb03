package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class PackingBoxTypeHelper {

	private final SysParmRepository sysParmRepository;

	public String generatePackingTypeRestrictionCode(String buCode, String categoryCode) {
		List<SysParmDo> restrictionList = sysParmRepository.findBySegmentAndBuCode("CATEGORY_RESTRICTION", buCode);
		for (SysParmDo restriction : restrictionList) {
			if (StringUtil.isNotEmpty(restriction.getParmValue())) {
				String[] restrictionCategoryCodes = restriction.getParmValue().split(",");
				for (String restrictedCategoryCode : restrictionCategoryCodes) {
					if (categoryCode.contains(restrictedCategoryCode)) {
						return restriction.getCode();
					}
				}
			}
		}
		return null;
	}
}
