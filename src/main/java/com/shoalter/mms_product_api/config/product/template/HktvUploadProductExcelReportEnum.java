package com.shoalter.mms_product_api.config.product.template;

import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import lombok.Getter;

@Getter
public enum HktvUploadProductExcelReportEnum {
    PRODUCT_CODE(0, "Product ID", null, null, null),
    SKU_CODE(1, "Sku ID", null, null, null),
    PRODUCT_HKTV_CAT_LIST(2, "Product Type Code", null, null, null),
    PRODUCT_HKTV_CAT_CODE_PRI(3, "Primary Category Code", null, null, null),
    BRAND_ID(4, "Brand", ExcelValidationName.VALIDATION_BRAND_NAME_ENG, null, null),
    PRODUCT_READY_METHOD(5, "Product Ready Method", ExcelValidationName.VALIDATION_PRO_READY_METHOD, null, null),
    ID(6, "ID", null, null, null),
	SKU_STATUS(7, "SKU Status", ExcelValidationName.VALIDATION_PRODUCT_STATUS, null, null),
    WAREHOUSE(8, "Warehouse", ExcelValidationName.VALIDATION_WAREHOUSE, null, null),
    TERM_NAME(9, "Term Name", ExcelValidationName.VALIDATION_TERM_NAME, null, null),
    IS_PRIMARY_SKU(10, "Is Primary SKU", ExcelValidationName.VALIDATION_YES_NO, null, null),
    SKU_NAME(11, "SKU Name", null, null, null),
    SKU_NAME_TCHI(12, "SKU Name Chi", null, null, null),
	SKU_NAME_SCHI(13, "SKU Name (SC)", null, null, null),
    SKU_S_TITLE_HKTV_EN(14, "SKU Short Description Title (Eng)", null, null, null),
    SKU_S_TITLE_HKTV_CH(15, "SKU Short Description Title (Chi)", null, null, null),
    SKU_S_DESC_HKTV_EN(16, "SKU Short Description (Eng)", null, null, null),
    SKU_S_DESC_HKTV_CH(17, "SKU Short Description (Chi)", null, null, null),
    SKU_S_DESC_HKTV_SC(18, "SKU Short Description (SC)", null, null, null),
    SKU_L_TITLE_HKTV_EN(19, "SKU Long Description Title (Eng)", null, null, null),
    SKU_L_TITLE_HKTV_CH(20, "SKU Long Description Title (Chi)", null, null, null),
    SKU_L_DESC_HKTV_EN(21, "SKU Long Description (Eng)", null, null, null),
    SKU_L_DESC_HKTV_CH(22, "SKU Long Description (Chi)", null, null, null),
    SKU_L_DESC_HKTV_SC(23, "SKU Long Description (SC)", null, null, null),
    MAIN_PHOTO_HKTVMALL(24, "Main photo", null, null, null),
    MAIN_VIDEO(25, "Main Video", null, null, null),
    OTHER_PRODUCT_PHOTO_HKTVMALL(26, "Other product photo", null, null, null),
    OTHER_PHOTO_HKTVMALL(27, "Other photo", null, null, null),
    ADVERTISING_PHOTO_HKTVMALL(28, "Advertising photo", null, null, null),
    VIDEO_LINK(29, "Video Link", null, null, null),
    VIDEO_LINK_EN(30, "Video Link Text (Eng)", null, null, null),
    VIDEO_LINK_CH(31, "Video Link Text (Chi)", null, null, null),
    VIDEO_LINK_SC(32, "Video Link Text (SC)", null, null, null),
    VIDEO_LINK2(33, "Video Link2", null, null, null),
    VIDEO_LINK_EN2(34, "Video Link Text (Eng)2", null, null, null),
    VIDEO_LINK_CH2(35, "Video Link Text (Chi)2", null, null, null),
    VIDEO_LINK_SC2(36, "Video Link Text (SC)2", null, null, null),
    VIDEO_LINK3(37, "Video Link3", null, null, null),
    VIDEO_LINK_EN3(38, "Video Link Text (Eng)3", null, null, null),
    VIDEO_LINK_CH3(39, "Video Link Text (Chi)3", null, null, null),
    VIDEO_LINK_SC3(40, "Video Link Text (SC)3", null, null, null),
    VIDEO_LINK4(41, "Video Link4", null, null, null),
    VIDEO_LINK_EN4(42, "Video Link Text (Eng)4", null, null, null),
    VIDEO_LINK_CH4(43, "Video Link Text (Chi)4", null, null, null),
    VIDEO_LINK_SC4(44, "Video Link Text (SC)4", null, null, null),
    VIDEO_LINK5(45, "Video Link5", null, null, null),
    VIDEO_LINK_EN5(46, "Video Link Text (Eng)5", null, null, null),
    VIDEO_LINK_CH5(47, "Video Link Text (Chi)5", null, null, null),
    VIDEO_LINK_SC5(48, "Video Link Text (SC)5", null, null, null),
    MANU_COUNTRY(49, "Manufactured Country", ExcelValidationName.VALIDATION_MANU_COUNTRY, null, null),
    COLOR_FAMILIAR(50, "Colour Families", ExcelValidationName.VALIDATION_COLOR_FAMILIES, null, null),
    COLOR_EN(51, "Color (Eng)", null, COLOR_FAMILIAR, SysParmSegment.COLOR_FAMILIES),
    COLOR_CH(52, "Color (Chi)", null, null, null),
    SIZE_SYSTEM(53, "Size System", ExcelValidationName.VALIDATION_SIZE_SYSTEM, null, null),
    SIZE(54, "Size", null, SIZE_SYSTEM, SysParmSegment.SIZE_SYSTEM),
    CURRENCY_CODE(55, "Currency", ExcelValidationName.VALIDATION_CURRENCY, null, null),
    COST(56, "Cost", null, null, null),
    ORIGINAL_PRICE(57, "Original Price", null, null, null),
    SELLING_PRICE(58, "Selling Price", null, null, null),
    MALL_DOLLAR(59, "Mall Dollar (% of selling price)", null, null, null),
    MALL_DOLLAR_VIP(60, "VIP Mall Dollar (% of selling price)", null, null, null),
    USER_MAX(61, "User Max", null, null, null),
    STYLE(62, "Style", ExcelValidationName.VALIDATION_STYLE, null, null),
    DISCOUNT_TEXT(63, "Discount Text (Eng)", null, null, null),
    DISCOUNT_TEXT_ZH(64, "Discount Text (Chi)", null, null, null),
	DISCOUNT_TEXT_SC(65, "Discount Text (SC)", null, null, null),
    PACK_SPEC_EN(66, "Packing Spec (Eng)", null, null, null),
    PACK_SPEC_CH(67, "Packing Spec (Chi)", null, null, null),
	PACK_SPEC_SC(68, "Packing Spec (SC)", null, null, null),
    PACK_HEIGHT(69, "Packing Height", null, null, null),
    PACK_LENGTH(70, "Packing Length", null, null, null),
    PACK_DEPTH(71, "Packing Depth", null, null, null),
    PACK_DIMENSION_UNIT(72, "Packing Dimension Unit", ExcelValidationName.VALIDATION_PACK_DIMENSION_UNIT, null, null),
    WEIGHT(73, "Weight", null, null, null),
    WEIGHT_UNIT(74, "Weight Unit", ExcelValidationName.VALIDATION_WEIGHT_UNIT, null, null),
    PACK_BOX_TYPE(75, "Packing Box Type", ExcelValidationName.VALIDATION_PACK_BOX_TYPE, null, null),
    CARTON_HEIGHT(76, "Carton Height(mm)", null, null, null),
    CARTON_DEPTH(77, "Carton Depth(mm)", null, null, null),
    CARTON_LENGTH(78, "Carton Length(mm)", null, null, null),
    INVISIBLE_FLAG(79, "Invisible flag", ExcelValidationName.VALIDATION_YES_NO, null, null),
    BARCODE(80, "Barcode", null, null, null),
    FEATURE_START_TIME(81, "Feature Start Date/Time\n(yyyy-MM-dd HH:00)", null, null, null),
    FEATURE_END_TIME(82, "Feature End Date/Time\n(yyyy-MM-dd HH:00)", null, null, null),
    VOUCHER_TYPE(83, "Voucher Type", ExcelValidationName.VALIDATION_VOUCHER_TYPE, null, null),
    VOUCHER_DISPLAY_TYPE(84, "Voucher Display Type", ExcelValidationName.VALIDATION_VOUCHER_DISPLAY_TYPE, null, null),
    VOUCHER_TEMPLATE_TYPE(85, "Voucher Template Type", ExcelValidationName.VALIDATION_VOUCHER_TEMPLATE_TYPE, null, null),
    EXPIRY_TYPE(86, "Expiry Type", ExcelValidationName.VALIDATION_EXPIRY_TYPE, null, null),
    CONSUMABLE(87, "Consumable", null, null, null),
    PRIORITY(88, "Priority", null, null, null),
    FIELD1(89, "Field 1", ExcelValidationName.VALIDATION_PRODUCT_FIELD, null, null),
    VALUE1(90, "Value 1", null, FIELD1, SysParmSegment.PRODUCT_FIELD),
    FIELD2(91, "Field 2", ExcelValidationName.VALIDATION_PRODUCT_FIELD, null, null),
    VALUE2(92, "Value 2", null, FIELD2, SysParmSegment.PRODUCT_FIELD),
    FIELD3(93, "Field 3", ExcelValidationName.VALIDATION_PRODUCT_FIELD, null, null),
    VALUE3(94, "Value 3", null, FIELD3, SysParmSegment.PRODUCT_FIELD),
    REDEEM_START_DATE(95, "Redeem Start Date\n(yyyy-MM-dd)", null, null, null),
    REDEEM_END_DATE(96, "Fixed Redemption Date \n(yyyy-MM-dd)", null, null, null),
    UPON_PURCHASE_DATE(97, "Upon Purchase Date", null, null, null),
    FINE_PRINT_TITLE_EN(98, "Fine Print Title(Eng)", null, null, null),
    FINE_PRINT_TITLE_CH(99, "Fine Print Title(Chi)", null, null, null),
    FINE_PRINT_EN(100, "Fine Print (Eng)", null, null, null),
    FINE_PRINT_CH(101, "Fine Print (Chi)", null, null, null),
    FINE_PRINT_SC(102, "Fine Print (SC)", null, null, null),
    REMOVAL_SERVICES(103, "Need Removal Services", ExcelValidationName.VALIDATION_YES_NO, null, null),
    GOODS_TYPE(104, "Goods Type", ExcelValidationName.VALIDATION_GOODS_TYPE, null, null),
    WARRANTY_PERIOD_UNIT(105, "Warranty Period Unit", ExcelValidationName.VALIDATION_WARRANTY_PERIOD_UNIT, null, null),
    WARRANTY_PERIOD(106, "Warranty Period", null, null, null),
    WARRANTY_SUPPLIER_EN(107, "Warranty Supplier(Eng)", null, null, null),
    WARRANTY_SUPPLIER_CH(108, "Warranty Supplier(Chi)", null, null, null),
    WARRANTY_SUPPLIER_SC(109, "Warranty Supplier(SC)", null, null, null),
    SERVICE_CENTRE_ADDRESS_EN(110, "Service Centre Address(Eng)", null, null, null),
    SERVICE_CENTRE_ADDRESS_CH(111, "Service Centre Address(Chi)", null, null, null),
    SERVICE_CENTRE_ADDRESS_SC(112, "Service Centre Address(SC)", null, null, null),
    SERVICE_CENTRE_EMAIL(113, "Service Centre Email", null, null, null),
    SERVICE_CENTRE_CONTACT(114, "Service Centre Contact", null, null, null),
    WARRANTY_REMARK_EN(115, "Warranty Remark (Eng)", null, null, null),
    WARRANTY_REMARK_CH(116, "Warranty Remark (Chi)", null, null, null),
    WARRANTY_REMARK_SC(117, "Warranty Remark (SC)", null, null, null),
    INVOICE_REMARKS_EN(118, "Invoice Remarks (Eng)", null, null, null),
    INVOICE_REMARKS_CH(119, "Invoice Remarks (Chi)", null, null, null),
    INVOICE_REMARKS_SC(120, "Invoice Remarks (SC)", null, null, null),
    RETURN_DAYS(121, "Return Days", ExcelValidationName.VALIDATION_RETURN_DAYS, null, null),
    PRODUCT_READY_DAYS(122, "Product Ready Days", ExcelValidationName.VALIDATION_PRODUCT_READY_DAYS, null, null),
    PICKUP_DAYS(123, "Pickup Days", ExcelValidationName.VALIDATION_PICKUP_DAYS, null, null),
    PICKUP_TIMESLOT(124, "Pickup Timeslot", ExcelValidationName.VALIDATION_PICKUP_TIMESLOT, null, null),
    OVERSEA_DELIVERY(125, "Oversea Delivery", null, null, null),
    URGENT(126, "Urgent", ExcelValidationName.VALIDATION_YES_NO, null, null),
    DELIVERY_TITLE_EN(127, "Delivery Title (Eng)", null, null, null),
    DELIVERY_TITLE_CH(128, "Delivery Title (Chi)", null, null, null),
    DELIVERY_DETAILS_EN(129, "Delivery Details (Eng)", null, null, null),
    DELIVERY_DETAILS_CH(130, "Delivery Details (Chi)", null, null, null),
    DELIVERY_COMPLETION_DAYS(131, "Delivery Completion Days", null, null, null),
    MINIMUM_SHELF_LIFE(132, "Minimum Shelf Life", null, null, null),
    VIRTUAL_STORE(133, "Virtual Store", ExcelValidationName.VALIDATION_VIRTUAL_STORE_MERCHANT, null, null),
    RM_CODE(134, "RM Code", null, null, null),
    STORAGE_TYPE(135, "Storage Type", ExcelValidationName.VALIDATION_STORAGE_TYPE, null, null),
    PRE_SELL_FRUIT(136, "PreSell Fruit", ExcelValidationName.VALIDATION_YES_NO, null, null),
	PHYSICAL_STORE(137, "Physical Store\n(separate by , )", null, null, null),
	AFFILIATE_URL(138, "Affiliate Url", null, null, null),
	EW_PERCENTAGE_SETTING(139, "EW Percentage Setting ", null, null, null),
	CLAIM_LINK_EN(140, "Claim Link(EN)", null, null, null),
	CLAIM_LINK_CH(141, "Claim Link(CH)", null, null, null),
	CLAIM_LINK_SC(142, "Claim Link(SC)", null, null, null),
	PARTNER_PLATFORM(143, "Partner Platform", null, null, null),
	PARTNER_PRODUCT_ID(144, "Partner Product ID", null, null, null),
	PARTNER_SKU_ID(145, "Partner SKU ID", null, null, null);


    private final Integer columnNumber;
    private final String columnName;
    private final String validationName;
    private final HktvUploadProductExcelReportEnum parent;
    private final String parentSegment;

    HktvUploadProductExcelReportEnum(Integer columnNumber, String columnName, String validationName, HktvUploadProductExcelReportEnum parent, String parentSegment) {
        this.columnNumber = columnNumber;
        this.columnName = columnName;
        this.validationName = validationName;
        this.parent = parent;
        this.parentSegment = parentSegment;
    }
}
