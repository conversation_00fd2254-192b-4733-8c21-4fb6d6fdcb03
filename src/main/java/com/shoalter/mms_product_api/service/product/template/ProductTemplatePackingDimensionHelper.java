package com.shoalter.mms_product_api.service.product.template;


import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.product.edit_column.PackingDimensionTemplateColumnEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.BusinessMapper;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.pojo.BusinessPlatformDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditPackingDimensionDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.SetCellValueDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import com.shoalter.mms_product_api.util.SysParmUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static com.shoalter.mms_product_api.util.ExcelUtil.PACK_DIMENSION_UNIT_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.WEIGHT_UNIT_LIST;

@RequiredArgsConstructor
@Service
@Slf4j
public class ProductTemplatePackingDimensionHelper extends AbstractReport implements IProductTemplateHelper<PackingDimensionTemplateColumnEnum> {

	private final BusinessMapper businessMapper;
	private final Gson gson;

	public Workbook setTemplateBodyColumn(Workbook workbook, List<SingleEditProductDto> productList, List<SysParmDo> sysParmList) {
		Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook, 100);

		BusinessPlatformDo businessPlatformDo =
			businessMapper.findByBusinessCode(ConstantType.PLATFORM_CODE_HKTV).orElse(new BusinessPlatformDo());
		Integer platformId = businessPlatformDo.getPlatformId();

		Map<String, String> packBoxTypeMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PACK_BOX_TYPE, platformId);

		Sheet dataSheet = tempWorkbook.getSheet(PRODUCT_SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		AtomicInteger rowIndex = new AtomicInteger(1);

		productList.forEach(singleEditProductDto -> {
			ProductMasterDto product = singleEditProductDto.getProduct();
			HktvProductDto hktvProductDto = product.getAdditional().getHktv();

			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.STORE_ID.getColumnNumber(), hktvProductDto.getStores());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.SKU_ID.getColumnNumber(), product.getSkuId());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.PACKING_SPEC_ENG.getColumnNumber(), hktvProductDto.getPackingSpecEn());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.PACKING_SPEC_CHI.getColumnNumber(), hktvProductDto.getPackingSpecCh());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.PACKING_SPEC_SC.getColumnNumber(), hktvProductDto.getPackingSpecSc());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.PACKING_HEIGHT.getColumnNumber(), convertDecimalToDouble(product.getPackingHeight()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.PACKING_LENGTH.getColumnNumber(), convertDecimalToDouble(product.getPackingLength()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.PACKING_DEPTH.getColumnNumber(), convertDecimalToDouble(product.getPackingDepth()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.PACKING_DIMENSION_UNIT.getColumnNumber(), product.getPackingDimensionUnit());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.PACKING_BOX_TYPE.getColumnNumber(), packBoxTypeMap.get(product.getPackingBoxType()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.WEIGHT.getColumnNumber(), convertDecimalToDouble(product.getWeight()));
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.WEIGHT_UNIT.getColumnNumber(), product.getWeightUnit());
			if (CollectionUtil.isNotEmpty(product.getCartonSizeList())) {
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.CARTON_HEIGHT.getColumnNumber(), product.getCartonSizeList().get(0).getHeight());
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.CARTON_DEPTH.getColumnNumber(), product.getCartonSizeList().get(0).getWidth());
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.CARTON_LENGTH.getColumnNumber(), product.getCartonSizeList().get(0).getLength());
			} else {
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.CARTON_HEIGHT.getColumnNumber(), "");
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.CARTON_DEPTH.getColumnNumber(), "");
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.CARTON_LENGTH.getColumnNumber(), "");
			}
			rowIndex.addAndGet(1);
		});
		return tempWorkbook;
	}

	@Override
	public Workbook setErrorReportBodyColumn(Workbook workbook, List<SaveProductRecordRowDo> rows, List<SysParmDo> sysParmList, SaveProductRecordDo record) {
		Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook, 100);

		BusinessPlatformDo businessPlatformDo =
			businessMapper.findByBusinessCode(ConstantType.PLATFORM_CODE_HKTV).orElse(new BusinessPlatformDo());
		Integer platformId = businessPlatformDo.getPlatformId();

		Map<String, String> packBoxTypeMap = addCodeAndShortDescMapByCode(sysParmList, SysParmSegment.PACK_BOX_TYPE, platformId);

		Sheet dataSheet = tempWorkbook.getSheet(PRODUCT_SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		CellStyle lockBodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		lockBodyStyle.setLocked(true);
		AtomicInteger rowIndex = new AtomicInteger(1);
		SetCellValueDto setCellValueDto = SetCellValueDto.builder()
			.sheet(dataSheet)
			.lockStyle(lockBodyStyle)
			.notLockStyle(bodyStyle)
			.build();

		rows.forEach(row -> {
			setCellValueDto.setRowNumber(rowIndex.get());
			SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
			BatchEditPackingDimensionDto batchEditPackingDimensionDto = singleEditProductDto.getBatchEditElement().getPackingDimension();

			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.STORE_ID.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.STORE_ID), batchEditPackingDimensionDto.getStorefrontStoreCode());
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.SKU_ID.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.SKU_ID), batchEditPackingDimensionDto.getSkuCode());
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.PACKING_SPEC_ENG.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.PACKING_SPEC_ENG), batchEditPackingDimensionDto.getPackingSpecEn());
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.PACKING_SPEC_CHI.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.PACKING_SPEC_CHI), batchEditPackingDimensionDto.getPackingSpecCh());
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.PACKING_SPEC_SC.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.PACKING_SPEC_SC), batchEditPackingDimensionDto.getPackingSpecSc());
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.PACKING_HEIGHT.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.PACKING_HEIGHT), convertDecimalToDouble(batchEditPackingDimensionDto.getPackingHeight()));
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.PACKING_LENGTH.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.PACKING_LENGTH), convertDecimalToDouble(batchEditPackingDimensionDto.getPackingLength()));
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.PACKING_DEPTH.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.PACKING_DEPTH), convertDecimalToDouble(batchEditPackingDimensionDto.getPackingDepth()));
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.PACKING_DIMENSION_UNIT.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.PACKING_DIMENSION_UNIT), batchEditPackingDimensionDto.getPackingDimensionUnit());
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.PACKING_BOX_TYPE.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.PACKING_BOX_TYPE), packBoxTypeMap.get(batchEditPackingDimensionDto.getPackingBoxType()));
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.WEIGHT.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.WEIGHT), convertDecimalToDouble(batchEditPackingDimensionDto.getWeight()));
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.WEIGHT_UNIT.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.WEIGHT_UNIT), batchEditPackingDimensionDto.getWeightUnit());
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.CARTON_HEIGHT.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.CARTON_HEIGHT), batchEditPackingDimensionDto.getCartonHeight());
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.CARTON_DEPTH.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.CARTON_DEPTH), batchEditPackingDimensionDto.getCartonDepth());
			setCellValue(setCellValueDto, PackingDimensionTemplateColumnEnum.CARTON_LENGTH.getColumnNumber(), isLockColumn(PackingDimensionTemplateColumnEnum.CARTON_LENGTH), batchEditPackingDimensionDto.getCartonLength());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), PackingDimensionTemplateColumnEnum.values().length, row.getErrorMessage());
			rowIndex.addAndGet(1);
		});
		return tempWorkbook;
	}

	@Override
	public int getColumnWidth(TemplateInterface<PackingDimensionTemplateColumnEnum> columnEnum) {
		if (columnEnum == PackingDimensionTemplateColumnEnum.SKU_ID) {
			return 7000;
		}
		return columnEnum.getColumnName().length() * 400;
	}

	@Override
	public boolean isLockColumn(TemplateInterface<PackingDimensionTemplateColumnEnum> columnEnum) {
		return columnEnum == PackingDimensionTemplateColumnEnum.STORE_ID ||
			columnEnum == PackingDimensionTemplateColumnEnum.SKU_ID;
	}

	@Override
	public void setLoveSheetValues(Sheet lovSheet, Map<String, Map<String, Object>> map, Num theColNum, Pair<BusinessPlatformDo, List<SysParmDo>> necessaryDataPair) {
		Integer platformId = necessaryDataPair.getLeft().getPlatformId();
		List<SysParmDo> sysParmList = necessaryDataPair.getRight();
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PACK_DIMENSION_UNIT, PACK_DIMENSION_UNIT_LIST, theColNum, map);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_WEIGHT_UNIT, WEIGHT_UNIT_LIST, theColNum, map);
		List<String> packBoxTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PACK_BOX_TYPE, platformId);
		List<String> packBoxThirdTypeList = SysParmUtil.getParamCodeAndShortDescList(sysParmList, SysParmSegment.PACK_BOX_TYPE_3PL_SKU, platformId);
		packBoxTypeList.addAll(packBoxThirdTypeList);
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PACK_BOX_TYPE, packBoxTypeList, theColNum, map);
	}

}
