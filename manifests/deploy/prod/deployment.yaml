apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: mms-product-api
  name: mms-product-api
  namespace: mms
spec:
  progressDeadlineSeconds: 600
  # replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: mms-product-api
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: mms-product-api
    spec:
      containers:
        - envFrom:
            - configMapRef:
                name: mms-product-configmap
              prefix: mms_product_configmap_
            - prefix: mms_product_secret_
              secretRef:
                name: mms-product-secret
          image: ite-git01.hktv.com.hk:5000/hktv/tw/mms/mms_product_api:20240206_1700
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /product/actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 180
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 3
          name: mms-product-api
          ports:
            - containerPort: 8080
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /product/actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 3
          resources:
            limits:
              cpu: "16"
              memory: 64Gi
            requests:
              cpu: "2"
              memory: 64Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /rakuten
              name: mms-product-api-upload
      volumes:
        - name: mms-product-api-upload
          persistentVolumeClaim:
            claimName: mms-product-api-pvc
            readOnly: false
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
