package com.shoalter.mms_product_api.service.product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.dao.mapper.product.ProductMapper;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.merchant.MerchantStoreRepository;
import com.shoalter.mms_product_api.exception.BadRequestException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditStoreOnlineStatusDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchUpdateOnlineStatusForStoreRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SkuMapUuidDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class BatchUpdateOnlineStatusForStoreService {

    private final MerchantStoreRepository merchantStoreRepository;
    private final SaveProductRecordRepository saveProductRecordRepository;
    private final ProductMapper productMapper;

    private final PermissionHelper permissionHelper;
    private final SaveProductRecordRowHelper saveProductRecordRowHelper;
    private final SaveProductRecordHelper saveProductRecordHelper;

    private final Gson gson;


	public ResponseDto<Long> start(UserDto userDto, Integer storeId,
		BatchUpdateOnlineStatusForStoreRequestDto requestDto) {

		Integer merchantId = merchantStoreRepository.findMerchantIdByStoreId(storeId);

		permissionHelper.checkPermission(userDto, merchantId);
		checkRequest(requestDto);

		String fileName =
			String.format("BATCH_EDIT_STORE_ONLINE_STATUS_%d_%d.xlsx",
				storeId,
				System.currentTimeMillis());

		SaveProductRecordDo record =
			saveProductRecordHelper.createSaveProductRecord(
				userDto,
				merchantId,
				SaveProductType.BATCH_EDIT_STORE_ONLINE_STATUS,
				fileName,
				SaveProductStatus.WAIT_START,
				ClientIpHolder.getClientIp()
			);

		generateSaveProductRecordRow(record, storeId, requestDto);
		saveProductRecordHelper.updateRecordStatusToChecking(record);

		return ResponseDto.<Long>builder().data(record.getId()).build();
	}

    private void checkRequest(BatchUpdateOnlineStatusForStoreRequestDto requestDto) {
        if (requestDto == null || requestDto.getOnlineStatus() == null) {
            throw new BadRequestException("Missing required parameter");
        }
    }

    private void generateSaveProductRecordRow(SaveProductRecordDo record, Integer storeId, BatchUpdateOnlineStatusForStoreRequestDto requestDto) {
        //find store all product
        List<SkuMapUuidDto> skuMapUuidDtoList = productMapper.getUuidAndSkuByStoreId(storeId);
        BatchEditStoreOnlineStatusDto batchEditStoreOnlineStatusDto = new BatchEditStoreOnlineStatusDto();
        batchEditStoreOnlineStatusDto.setStoreId(storeId);
        batchEditStoreOnlineStatusDto.setOnlineStatus(requestDto.getOnlineStatus());
        String content = gson.toJson(batchEditStoreOnlineStatusDto);

        List<SaveProductRecordRowDo> rowList = skuMapUuidDtoList.stream().map(skuMapUuidDto -> {
            SaveProductRecordRowDo row = new SaveProductRecordRowDo();
            row.setRecordId(record.getId());
            row.setSku(skuMapUuidDto.getSkuCode());
            row.setUuid(skuMapUuidDto.getUuid());
            row.setStatus(SaveProductStatus.CHECKING_PRODUCT);
            row.setContent(content);
            return row;
        }).collect(Collectors.toList());

        saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(rowList);
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", record.getId(), SaveProductTypeEnum.getProductTypeName(record.getUploadType()), skuMapUuidDtoList.size(), record.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(record.getStatus()));
    }

}
