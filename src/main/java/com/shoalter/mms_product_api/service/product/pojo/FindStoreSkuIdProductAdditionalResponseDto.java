package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

@Data
public class FindStoreSkuIdProductAdditionalResponseDto implements Serializable {
	private static final long serialVersionUID = 2151516118648461338L;

    // multiple bu in additional
    private FindStoreSkuIdProductHktvResponseDto hktv;

    @SerializedName("3pl")
    @JsonProperty("3pl")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ThirdPLProductDto thirdParty;
	@JsonProperty("little_mall")
	@SerializedName("little_mall")
	private LittleMallProductDto littleMall;
	private ProductMasterIIDSBuDto iids;
}
