package com.shoalter.mms_product_api.dao.repository.api.pojo;

import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "SAVE_PRODUCT_RECORD_ROW_RELATION")
@Data
@NoArgsConstructor
public class SaveProductRecordRowRelationDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID")
    private Long id;
    @Column(name = "RECORD_ID")
    private Long recordId;
	@Column(name = "RECORD_ROW_ID")
    private Long recordRowId;
    @Column(name = "DEPEND_RECORD_ROW_ID")
    private Long dependRecordRowId;
	@Column(name = "STORE_SKU_ID")
	private String storeSkuId;
	@Column(name = "DEPEND_TYPE")
	private String dependType;
	@Column(name = "CREATED_BY")
	private String createdBy;
	@Column(name = "CREATED_DATE")
	@CreationTimestamp
	private Date createdDate;

	public static final String DEPEND_TYPE_FIELD = "FIELD";
	public static final String DEPEND_TYPE_PRIMARY = "PRIMARY";
	public static final String DEPEND_TYPE_CREATE = "CREATE";

	public SaveProductRecordRowRelationDo(SaveProductRecordDo record, SingleEditProductDto singleEditProductDto, Long dependRecordRowId, String dependType) {
		this.recordId = record.getId();
		this.recordRowId = singleEditProductDto.getProduct().getRecordRowId();
		this.storeSkuId = singleEditProductDto.getProduct().getAdditional().getHktv().getStoreSkuId();
		this.dependRecordRowId = dependRecordRowId;
		this.dependType = dependType;
		this.createdBy = record.getUploadUserId().toString();
	}
}
