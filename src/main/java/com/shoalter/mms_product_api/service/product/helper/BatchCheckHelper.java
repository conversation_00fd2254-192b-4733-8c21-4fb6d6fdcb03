package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.base.pojo.BatchRequestDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.*;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class BatchCheckHelper {

	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;

	private final SaveProductRecordRowHelper saveProductRecordRowHelper;

	private final Gson gson;
	private final MessageSource messageSource;
	public static final int MAXIMUM_20000 = 20000;
	public static final int MAXIMUM_10000 = 10000;
	public static final int MAXIMUM_3000 = 3000;
	public static final int MAXIMUM_2000 = 2000;
	public static final int OAPI_BATCH_EDIT_MAXIMUM = 100;

	/**
	 * @deprecated This method will be removed in future versions (MS-5279). Use checkCount() instead.
	 * check upload exceed 10000 product data
	 */
	@Deprecated
	public ResponseDto<Long> checkCount(UserDto userDto, int uploadType, BatchRequestDto batchRequestDto, List<?> dataList, int maximum) {
		if (CollectionUtil.isEmpty(dataList)) {
			throw new SystemException("No data.");
		} else if (dataList.size() > maximum) {
			SaveProductRecordDo record = createFailProductRecordDo(userDto, uploadType, batchRequestDto);
			Object firstData = dataList.get(0);
			createFailProductRecordRowDo(record.getId(), getSku(uploadType, firstData),
					messageSource.getMessage("message16", new String[]{String.format("%,d", maximum)}, null), firstData);
			dataList.remove(0);
			List<SaveProductRecordRowDo> saveProductRecordRowDoList = dataList
					.stream()
					.map(data -> createFailProductRecordRowDo(record.getId(), getSku(uploadType, data), data))
					.collect(Collectors.toList());
			saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(saveProductRecordRowDoList);

			return ResponseDto.<Long>builder().data(record.getId()).status(-1).build();
		} else {
			return ResponseDto.<Long>builder().status(1).build();
		}
	}

	/**
	 * MS-5279 save client ip
	 * check upload exceed 10000 product data
	 */
	public ResponseDto<Long> checkCount(UserDto userDto, int uploadType, BatchRequestDto batchRequestDto, List<?> dataList, int maximum, String clientIp) {
		if (CollectionUtil.isEmpty(dataList)) {
			throw new SystemException("No data.");
		} else if (dataList.size() > maximum) {
			SaveProductRecordDo record = createFailProductRecordDo(userDto, uploadType, batchRequestDto, clientIp);
			Object firstData = dataList.get(0);
			createFailProductRecordRowDo(record.getId(), getSku(uploadType, firstData),
				messageSource.getMessage("message16", new String[]{String.format("%,d", maximum)}, null), firstData);
			dataList.remove(0);
			List<SaveProductRecordRowDo> saveProductRecordRowDoList = dataList
				.stream()
				.map(data -> createFailProductRecordRowDo(record.getId(), getSku(uploadType, data), data))
				.collect(Collectors.toList());
			saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(saveProductRecordRowDoList);

			return ResponseDto.<Long>builder().data(record.getId()).status(-1).build();
		} else {
			return ResponseDto.<Long>builder().status(1).build();
		}
	}

	private String getSku(int uploadType, Object data) {
		String sku;
		switch (uploadType) {
			case SaveProductType.BATCH_CREATE_PRODUCT:
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL:
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL:
			case SaveProductType.BATCH_EDIT_PRODUCT:
				sku = ((SingleEditProductDto) data).getProduct().getSkuId();
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_COMMONLY_USED:
				sku = ((EditProductCommonlyUsedInfoDto) data).getSkuCode();
				break;
			case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT_COMMONLY_USED:
				sku = ((EditLittleMallProductCommonlyUsedInfoDto) data).getSkuCode();
				break;
			default:
				sku = "";
				break;
		}
		return sku;
	}

	/**
	 * @deprecated This method will be removed in future versions (MS-5279). Use createFailProductRecordDo() instead.
	 */
	@Deprecated
	private SaveProductRecordDo createFailProductRecordDo(UserDto userDto, int uploadType, BatchRequestDto batchRequestDto) {
		SaveProductRecordDo saveProductRecordDo = new SaveProductRecordDo();
		saveProductRecordDo.setMerchantId(batchRequestDto.getMerchantId());
		saveProductRecordDo.setFileName(batchRequestDto.getFileName());
		saveProductRecordDo.setUploadTime(new Date());
		saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
		saveProductRecordDo.setUploadType(uploadType);
		saveProductRecordDo.setUploadUserId(userDto.getUserId());
		return saveProductRecordRepository.save(saveProductRecordDo);
	}

	/**
	 * MS-5279 save client ip
	 */
	private SaveProductRecordDo createFailProductRecordDo(UserDto userDto, int uploadType, BatchRequestDto batchRequestDto, String clientIp) {
		SaveProductRecordDo saveProductRecordDo = new SaveProductRecordDo();
		saveProductRecordDo.setMerchantId(batchRequestDto.getMerchantId());
		saveProductRecordDo.setFileName(batchRequestDto.getFileName());
		saveProductRecordDo.setUploadTime(new Date());
		saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
		saveProductRecordDo.setUploadType(uploadType);
		saveProductRecordDo.setUploadUserId(userDto.getUserId());
		saveProductRecordDo.setUserIp(clientIp);
		return saveProductRecordRepository.save(saveProductRecordDo);
	}

	private SaveProductRecordRowDo createFailProductRecordRowDo(Long recordId, String sku, Object content) {
		SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
		saveProductRecordRowDo.setRecordId(recordId);
		saveProductRecordRowDo.setSku(sku);
		saveProductRecordRowDo.setStatus(SaveProductStatus.FAIL);
		saveProductRecordRowDo.setContent(gson.toJson(content));
		return saveProductRecordRowDo;
	}

	private void createFailProductRecordRowDo(Long recordId, String sku, String errorMessage, Object content) {
		SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
		saveProductRecordRowDo.setStatus(SaveProductStatus.FAIL);
		saveProductRecordRowDo.setRecordId(recordId);
		saveProductRecordRowDo.setSku(sku);
		saveProductRecordRowDo.setErrorMessage(errorMessage);
		saveProductRecordRowDo.setContent(gson.toJson(content));
		saveProductRecordRowRepository.save(saveProductRecordRowDo);
	}
}
