package com.shoalter.mms_product_api.config.product;

import java.util.List;
import java.util.Set;

/**
 * detail please view spec
 * https://hongkongtv.atlassian.net/wiki/spaces/SB/pages/3703406922/Product+Upload+Type
 */
public final class SaveProductType {
	public static final int SINGLE_CREATE_PRODUCT = 1;
	public static final int SINGLE_EDIT_PRODUCT = 2;
	public static final int BATCH_CREATE_PRODUCT = 3;
	public static final int BATCH_EDIT_PRODUCT = 4;
	public static final int BATCH_EDIT_PRODUCT_COMMONLY_USED = 5;
	public static final int SINGLE_EDIT_PRODUCT_PACKAGING_INFO = 7;
	public static final int SYNC_SAME_PRODUCT_CODE = 9;
	public static final int BATCH_EDIT_LITTLE_MALL_PRODUCT_COMMONLY_USED = 10;
	public static final int BATCH_EDIT_STORE_ONLINE_STATUS = 12;
	public static final int BATCH_EDIT_OVERSEA_DELIVERY = 13;
	public static final int BATCH_EDIT_PRODUCT_PACKAGING_INFO = 14;
	public static final int SINGLE_CREATE_LITTLE_MALL_PRODUCT = 15;
	public static final int SINGLE_EDIT_LITTLE_MALL_PRODUCT = 16;
	public static final int BATCH_CREATE_LITTLE_MALL_PRODUCT = 17;
	public static final int SINGLE_CREATE_BUNDLE = 18;
	public static final int SINGLE_EDIT_BUNDLE = 19;
	public static final int SYNC_OFFLINE_BUNDLE = 20;
	public static final int BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE = 21;
	public static final int BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV = 22;
	public static final int BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL = 23;
	public static final int BATCH_CREATE_PRODUCT_FROM_TMALL = 24;
	public static final int BATCH_EDIT_PRODUCT_FROM_EXCEL = 25;
	public static final int BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION = 26;
	public static final int BATCH_EDIT_PRODUCT_PRICE = 27;
	public static final int BATCH_EDIT_PRODUCT_ONLINE_STATUS = 28;
	public static final int BATCH_EDIT_PRODUCT_VISIBILITY = 29;
	public static final int SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT = 30;
	public static final int BATCH_EDIT_LITTLE_MALL_PRODUCT = 33;
	public static final int BATCH_CRATE_BINDING_EXTENDED_WARRANTY = 31;
	public static final int BATCH_DELETE_BINDING_EXTENDED_WARRANTY = 32;
	public static final int BATCH_EDIT_DEPRECATED_PRODUCT_IMAGES = 34;
	public static final int BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION = 35;
	public static final int COMMISSION_RATE_APPROVE = 36;
	public static final int BATCH_EDIT_HKTV_PRODUCT_TRANSLATE = 37;
	public static final int THE_PLACE_SWITCH_PRIMARY = 38;
	public static final int LITTLE_MALL_FLATTEN_PRODUCTS = 39;
	public static final int BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB = 40;
	public static final int BATCH_EDIT_PRODUCT_READY_DAYS = 41;
	public static final int BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL = 42;

	private SaveProductType() {}

	public static final Set<Integer> CREATE_HKTV_PRODUCT_TYPE_SET = Set.of(SINGLE_CREATE_PRODUCT, BATCH_CREATE_PRODUCT, BATCH_CREATE_PRODUCT_FROM_TMALL, BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL);
	public static final Set<Integer> CHECK_BATCH_VARIANT_CREATE_HKTV_PRODUCT_TYPE_SET = Set.of(BATCH_CREATE_PRODUCT, BATCH_CREATE_PRODUCT_FROM_TMALL, BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL);

	public static final Set<Integer> MIGRATION_PRODUCT_TYPE_SET = Set.of(
		BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE, BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV, BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL,
		BATCH_CREATE_PRODUCT_FROM_TMALL);

	public static final Set<Integer> BATCH_EDIT_PRODUCT_TYPE_SET = Set.of(
		BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION, BATCH_EDIT_PRODUCT_PRICE, BATCH_EDIT_PRODUCT_ONLINE_STATUS, BATCH_EDIT_PRODUCT_VISIBILITY,
		BATCH_EDIT_PRODUCT_FROM_EXCEL, BATCH_EDIT_LITTLE_MALL_PRODUCT, BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION, BATCH_EDIT_HKTV_PRODUCT_TRANSLATE, BATCH_EDIT_PRODUCT_READY_DAYS);

	public static final Set<Integer> PATCH_EDIT_PRODUCT_SET = Set.of(
		BATCH_EDIT_PRODUCT_PACKAGING_INFO,
		BATCH_EDIT_DEPRECATED_PRODUCT_IMAGES,
		SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT,
		BATCH_EDIT_PRODUCT_ONLINE_STATUS,
		BATCH_EDIT_PRODUCT_PRICE,
		BATCH_EDIT_PRODUCT_VISIBILITY);

	public static final Set<Integer> PATCH_EDIT_PRODUCT_CONTENT_SET = Set.of(
		BATCH_EDIT_HKTV_PRODUCT_TRANSLATE
	);

	public static final Set<Integer> CHECK_BUNDLE_OFFLINE_PRODUCT_SET = Set.of(BATCH_EDIT_PRODUCT, SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT,
		SINGLE_EDIT_PRODUCT, BATCH_EDIT_PRODUCT_FROM_EXCEL, BATCH_EDIT_PRODUCT_ONLINE_STATUS);

	public static final List<Integer> ALLOW_SYSTEM_UPLOAD_SET = List.of(SINGLE_EDIT_PRODUCT_PACKAGING_INFO, SINGLE_EDIT_PRODUCT,
		SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT, BATCH_EDIT_STORE_ONLINE_STATUS, BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV, SINGLE_CREATE_PRODUCT, BATCH_EDIT_HKTV_PRODUCT_TRANSLATE, BATCH_EDIT_PRODUCT_ONLINE_STATUS);

	public static final Set<Integer> SYNC_BUNDLE_INVENTORY_TYPE_SET = Set.of(SINGLE_CREATE_BUNDLE, SINGLE_EDIT_BUNDLE, SYNC_OFFLINE_BUNDLE);

	public static final Set<Integer> HKTV_TYPE_SET = Set.of(
		SINGLE_CREATE_PRODUCT,
		SINGLE_EDIT_PRODUCT,
		BATCH_CREATE_PRODUCT,
		BATCH_EDIT_PRODUCT,
		BATCH_EDIT_PRODUCT_COMMONLY_USED,
		SINGLE_EDIT_PRODUCT_PACKAGING_INFO,
		SYNC_SAME_PRODUCT_CODE,
		BATCH_EDIT_STORE_ONLINE_STATUS,
		BATCH_EDIT_OVERSEA_DELIVERY,
		BATCH_EDIT_PRODUCT_PACKAGING_INFO,
		BATCH_CREATE_PRODUCT_FROM_TMALL,
		BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL,
		BATCH_EDIT_PRODUCT_FROM_EXCEL,
		BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION,
		BATCH_EDIT_PRODUCT_PRICE,
		BATCH_EDIT_PRODUCT_ONLINE_STATUS,
		BATCH_EDIT_PRODUCT_VISIBILITY,
		SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT,
		BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION,
		BATCH_EDIT_HKTV_PRODUCT_TRANSLATE,
		BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB,
		BATCH_EDIT_PRODUCT_READY_DAYS
	);

	public static final Set<Integer> SINGLE_EDIT_PRODUCT_TYPE_SET = Set.of(
		SaveProductType.SINGLE_EDIT_PRODUCT,
		SaveProductType.SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT,
		SaveProductType.COMMISSION_RATE_APPROVE);

	public static final Set<Integer> CHECKING_APPROVAL_TYPE_SET = Set.of(BATCH_EDIT_PRODUCT, BATCH_EDIT_PRODUCT_FROM_EXCEL, SINGLE_EDIT_PRODUCT);

	public static final Set<Integer> SINGLE_PRODUCT_TYPE_SET = Set.of(
		SINGLE_CREATE_PRODUCT,
		SINGLE_EDIT_PRODUCT,
		SINGLE_EDIT_PRODUCT_PACKAGING_INFO,
		SINGLE_CREATE_LITTLE_MALL_PRODUCT,
		SINGLE_EDIT_LITTLE_MALL_PRODUCT,
		SINGLE_CREATE_BUNDLE,
		SINGLE_EDIT_BUNDLE,
		SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT,
		COMMISSION_RATE_APPROVE
	);

	public static final Set<Integer> HKTV_PROMOTION_MEMBERSHIP_PRICING_TYPE_SET = Set.of(SINGLE_EDIT_PRODUCT, BATCH_EDIT_PRODUCT,
		BATCH_EDIT_PRODUCT_FROM_EXCEL, BATCH_EDIT_PRODUCT_PRICE, SINGLE_EDIT_BUNDLE);

	public static final Set<Integer> CHECK_LITTLE_MALL_VARIANT_SET = Set.of(BATCH_CREATE_LITTLE_MALL_PRODUCT, BATCH_EDIT_LITTLE_MALL_PRODUCT);

	public static final Set<Integer> LITTLE_MALL_TYPE_SET_FOR_CHECK_RECORD = Set.of(
		BATCH_EDIT_LITTLE_MALL_PRODUCT_COMMONLY_USED,
		SINGLE_CREATE_LITTLE_MALL_PRODUCT,
		SINGLE_EDIT_LITTLE_MALL_PRODUCT,
		BATCH_CREATE_LITTLE_MALL_PRODUCT,
		BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE,
		BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV,
		BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL,
		BATCH_EDIT_LITTLE_MALL_PRODUCT,
		LITTLE_MALL_FLATTEN_PRODUCTS
	);

	public static final Set<Integer> HKTV_MALL_TYPE_SET_FOR_CHECK_RECORD = Set.of(
		SINGLE_CREATE_PRODUCT,
		SINGLE_EDIT_PRODUCT,
		BATCH_CREATE_PRODUCT,
		BATCH_EDIT_PRODUCT,
		BATCH_EDIT_PRODUCT_COMMONLY_USED,
		SINGLE_EDIT_PRODUCT_PACKAGING_INFO,
		SYNC_SAME_PRODUCT_CODE,
		BATCH_EDIT_STORE_ONLINE_STATUS,
		BATCH_EDIT_OVERSEA_DELIVERY,
		BATCH_EDIT_PRODUCT_PACKAGING_INFO,
		SINGLE_CREATE_BUNDLE,
		SINGLE_EDIT_BUNDLE,
		SYNC_OFFLINE_BUNDLE,
		BATCH_CREATE_PRODUCT_FROM_TMALL,
		BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL,
		BATCH_EDIT_PRODUCT_FROM_EXCEL,
		BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION,
		BATCH_EDIT_PRODUCT_PRICE,
		BATCH_EDIT_PRODUCT_ONLINE_STATUS,
		BATCH_EDIT_PRODUCT_VISIBILITY,
		SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT,
		BATCH_CRATE_BINDING_EXTENDED_WARRANTY,
		BATCH_DELETE_BINDING_EXTENDED_WARRANTY,
		BATCH_EDIT_DEPRECATED_PRODUCT_IMAGES,
		BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION,
		COMMISSION_RATE_APPROVE,
		BATCH_EDIT_HKTV_PRODUCT_TRANSLATE,
		THE_PLACE_SWITCH_PRIMARY,
		BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB,
		BATCH_EDIT_PRODUCT_READY_DAYS
	);

	public static final Set<Integer> HKTV_RMB_SKU_WHITELIST_CHECK_TYPE_SET = Set.of(
		SINGLE_EDIT_PRODUCT,
		BATCH_EDIT_PRODUCT,
		BATCH_EDIT_PRODUCT_FROM_EXCEL,
		BATCH_EDIT_PRODUCT_PRICE,
		BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB,
		SINGLE_EDIT_BUNDLE
	);

}
