package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.shoalter.mms_product_api.config.security.xss.XssStringDeserializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BatchEditPackingDimensionDto extends BatchEditProductBaseDto {
	@JsonProperty("packing_spec_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String packingSpecEn;
	@JsonProperty("packing_spec_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String packingSpecCh;
	@JsonProperty("packing_spec_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String packingSpecSc;
	private BigDecimal weight;
	@JsonProperty("weight_unit")
	private String weightUnit;
	@JsonProperty("packing_height")
	private BigDecimal packingHeight;
	@JsonProperty("packing_length")
	private BigDecimal packingLength;
	@JsonProperty("packing_depth")
	private BigDecimal packingDepth;
	@JsonProperty("packing_dimension_unit")
	private String packingDimensionUnit;
	@JsonProperty("packing_box_type")
	private String packingBoxType;
	@JsonProperty("carton_height")
	private Integer cartonHeight;
	@JsonProperty("carton_length")
	private Integer cartonLength;
	@JsonProperty("carton_depth")
	private Integer cartonDepth;
}
