package com.shoalter.mms_product_api.service.hybris.pojo;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleInventoryInfoRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
public class HybrisBundleCreateDto {
	private List<HybrisBundleSetDto> bundleSetList;

	public static HybrisBundleCreateDto generateHybrisBundleCreateDto(SingleEditProductDto singleEditProductDto) {
		String parentStoreSkuId = singleEditProductDto.getProduct().getAdditional().getHktv().getStoreSkuId();
		List<HybrisBundleChildSkuDto> childSkus = singleEditProductDto.getProduct().getBundleSetting().getChildSkuInfo().stream()
			.map(HybrisBundleChildSkuDto::generateHybrisBundleChildSkuDto)
			.collect(Collectors.toList());

		Integer ceilingQty = singleEditProductDto.getProduct().getBundleSetting()
			.getMallInventoryInfo().stream()
			.filter(inventory -> BuCodeEnum.HKTV.name().equalsIgnoreCase(inventory.getMall()))
			.findAny().map(BundleInventoryInfoRequestDto::getCeilingQuantity)
			.orElse(null);

		return HybrisBundleCreateDto.builder()
			.bundleSetList(List.of(HybrisBundleSetDto.builder()
				.parentSkuCode(parentStoreSkuId)
				.ceilingQty(ceilingQty)
				.priority(singleEditProductDto.getProduct().getBundleSetting().getPriority())
				.isActive(singleEditProductDto.getProduct().getBundleSetting().getIsActive())
				.childSkuList(childSkus)
				.build()))
			.build();
	}
}
