# Database
spring.datasource.url=jdbc:mysql://${mms_product_configmap_mms_service_db_url}:${mms_product_configmap_mms_db_port}/${mms_product_configmap_mms_db_schema}?characterEncoding=UTF-8&useSSL=false&rewriteBatchedStatements=true
spring.datasource.username=${mms_product_secret_mms_product_db_username}
spring.datasource.password=${mms_product_secret_mms_product_db_password}
spring.datasource.hikari.maximum-pool-size=${mms_product_configmap_db_hikari_maximum_pool_size}

mybatis.configuration.map-underscore-to-camel-case=true

# JWT
jwt.app.secret.key=${mms_product_secret_jwt_app_secret_key}

# Product Master
product.master.api.url=${mms_product_configmap_product_master_api_url}
product.master.rabbitmq.addresses=${mms_product_configmap_product_master_rabbit_mq_address}
product.master.rabbitmq.port=${mms_product_secret_product_master_rabbit_mq_port}
product.master.rabbitmq.username=${mms_product_secret_product_master_rabbit_mq_username}
product.master.rabbitmq.password=${mms_product_secret_product_master_rabbit_mq_password}
product.master.rabbitmq.product.result.exchange=mms_product_topic
product.master.rabbitmq.product.result.routing.key=mms_product.product-result
product.master.ew.update.binding.endpoint=/v1/api/products/extended_warranty_mapping
product.master.ew.search.binding.endpoint=/v1/api/products/search_extended_warranty

# Inventory
inventory.api.url=${mms_product_configmap_inventory_api_url}
product.inventory.service.name=/api/s2s/product-inventory
product.inventory.batch.history=/api/s2s/product-inventory/batch/history
product.inventory.bundle.update=/api/v2/s2s/product-inventory/bundle
product.inventory.bundle.selling.qty=/api/v2/s2s/product-inventory/findBundleInventoryList
product.inventory.child.sku.qty=/api/v2/s2s/product-inventory/findChildSkuQtyList

# Mms Store Api
mms.store.api.url=${mms_product_configmap_mms_store_api_url}

# Mpps Rabbit MQ
mpps.rabbitmq.sku.info.routing.key=mms.sku_info
mpps.rabbitmq.addresses=${mms_product_configmap_rabbitmq_hktv_address}
mpps.rabbitmq.port=${mms_product_configmap_rabbitmq_hktv_port}
mpps.rabbitmq.username=${mms_product_secret_rabbitmq_hktv_username}
mpps.rabbitmq.password=${mms_product_secret_rabbitmq_hktv_password}
mpps.rabbitmq.queue.name=${mms_product_secret_rabbitmq_hktv_queue_name}
mpps.rabbitmq.single.queue.name=${mms_product_configmap_rabbitmq_hktv_single_queue_name}
mpps.rabbitmq.batch.mq.queue.name=${mms_product_configmap_rabbitmq_hktv_batch_mq_queue_name}

# image
hktv.image.url=${mms_product_configmap_hktv_image_url}
image.upload.url=${mms_product_configmap_image_upload_url}
image.status.url=${mms_product_configmap_image_status_url}
image.delete.url=${mms_product_configmap_image_delete_url}
image.batch.delete.url=${mms_product_configmap_image_batch_delete_url}
image.domain=${mms_product_configmap_image_domain}
image.domain.modify=${mms_product_configmap_image_domain_modify}
upload.file.imagePath=/images

# video
video.upload.url=${mms_product_configmap_video_upload_url}
video.status.url=${mms_product_configmap_video_status_url}
upload.file.videoPath=/video
mms.video.private.key=${mms_product_secret_video_private_key}

# Gateway
gateway.service.key=${mms_product_secret_mms_gw_key}
gateway.service.url=${mms_product_configmap_mms_gw_url}
gateway.service.name=/s2s/v1/mms/syncToStorefront
gateway.service.method=POST

create.promotion.service.name=/createDiscountRule
create.promotion.service.method=POST

create.product.service.name=/createProduct
create.product.service.method=PUT

update.product.service.name=/updateProduct
update.product.service.method=PUT

# Front
# Little Mall
little-mall.base-url=${mms_product_configmap_little_mall_base_url}

# 3PL
third-party.api.url=${mms_product_secret_third_party_api_url}
third-party.api.sku.validate.url=/internal/sku/validate

# mms-third-party-sku
mms-third-party-sku.api.url=${mms_product_configmap_mms_third_party_sku_url}
mms-third-party-sku.one-bound.detail.endpoint=/third-party-sku/api/s2s/one-bound/{storeId}/{productId}/detail

# Feature Toggle
feature.toggle.send.mpps.queue=${mms_product_configmap_feature_toggle_send_mpps_queue}

# Hybris
hybris.api.url=${mms_product_configmap_hybris_url}
hybris.create.bundle.endpoint=/v1/hktv/s2s/mms/import_bundle_sets
hybris.update.bundle.endpoint=/v1/hktv/s2s/mms/update_bundle_sets
hybris.update.ew.binding.endpoint=/v1/hktv/s2s/mms/set_dynamicProduct_relation
hybris.update.product.mainland.same.price.endpoint=/v1/hktv/s2s/mms/updateProductMainlandSamePrice
hybris.upsert.everuts.buyer.endpoint=/v1/hktv/s2s/mms/createUpdateEverutsBuyer
hybris.create.update.option.value.endpoint=/v1/hktv/s2s/mms/createUpdateOptionValue
hybris.rabbitmq.product.update.routing.key=mms_product.update_product_from_mms_product_to_hktvmall_hybris
hybris.rabbitmq.product.update.result.queue.name=${mms_product_configmap_rabbitmq_hybris_product_update_result_queue_name}
hybris.api.secret.key=${mms_product_secret_hybris_api_secret_key}
hybris.api.batch.size=${mms_product_configmap_hybris_api_batch_size}
hybris.api.update.mainland.same.price.size=${mms_product_configmap_hybris_api_update_mainland_same_price_size}

# grafana endpoint
management.endpoint.prometheus.enabled=true
management.endpoints.web.exposure.include=prometheus,health

# HttpClient config
httpclient.pool.connection.default.max.route=${mms_product_configmap_httpclient_pool_connection_default_max_route}

# hktvmall external affiliate url
hktvmall.external.base.url=${mms_product_configmap_hktvmall_external_url}
hktvmall.external.affiliate.endpoint=/external_affiliate?targetURL=

# Promotion
promotion.api.url=${mms_product_configmap_promotion_api_url}
promotion.membership.pricing.unexpiredEvents.check=${promotion.api.url}/membershipPricing/internal/unexpiredEvents/check

# product process size limit
product.process.size.limit=${mms_product_configmap_product_process_size_limit}

# Notification
notification.url=${mms_product_configmap_notification_url}

# Approval deal daily report
approval.daily.report.receiver=${mms_product_configmap_approval_daily_report_receiver}

# Product price alert
product.price.alert.internal.user.receiver=${mms_product_configmap_product_price_alert_internal_user_receiver}
product.price.alert.onebound.threads=${mms_product_configmap_product_price_alert_onebound_threads}

# Mms Setting api
mms.setting.api.url=${mms_product_configmap_mms_setting_api_url}
mms.setting.exchange-rate.endpoint=/api/s2s/exchange-rate/

# Cron job
mms.save-product-record.expired.days=${mms_product_configmap_mms_save_product_record_expired_days}
mms.housekeeping.limit.counts=${mms_product_configmap_mms_housekeeping_limit_counts}

# MMS Product
mms.db.price.alert.data.size=${mms_product_configmap_mms_db_price_alert_data_size}


xss.protection.enabled:${mms_product_configmap_xss_protection_enabled:true}
