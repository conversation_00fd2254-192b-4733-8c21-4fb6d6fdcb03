package com.shoalter.mms_product_api.service.notification.pojo;

import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserNameAndEmailViewDo;
import com.shoalter.mms_product_api.service.notification.enums.NotificationCodeEnum;
import com.shoalter.mms_product_api.service.notification.pojo.dto.ButtonDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.ContentDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.MailHeadersDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.MobilePushDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.TemplateDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NotificationRequest {
	private List<Integer> recipientIds;
	private Integer senderId;
	private String serviceCode;
	private String typeCode;
	private String subtypeCode;
	private Boolean sendMessageCenter;
	private ButtonDto button;
	private TemplateDto template;
	private List<ContentDto> contents;
	private MobilePushDto mobilePush;
	private List<MailHeadersDto> mailHeaders;

	public static NotificationRequest generate(
		NotificationCodeEnum notificationCode,
		ButtonDto buttonDto,
		TemplateDto templateDto,
		List<ContentDto> contents,
		List<UserNameAndEmailViewDo> receiverUsers,
		String redirectUrl
	) {
		List<Integer> recipientIds = new ArrayList<>();
		List<MailHeadersDto> mailHeadersList = new ArrayList<>();
		for (UserNameAndEmailViewDo receiverUser : receiverUsers) {
			if (StringUtils.isEmpty(receiverUser.getEmail())) {
				continue;
			}
			MailHeadersDto mailHeadersDto = new MailHeadersDto();
			mailHeadersDto.setFromAddress(ConstantType.NOTIFICATION_MAIL_FROM_ADDRESS);
			mailHeadersDto.setFromName(ConstantType.NOTIFICATION_MAIL_FROM_NAME);
			mailHeadersDto.setToName(receiverUser.getUserName());
			mailHeadersDto.setToAddress(receiverUser.getEmail());
			mailHeadersDto.setLanguage(ConstantType.LANGUAGE_EN);
			mailHeadersList.add(mailHeadersDto);

			recipientIds.add(receiverUser.getUserId());
		}

		// button url
		buttonDto.setUrl(redirectUrl);

		return NotificationRequest.builder()
			.recipientIds(recipientIds)
			.senderId(null)
			.serviceCode(notificationCode.getServiceCode())
			.typeCode(notificationCode.getTypeCode())
			.subtypeCode(notificationCode.getSubtypeCode())
			.sendMessageCenter(true)
			.button(buttonDto)
			.template(templateDto)
			.contents(contents)
			.mobilePush(MobilePushDto.builder()
				.appId(ConstantType.APP_ID)
				.clickthroughUrl(redirectUrl)
				.build())
			.mailHeaders(CollectionUtil.isEmpty(mailHeadersList) ? null : mailHeadersList)
			.build();
	}
}
