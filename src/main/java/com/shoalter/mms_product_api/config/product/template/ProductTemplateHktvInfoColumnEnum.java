package com.shoalter.mms_product_api.config.product.template;

import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import lombok.Getter;

@Getter
public enum ProductTemplateHktvInfoColumnEnum {
	SKU_ID(0, "SKU ID", null, null, null),
	CONTRACT_NO(1, "Contract no.", ExcelValidationName.VALIDATION_CONTRACT_NO, null, null),
	STORES(2, "Store Id", ExcelValidationName.VALIDATION_STORE_ID, null, null),
	PRODUCT_READY_METHOD(3, "Product Ready Method", ExcelValidationName.VALIDATION_PRO_READY_METHOD, null, null),
	DELIVERY_METHOD(4, "Delivery Method", ExcelValidationName.VALIDATION_DELIVERY_METHOD, null, null),
	PRODUCT_TYPE_CODE(5, "Product Type Code", null, null, null),
	PRIMARY_CATEGORY_CODE(6, "Primary Category Code", null, null, null),
	IS_PRIMARY_SKU(7, "Is Primary SKU", ExcelValidationName.VALIDATION_YES_NO, null, null),
	VISIBILITY(8, "Visibility", ExcelValidationName.VALIDATION_YES_NO, null, null),
	ONLINE_STATUS(9, "Online Status", ExcelValidationName.VALIDATION_PRODUCT_STATUS, null, null),

	SKU_SHORT_DESCRIPTION_ENG(10, "SKU Short Description (Eng)", null, null, null),
	SKU_SHORT_DESCRIPTION_CHI(11, "SKU Short Description (Chi)", null, null, null),
	SKU_LONG_DESCRIPTION_ENG(12, "SKU Long Description (Eng)", null, null, null),
	SKU_LONG_DESCRIPTION_CHI(13, "SKU Long Description (Chi)", null, null, null),
	RM_CODE(14, "RM Code", null, null, null),
	VIRTUAL_STORE(15, "Virtual Store", null, null, null),
	FEATURE_START_DATETIME(16, "Feature Start Date/Time", null, null, null),
	FEATURE_END_DATETIME(17, "Feature End Date/Time", null, null, null),
	VOUCHER_TYPE(18, "Voucher Type", ExcelValidationName.VALIDATION_VOUCHER_TYPE, null, null),
	VOUCHER_DISPLAY_TYPE(19, "Voucher Display Type", ExcelValidationName.VALIDATION_VOUCHER_DISPLAY_TYPE, null, null),
	EXPIRY_TYPE(20, "Expiry Type", ExcelValidationName.VALIDATION_EXPIRY_TYPE, null, null),
	REDEEM_START_DATE(21, "Redeem Start Date", null, null, null),
	VOUCHER_TEMPLATE_TYPE(22, "Voucher Template Type", ExcelValidationName.VALIDATION_VOUCHER_TEMPLATE_TYPE, null, null),
	FIXED_REDEMPTION_DATE(23, "Fixed Redemption Date", null, null, null),
	UPON_PURCHASE_DATE(24, "Upon Purchase Date", null, null, null),
	FINE_PRINT_ENG(25, "Fine Print (Eng)", null, null, null),
	FINE_PRINT_CHI(26, "Fine Print (Chi)", null, null, null),
	TERM_NAME(27, "Term Name", ExcelValidationName.VALIDATION_TERM_NAME, null, null),
	CURRENCY(28, "Currency", ExcelValidationName.VALIDATION_CURRENCY, null, null),
	COST(29, "Cost", null, null, null),
	SELLING_PRICE(30, "Selling Price", null, null, null),
	STYLE(31, "Style", ExcelValidationName.VALIDATION_STYLE, null, null),
	DISCOUNT_TEXT_ENG(32, "Discount Text (Eng)", null, null, null),
	DISCOUNT_TEXT_CHI(33, "Discount Text (Chi)", null, null, null),
	MALL_DOLLAR(34, "Mall dollar", null, null, null),
	VIP_MALL_DOLLAR(35, "VIP Mall dollar", null, null, null),
	USER_MAX(36, "User Max", null, null, null),
	MAIN_PHOTO(37, "Main photo", null, null, null),
	ADVERTISING_PHOTO(38, "Advertising Photo", null, null, null),
	VARIANT_PRODUCT_PHOTO(39, "Variant Product Photo", null, null, null),
	OTHER_PHOTO(40, "Other Photo", null, null, null),
	MAIN_VIDEO(41, "Main Video", null, null, null),
	VIDEO_LINK(42, "Video Link", null, null, null),
	VIDEO_LINK_TEXT_ENG(43, "Video Link Text (Eng)", null, null, null),
	VIDEO_LINK_TEXT_CHI(44, "Video Link Text (Chi)", null, null, null),
	WAREHOUSE(45, "Warehouse", ExcelValidationName.VALIDATION_WAREHOUSE, null, null),
	STORAGE_TYPE(46, "Storage type", null, null, null),
	PRE_SELL_FRUIT(47, "Pre-sell Fruit", null, null, null),
	PHYSICAL_STORE(48, "Physical Store", null, null, null),
	PACKING_SPEC_ENG(49, "Packing Spec (Eng)", null, null, null),
	PACKING_SPEC_CHI(50, "Packing Spec (Chi)", null, null, null),
	INVOICE_REMARKS_ENG(51, "Invoice Remarks (Eng)", null, null, null),
	INVOICE_REMARKS_CHI(52, "Invoice Remarks (Chi)", null, null, null),
	RETURN_DAYS(53, "Return Days", ExcelValidationName.VALIDATION_RETURN_DAYS, null, null),
	PRODUCT_READY_DAYS(54, "Product Ready Days", ExcelValidationName.VALIDATION_PRODUCT_READY_DAYS, null, null),
	PICKUP_DAYS(55, "Pickup Days", ExcelValidationName.VALIDATION_PICKUP_DAYS, null, null),
	PICKUP_TIMESLOT(56, "Pickup Timeslot", ExcelValidationName.VALIDATION_PICKUP_TIMESLOT, null, null),
	URGENT(57, "Urgent", ExcelValidationName.VALIDATION_YES_NO, null, null),
	WARRANTY(58, "Warranty", ExcelValidationName.VALIDATION_YES_NO, null, null),
	NEED_REMOVAL_SERVICES(59, "Need Removal Services", ExcelValidationName.VALIDATION_YES_NO, null, null),
	GOODS_TYPE(60, "Goods Type", ExcelValidationName.VALIDATION_GOODS_TYPE, null, null),
	WARRANTY_PERIOD_UNIT(61, "Warranty Period Unit", ExcelValidationName.VALIDATION_WARRANTY_PERIOD_UNIT, null, null),
	WARRANTY_PERIOD(62, "Warranty Period", null, null, null),
	WARRANTY_SUPPLIER_ENG(63, "Warranty Supplier(Eng)", null, null, null),
	WARRANTY_SUPPLIER_CHI(64, "Warranty Supplier(Chi)", null, null, null),
	SERVICE_CENTRE_ADDRESS_ENG(65, "Service Centre Address(Eng)", null, null, null),
	SERVICE_CENTRE_ADDRESS_CHI(66, "Service Centre Address(Chi)", null, null, null),
	SERVICE_CENTRE_EMAIL(67, "Service Centre Email", null, null, null),
	SERVICE_CENTRE_CONTACT(68, "Service Centre Contact", null, null, null),
	WARRANTY_REMARK_ENG(69, "Warranty Remark (Eng)", null, null, null),
	WARRANTY_REMARK_CHI(70, "Warranty Remark (Chi)", null, null, null),
	PARTNER_PLATFORM(71, "Partner Platform", null, null, null),
	PARTNER_PRODUCT_ID(72, "Partner Product ID", null, null, null),
	PARTNER_SKU_ID(73, "Partner SKU ID", null, null, null);

	private final Integer columnNumber;
	private final String colName;
	private final String validationName;
	private final ProductTemplateHktvInfoColumnEnum parent;
	private final String parentSegment;

	ProductTemplateHktvInfoColumnEnum(Integer colNum, String colName, String validationName, ProductTemplateHktvInfoColumnEnum parent, String parentSegment) {
		this.columnNumber = colNum;
		this.colName = colName;
		this.validationName = validationName;
		this.parent = parent;
		this.parentSegment = parentSegment;
	}
}
