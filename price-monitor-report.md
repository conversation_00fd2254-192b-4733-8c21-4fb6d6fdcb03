# Price Monitoring System Technical Documentation

## Overview
The Price Monitoring System is designed to ensure that HKTVmall product prices remain competitive by automatically comparing them with third-party marketplace prices (currently Tmall). The system consists of three cronjobs that work together to:

1. Fetch current prices from third-party platforms
2. Compare these prices with HKTVmall prices
3. Take products offline if HKTVmall prices exceed third-party prices
4. Verify the offline action results

This document provides a detailed technical explanation of each component in the price monitoring system.

## Cronjob Architecture

### Cronjob 1: Price Monitor Update (`cronjob-price-monitor-update.yaml`)
**Purpose**: Fetches and updates third-party platform prices for monitored products.

**Schedule**:
- Development: Every 30 minutes (at 0 and 30 minutes of each hour)
- Production: Every Sunday at 22:00 (TZ=Asia/Hong_Kong 0 22 * * 0)

**Endpoint**: `/product/cronjob/price-monitor/update-price`

**Implementation**: `ProductPriceMonitorService.updateMonitorProductPrice()`

**Workflow**:
1. Retrieves all enabled price monitor products for the TMALL platform
2. Groups products by `targetProductCode` (the product ID in Tmall)
3. For each group, calls the OneBound API to fetch current prices
4. Updates product records with fetched prices and status:
   - Success: `PRICE_UPDATED`
   - Price not found: `PRICE_NOT_FOUND`
   - Server error: `THIRD_PARTY_SERVER_ERROR`

### Cronjob 2: Price Monitor Check (`cronjob-price-monitor-check.yaml`)
**Purpose**: Compares HKTVmall prices with third-party prices and takes offline action if needed.

**Schedule**:
- Development: Every 10 minutes (at 10, 20, 40, 50 minutes of each hour)
- Production: Every day at 1:00 AM (TZ=Asia/Hong_Kong 0 1 * * *)

**Endpoint**: `/product/cronjob/price-monitor/check-price`

**Implementation**: `ProductPriceMonitorCheckService.monitorProductPrice()`

**Workflow**:
1. Retrieves products with `PRICE_UPDATED` status
2. Groups products by merchant ID
3. Fetches current HKTVmall prices from ProductMaster
4. Compares prices using a configurable threshold percentage
5. For products where HKTVmall price > Tmall price:
   - Creates offline records with status `OFFLINE_RECORD_CREATE`
   - Initiates the offline process via BatchEditHelper
6. For products with matching prices:
   - Updates Hybris with "mainland same price" flag
   - Sets status to `CHECKED`
7. Sends notifications to internal users and merchants
8. Saves final statuses to history table

### Cronjob 3: Price Monitor Offline Check (`cronjob-price-monitor-offline-check.yaml`)
**Purpose**: Verifies the results of offline actions and updates status accordingly.

**Schedule**:
- Development: Every 10 minutes (at 5, 15, 25, 35, 45, 55 minutes of each hour)
- Production: Every 3 hours at minute 15 (TZ=Asia/Hong_Kong 15 */3 * * *)

**Endpoint**: `/product/cronjob/price-monitor/check-offline`

**Implementation**: `ProductPriceMonitorCheckService.offlineChecking()`

**Workflow**:
1. Retrieves records with `OFFLINE_RECORD_CREATE` status
2. Checks the status of corresponding offline records in `SaveProductRecordDo`
3. Updates status based on offline record status:
   - Success: `OFFLINE`
   - Failure: `OFFLINE_FAIL`
4. Saves final statuses to history table

## Data Model

### Main Tables
1. **PRODUCT_PRICE_MONITOR_PRODUCT**
   - Stores information about products being monitored
   - Key fields: `id`, `merchantId`, `skuCode`, `storeSkuId`, `targetProductCode`, `targetSkuCode`, `targetOriginalPrice`, `targetSellingPrice`, `priceStatus`, `activeInd`

2. **PRODUCT_PRICE_MONITOR_PRODUCT_CHECK**
   - Records price comparison results and offline actions
   - Key fields: `id`, `productPriceMonitorProductId`, `status`, `jobTraceUuid`, `recordId`, `sourceSellingPrice`, `targetSellingPrice`

3. **PRODUCT_PRICE_MONITOR_PRODUCT_CHECK_HISTORY**
   - Archives completed check records
   - Contains fields from both tables above for historical reference

4. **SAVE_PRODUCT_RECORD**
   - Tracks the status of offline actions
   - Referenced by `recordId` in the check table

### Status Enums
1. **MonitorProductPriceStatus**
   - `PENDING(0)`: Initial state, waiting for price update
   - `PRICE_UPDATED(1)`: Third-party price successfully updated
   - `PRICE_NOT_FOUND(2)`: Third-party price not found
   - `THIRD_PARTY_SERVER_ERROR(3)`: Error fetching third-party price

2. **MonitorProductCheckStatus**
   - `ERROR(-1)`: Check process error
   - `PENDING(0)`: Initial state, waiting for check
   - `PROCESSING(1)`: Check in progress
   - `OFFLINE(2)`: Product successfully taken offline
   - `OFFLINE_FAIL(3)`: Failed to take product offline
   - `CHECKED(4)`: Price check completed, no action needed
   - `NOT_FOUND(5)`: Price not found during check
   - `OFFLINE_RECORD_CREATE(11)`: Offline record created
   - `OFFLINE_RECORD_CREATE_FAIL(12)`: Failed to create offline record

3. **MonitorSamePriceStatus**
   - `TRUE(1)`: Same price flag set in Hybris
   - `FALSE(0)`: Same price flag not set, needs retry
   - `ERROR(-1)`: Error setting same price flag, needs retry

## Status Flow Diagram
```
1. Price Update Flow:
   PENDING → PRICE_UPDATED / PRICE_NOT_FOUND / THIRD_PARTY_SERVER_ERROR

2. Price Check Flow:
   PENDING → PROCESSING → OFFLINE_RECORD_CREATE → OFFLINE / OFFLINE_FAIL
   PENDING → CHECKED (if prices match)
   PENDING → NOT_FOUND (if price not found)
   PENDING → ERROR (if check fails)

3. Offline Check Flow:
   OFFLINE_RECORD_CREATE → OFFLINE (success)
   OFFLINE_RECORD_CREATE → OFFLINE_FAIL (failure)
```

## Integration Points

1. **OneBound API**
   - Used to fetch Tmall product prices
   - Accessed via `MmsThirdPartySkuHelper.fetchSkuOneBoundDetail()`

2. **ProductMaster**
   - Used to fetch current HKTVmall product prices
   - Accessed via `ProductMasterHelper.requestProductByStoreSkuId()`

3. **Hybris API**
   - Used to set "mainland same price" flag
   - Accessed via `HybrisHelper.requestUpdateProductMainlandSamePrice()`

4. **BatchEditHelper**
   - Used to create offline records for products
   - Creates entries in `SAVE_PRODUCT_RECORD` table

5. **Notification Service**
   - Sends alerts to internal users and merchants
   - Implemented in `ProductPriceAlertNotificationService`

## Configuration

1. **Price Threshold**
   - Configured in system parameters table (`SysParmDo`)
   - Parameter: `PRICE_ALERT_PRICE_DIFF_RATE_CODE_TAMLL`
   - Controls the percentage threshold for price comparison

2. **Cronjob Schedules**
   - Configured in Kubernetes cronjob YAML files
   - Different schedules for dev, staging, and production environments

## Product Monitoring Lifecycle

1. **Adding Products to Monitoring**
   - Products are added when created or edited with `ExternalPlatform` set to TMALL
   - Handled by `ProductPriceMonitorProductHelper.priceMonitorCreateProcess()`

2. **Removing Products from Monitoring**
   - Products are removed when `ExternalPlatform` is changed or removed
   - Handled by `ProductPriceMonitorProductHelper.priceMonitorUpdateProcess()`

3. **Monitoring Criteria**
   - Product must have `ExternalPlatform` set to TMALL
   - Must have valid `productId` and `skuId`
   - Implemented in `ProductPriceMonitorProductHelper.meetMonitorCriteria()`

## Error Handling and Logging

1. **Error States**
   - Each process has specific error states (e.g., `THIRD_PARTY_SERVER_ERROR`, `OFFLINE_FAIL`)
   - Errors are logged with detailed messages

2. **Retry Mechanism**
   - Some operations (like setting same price flag) have built-in retry logic
   - Controlled by status enums like `MonitorSamePriceStatus.NEED_RETRY`

3. **Logging**
   - Comprehensive logging throughout the system
   - Each log entry includes job trace UUID for correlation
   - Format: `[cron-job][price-monitor/...] <message>`

## Performance Considerations

1. **Batch Processing**
   - Products are processed in batches to avoid memory issues
   - Batch sizes:
     - ProductMaster search: 1000 SKUs
     - Hybris update: Configured in `HybrisHelper.UPDATE_PRODUCT_MAINLAND_SAME_PRICE_BATCH_SIZE_LIMIT`

2. **Asynchronous Processing**
   - All cronjob methods are annotated with `@Async`
   - Allows long-running operations without blocking

3. **Database Optimization**
   - Bulk operations used where possible (`saveAll` instead of individual saves)
   - Efficient queries with proper indexing

## Conclusion

The Price Monitoring System provides an automated solution for ensuring HKTVmall's price competitiveness against third-party marketplaces. The three-cronjob architecture separates concerns and allows for efficient processing:

1. **Update Cronjob**: Focuses solely on fetching and updating third-party prices
2. **Check Cronjob**: Handles price comparison and initiates offline actions
3. **Offline Check Cronjob**: Verifies the results of offline actions

This separation ensures that each process can be monitored and troubleshooted independently, while the shared data model maintains consistency across all operations.
