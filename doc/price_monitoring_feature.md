# 價格監控（Price Monitor）功能設計說明

## 一、業務目的

當第三方商城（如天貓）匯入商品後，需建立一套價格監控機制，確保 HKTVmall 上架商品的售價「小於等於」第三方商城價格，以維持 HKTVmall 的價格競爭力。若發現 HKTVmall 價格高於第三方，則自動下架並通知相關人員。

---

## 二、主要排程流程

### 排程A：同步第三方價格（ProductPriceMonitorService.updateMonitorProductPrice）

- 目標：定時透過第三方 API 取得商品價格，並寫入 `ProductPriceMonitorProductDo`。
- 流程：
  1. 查詢所有啟用（ENABLE）、目標平台（如 TMALL）的監控商品。
  2. 依 `targetProductCode` 分組，逐一調用第三方 API 取得每個 SKU 的價格。
  3. 更新監控商品的 `targetOriginalPrice`、`targetSellingPrice`、`targetUrl`、`targetPriceUpdatedDate`。
  4. 狀態流轉：
     - 成功取得價格：`PRICE_UPDATED`
     - 未取得價格：`PRICE_NOT_FOUND`
     - 伺服器錯誤：`THIRD_PARTY_SERVER_ERROR`

### 排程B：價格比對與下架判斷（ProductPriceMonitorCheckService.monitorProductPrice）

- 目標：定時比對 HKTVmall 價格與第三方價格，若高於第三方則下架並通知。
- 流程：
  1. 查詢所有啟用、價格已更新（`PRICE_UPDATED`）的監控商品。
  2. 依商戶分組，批次查詢 HKTVmall 當前價格。
  3. 比較 HKTVmall 價格與第三方價格，若 HKTVmall > 第三方，則進入下架流程。
  4. 為需下架的商品建立 [ProductPriceMonitorProductCheckDo] 記錄，狀態設為 `OFFLINE_RECORD_CREATE`。
  5. 若價格相同，則調用 Hybris API 設定「同價」flag。
  6. 發送通知給內部用戶與商戶。
  7. 狀態流轉：
     - 檢查中：`PENDING` → `PROCESSING`
     - 比對完成：`CHECKED`
     - 找不到價格：`NOT_FOUND`
     - 下架記錄建立：`OFFLINE_RECORD_CREATE`
     - 失敗：`ERROR`、`OFFLINE_RECORD_CREATE_FAIL`

### 排程C：下架結果回寫（ProductPriceMonitorCheckService.offlineChecking）

- 目標：非同步檢查下架任務結果，並將狀態寫回資料表。
- 流程：
  1. 查詢所有狀態為 `OFFLINE_RECORD_CREATE` 的商品監控記錄。
  2. 根據 `recordId` 查詢下架任務執行情況（SaveProductRecordDo）。
  3. 若下架成功，狀態設為 `OFFLINE`；失敗則設為 `OFFLINE_FAIL`，並記錄錯誤原因。
  4. 結束時將最終狀態寫入歷史表（ProductPriceMonitorProductCheckHistoryDo）。
  5. 狀態流轉：
     - 下架成功：`OFFLINE`
     - 下架失敗：`OFFLINE_FAIL`

---

## 三、核心資料結構與狀態

### 1. ProductPriceMonitorProductDo

- 代表每一個需監控價格的商品（SKU）。
- 主要欄位：
  - `id`
  - `merchantId`
  - `skuCode`、`storeSkuId`
  - `targetProductCode`、`targetSkuCode`
  - `targetOriginalPrice`、`targetSellingPrice`
  - `targetPriceUpdatedDate`
  - `priceStatus`（參考 MonitorProductPriceStatus Enum）

#### MonitorProductPriceStatus Enum
- `PENDING(0)`：待同步
- `PRICE_UPDATED(1)`：價格已取得
- `PRICE_NOT_FOUND(2)`：找不到價格
- `THIRD_PARTY_SERVER_ERROR(3)`：第三方伺服器錯誤

### 2. ProductPriceMonitorProductCheckDo

- 代表每一次價格比對與下架判斷的處理記錄。
- 主要欄位：
  - `id`
  - `productPriceMonitorProductId`（關聯 ProductPriceMonitorProductDo）
  - `status`（參考 MonitorProductCheckStatus Enum）
  - `jobTraceUuid`（批次識別）
  - `recordId`（下架任務記錄）
  - `sourceSellingPrice`、`targetSellingPrice`
  - `errorReason`、`errorCode`
  - `createdDate`、`lastUpdatedDate`

#### MonitorProductCheckStatus Enum
- `ERROR(-1)`：檢查錯誤
- `PENDING(0)`：待檢查
- `PROCESSING(1)`：處理中
- `OFFLINE(2)`：已下架
- `OFFLINE_FAIL(3)`：下架失敗
- `CHECKED(4)`：比對完成
- `NOT_FOUND(5)`：找不到價格
- `OFFLINE_RECORD_CREATE(11)`：下架記錄已建立
- `OFFLINE_RECORD_CREATE_FAIL(12)`：下架記錄建立失敗

### 3. MonitorSamePriceStatus Enum（Hybris「同價」flag 狀態）

- `TRUE(1)`：已於 Hybris 設定同價（isMainlandSamePrice = true）
- `FALSE(0)`：尚未設定同價（需重試）
- `ERROR(-1)`：設定失敗（需重試）

其中 `FALSE` 與 `ERROR` 狀態會被自動納入重試機制（見 `MonitorSamePriceStatus.NEED_RETRY`）。該狀態用於追蹤 Hybris API 設定「同價」flag 的結果，並決定是否需要再次嘗試。

MS-7514: 一旦isSamePrice被設為`TRUE(1)`後則不會被改為`FALSE(0)`

---

## 四、SQL 與資料表關聯

- 主要表：
  - `PRODUCT_PRICE_MONITOR_PRODUCT`（商品監控主表）
  - `PRODUCT_PRICE_MONITOR_PRODUCT_CHECK`（比對/下架記錄表）
  - `PRODUCT_PRICE_MONITOR_PRODUCT_CHECK_HISTORY`（歷史表）
  - `SAVE_PRODUCT_RECORD`（下架任務執行狀態）

- 典型查詢：
  - 依據 `activeInd`、`targetPlatform`、`busUnitId` 查詢需監控商品
  - 依據 `status` 查詢需下架或需檢查的記錄
  - 依據 `recordId` 查詢下架任務執行情況

---

## 五、MonitorProductCheckStatus 狀態機（依 CRON JOB 分區分組）

```mermaid
flowchart TD
    subgraph PriceMonitorUpdateJob
        Z[定期查詢第三方價格] --> A[PENDING]
    end

    subgraph PriceMonitoringCheckJob
        A -->|超過門檻| B(PROCESSING)
        A -->|符合規則| C(CHECKED)
        A -->|找不到價格| D(NOT_FOUND)
        A -->|檢查錯誤| E(ERROR)
        B -->|建立下架記錄| F(OFFLINE_RECORD_CREATE)
        B -->|建立失敗| J(OFFLINE_RECORD_CREATE_FAIL)
    end

    subgraph OfflineRecordResultJob
        F -->|定期檢查| L([判斷RECORD狀態])
        L -->|下架成功| G(OFFLINE)
        L -->|下架失敗| H(OFFLINE_FAIL)
    end

    C -->|寫HISTORY| K([結束])
    D -->|寫HISTORY| K
    G -->|寫HISTORY| K
    H -->|寫HISTORY| K
    J -->|寫HISTORY| K
    E --> K
```

**說明：**
- **PriceMonitorUpdateJob**：負責定期向第三方平台查詢商品價格，並產生/刷新待檢查記錄（PENDING）
- **PriceMonitoringCheckJob**：負責價格狀態檢查、處理下架記錄建立與失敗
- **OfflineRecordResultJob**：負責定期檢查下架任務執行狀態，並判斷最終結果

---

## 六、參考檔案與類別

- Controller: `CronjobController`（/cronjob/price-monitor/*）
- 服務層：`ProductPriceMonitorService`、`ProductPriceMonitorCheckService`
- 核心資料物件：`ProductPriceMonitorProductDo`、`ProductPriceMonitorProductCheckDo`
- 狀態 Enum：`MonitorProductPriceStatus`、`MonitorProductCheckStatus`
- 下架任務狀態：`SaveProductStatus`（SUCCESS/FAIL）

---

## 七、商品價格監控清單管理邏輯說明（ProductPriceMonitorProductHelper）

### 一、功能目的
此功能的主要目的是在商品建立或編輯時，根據商品的 `ExternalPlatform` 屬性，判斷該商品是否需要加入或移除價格監控清單。
- **加入監控**：當商品來自特定第三方平台（如 TMALL）且資料齊全時，將其加入監控清單。
- **移除監控**：當商品不再符合監控條件時（如編輯後移除 ExternalPlatform），需將其從監控清單中移除（即停用監控）。

### 二、核心業務流程
1. **建立流程（Create）**
   - 由 `priceMonitorCreateProcess` 負責。
   - 依據商品的 `ExternalPlatform`、來源平台（如 TMALL）、store 資訊等條件，判斷是否需監控。
   - 若需監控，則呼叫 `enableMonitor`，將商品加入監控清單（資料表 `PRODUCT_PRICE_MONITOR_PRODUCT`）。
   - 若不需監控或資料不齊全，則略過。
2. **編輯流程（Update）**
   - 由 `priceMonitorUpdateProcess` 負責。
   - 需同時考慮「編輯前」與「編輯後」商品的 `ExternalPlatform` 狀態。
   - 透過 `determinePriceMonitorAction` 方法，根據新舊狀態判斷：
     - 新增監控 → 執行 `enableMonitor`
     - 移除監控 → 執行 `disableMonitor`
     - 無需變動 → 不動作
3. **監控條件判斷**
   - 由 `meetMonitorCriteria` 方法判斷：
     - `ExternalPlatform` 不為 null
     - 來源平台需包含指定值（如 TMALL）
     - 需有完整的 productId 與 skuId

### 三、狀態管理與流轉
1. **監控商品主表（ProductPriceMonitorProductDo）**
   - `activeInd`：`ENABLE`(1) 啟用監控、`DISABLE`(0) 停用監控
   - `priceStatus`（MonitorProductPriceStatus Enum）：`PENDING`(0)、`PRICE_UPDATED`(1)、`PRICE_NOT_FOUND`(2)、`THIRD_PARTY_SERVER_ERROR`(3)
2. **狀態轉換重點**
   - 啟用監控時：若資料庫已有紀錄但為停用，則重新啟用並將 `priceStatus` 設為 `PENDING`。若無紀錄則新增。
   - 停用監控時：將 `activeInd` 設為 `DISABLE`，`priceStatus` 重設為 `PENDING`。

### 四、資料表與 SQL 關聯
- 主要操作表：`PRODUCT_PRICE_MONITOR_PRODUCT`
- 常用查詢：依據 `storeSkuId`、`busUnitId`、`targetPlatform` 查詢監控商品，啟用/停用時更新 `activeInd` 與 `priceStatus`

### 五、關鍵方法摘要
- `priceMonitorCreateProcess(...)`：建立時判斷是否加入監控
- `priceMonitorUpdateProcess(...)`：編輯時根據新舊狀態決定是否啟用/停用監控
- `determinePriceMonitorAction(newPlatform, originPlatform)`：判斷監控動作
- `meetMonitorCriteria(ext, sourceEnum)`：判斷是否符合監控條件
- `createOrUpdateActiveMonitorRecord(...)`：建立或重啟監控紀錄
- `reActiveMonitorRecord(...)`：將監控紀錄設為啟用
- `disableMonitorRecord(...)`：將監控紀錄設為停用

### 六、業務邏輯補充
- 每次啟用/停用監控都會重設 `priceStatus` 為 `PENDING`，確保後續排程能正確處理。
- 只針對 TMALL 等指定平台進行監控，避免誤將不需監控的商品納入清單。
- 日誌完整，所有異常與關鍵動作皆有 log 記錄。

### 七、維護建議
- 當新增其他第三方平台時，需同步擴充 `meetMonitorCriteria` 的判斷邏輯。
- 若有監控條件變更，務必檢查所有 `enable/disableMonitor` 相關方法。
- 狀態 enum 若有調整，需同步更新資料表與相關排程邏輯。

> 相關 ticket：MS-7514
> 主要類別：`ProductPriceMonitorProductHelper`, `ProductPriceMonitorProductDo`, `MonitorProductPriceStatus`, `ActiveInd`

---

## 八、監控啟用/移除流程圖

```mermaid
flowchart TD
    A[商品建立或編輯] --> B{ExternalPlatform 條件判斷}
    B -- 符合監控條件 --> C[加入監控清單]
    B -- 不符合監控條件 --> D[移除監控清單]
    C --> E[activeInd=ENABLE\npriceStatus=PENDING]
    D --> F[activeInd=DISABLE\npriceStatus=PENDING]
```

> 說明：
> - "符合監控條件" 指 ExternalPlatform 不為 null、來源平台為 TMALL 且 productId/skuId 不為空。
> - "加入監控清單" 會將資料設為 activeInd=ENABLE、priceStatus=PENDING。
> - "移除監控清單" 則設為 activeInd=DISABLE、priceStatus=PENDING。


順序
QA 測試介紹:

資料庫
價格檢查商品主表: `PRODUCT_PRICE_MONITOR_PRODUCT`
價格檢查記錄表: `PRODUCT_PRICE_MONITOR_PRODUCT_CHECK`
價格檢查歷史表: `PRODUCT_PRICE_MONITOR_PRODUCT_CHECK_HISTORY`

排程A 更新第三方平台商品價格排程: `mms-product-cronjob-price-monitor-update`
- dev 執行時間: (0,30 * * * *) 0分和30分執行, 但是只有奇數小時才會執行(模擬雙周執行一次)
排程B 價格檢查排程: `mms-product-cronjob-price-monitor-check`
- dev 執行時間: (10,20,40,50 * * * *) 每小時內的 10,20,40,50 分執行
排程C 下架結果排程: `mms-product-cronjob-price-monitor-offline-check`
- dev 執行時間: (5,15,25,35,45,55 * * * *) 每小時內的 5,15,25,35,45,55 分執行

以上排程皆可手動觸發, 請聯繫BE PIC如果需要直接觸發以上排程.

HAPPY FLOW:

1. 建立主表商品紀錄
- 當商品建立或編輯時，有新增ExternalPlatform, 並且sourePlatform為TMALL, 則會將商品加入主表(PRODUCT_PRICE_MONITOR_PRODUCT)。

2. 排程A 更新價格(TMALL價格)
- dev 每小時的 0分和30分執行，但是只有奇數小時才會執行(模擬雙周執行一次)
- 會透過 third-party API 取得 TMALL 的價格，請注意這個是call one-bound product api。如果要測試這段測試資料必須是現實TMALL存在資料。要略過這段可以直接修改主表資料，模擬已經取得價格的結果。
- 價格獲取成功, 會更新主表上以下欄位
  - `targetOriginalPrice`
  - `targetSellingPrice`
  - `targetUrl`
  - `targetPriceUpdatedDate`
  - `priceStatus` (priceStatus = PRICE_UPDATED)

3. 排程B 價格檢查
- dev 每小時的 10,20,40,50 分執行
- 會檢查主表商品的價格是否符合門檻, 比較HKTV與TMALL的價格
- 檢查紀錄會記錄在紀錄表 `PRODUCT_PRICE_MONITOR_PRODUCT_CHECK`
- 根據主表上價格紀錄已經 update 的商品, 會去查詢 HKTV 的當下價格, 根據比對結果處理(流程與price-alert相同) 進行下架商品, 通知商戶/user.
- 新增部分: 如果HKTV價格小於等於TMALL價格, 則會透過call hybris API 新增 samePrice flag. (這個部分不確定要如何驗證)

4. 排程C 確認下架結果
- dev 每小時的 5,15,25,35,45,55 分執行
- 會透過紀錄表 `PRODUCT_PRICE_MONITOR_PRODUCT_CHECK` 中的紀錄確認商品是否已經下架 

