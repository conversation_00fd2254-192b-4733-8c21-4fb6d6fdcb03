package com.shoalter.mms_product_api.service.force_offline;

import com.shoalter.mms_product_api.config.product.ForceOfflineStatusEnum;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.BatchEditHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * Service for handling force offline functionality
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ForceOfflineService {

	private final BatchEditHelper batchEditHelper;

    /**
     * Process a single SKU for force offline or remove force offline
     */
    public ResponseDto<ForceOfflineResponseDto> processSingleForceOffline(UserDto userDto, ForceOfflineRequestDto requestDto) {
        log.info("Processing single force offline request: {}", requestDto);

        try {
            // 1. Validate request
            validateRequest(requestDto);

            // 2. Process based on skuStatus
			createForceOfflineRecord(List.of(requestDto));

			// 3. call product master

            // 3. Create response
            ForceOfflineResponseDto responseDto = new ForceOfflineResponseDto();
            responseDto.setRecordId(1L); // Replace with actual record ID

			// 4. return record ID

            return ResponseDto.success(responseDto);
        } catch (Exception e) {
            log.error("Error processing single force offline request", e);
            return ResponseDto.fail(Collections.emptyList());
        }
    }

    /**
     * Process multiple SKUs for force offline or remove force offline
     */
    public ResponseDto<Void> processBatchForceOffline(UserDto userDto, List<ForceOfflineRequestDto> requestDtoList) {
        log.info("Processing batch force offline request with {} items", requestDtoList.size());

        try {
            // Process each request
            for (ForceOfflineRequestDto requestDto : requestDtoList) {
                // 1. Validate request
                validateRequest(requestDto);
            }

			// 2. Process based on skuStatus
			createForceOfflineRecord(requestDtoList);

            return ResponseDto.success(null);
        } catch (Exception e) {
            log.error("Error processing batch force offline request", e);
            return ResponseDto.fail(Collections.emptyList());
        }
    }

    /**
     * Validate request data
     */
    private void validateRequest(ForceOfflineRequestDto requestDto) {
        // Check required fields
        if (requestDto.getStorefrontStoreCode() == null || requestDto.getStorefrontStoreCode().isEmpty()) {
            throw new IllegalArgumentException("storefrontStoreCode is required");
        }

        if (requestDto.getSkuCode() == null || requestDto.getSkuCode().isEmpty()) {
            throw new IllegalArgumentException("skuCode is required");
        }

        if (requestDto.getSkuStatus() == null || requestDto.getSkuStatus().isEmpty()) {
            throw new IllegalArgumentException("skuStatus is required");
        }

		// Validate skuStatus
		ForceOfflineStatusEnum statusEnum = ForceOfflineStatusEnum.getEnum(requestDto.getSkuStatus());
		if (statusEnum == null) {
			throw new IllegalArgumentException("Invalid skuStatus: " + requestDto.getSkuStatus());
		}
    }

    /**
     * Set a SKU to force offline
     */
    private void createForceOfflineRecord(List<ForceOfflineRequestDto> requestDtoList) {
        // 1. Fetch current sku data from product master(full sku current data/status)

		// 2. Set force offline fields into current sku data
		// - force_offline = true
		// - case_number = requestDto.getCaseNumber()

        // 3. save update product record to local DB(mms)
		// UPDATE TYPE: SINGLE_EDIT_PRODUCT_FORCE_OFFLINE(43)
		// UPDATE TYPE: BATCH_EDIT_PRODUCT_FORCE_OFFLINE(44)

		// 4. USE BatchEditHelper to create a batch edit record


    }

    /**
     * Remove force offline from a SKU
     *
     * @param requestDto force offline request data
     */
    private void removeForceOffline(ForceOfflineRequestDto requestDto) {
        // 1. Update local DB
        // Example:
        // productRepository.updateForceOfflineStatus(
        //    requestDto.getStorefrontStoreCode(),
        //    requestDto.getSkuCode(),
        //    null,
        //    null,
        //    "OFFLINE"
        // );

        // 2. Send to product master
        // Example:
        // productMasterService.sendForceOfflineUpdate(
        //    requestDto.getStorefrontStoreCode(),
        //    requestDto.getSkuCode(),
        //    null,
        //    null,
        //    "OFFLINE"
        // );

        log.info("Remove force offline for SKU: {}", requestDto.getSkuCode());
    }
}
