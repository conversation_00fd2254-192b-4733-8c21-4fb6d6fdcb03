package com.shoalter.mms_product_api.service.hybris.helper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.HybrisMqHandlingEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.edit_column.SaveProductRecordRowTempStatusEnum;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowTempRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowTempDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductStoreStatusRepository;
import com.shoalter.mms_product_api.dao.repository.product.TempProductSyncHybrisRecordRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.helper.RabbitTemplateHelper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.hybris.enums.HybrisMqFromSystemEnum;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisProductResultMqMessageDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateProductMainMqMessage;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateReadyPickupDaysActionDto;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.helper.UserHelper;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterMqDto;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

@Slf4j
@ExtendWith(MockitoExtension.class)
class HybrisMqFlowHelperTest {

	@InjectMocks
	private HybrisMqFlowHelper hybrisMqFlowHelper;

	@Mock
	private StoreRepository storeRepository;
	@Mock
	private RabbitTemplateHelper rabbitTemplateHelper;
	@Mock
	private SaveProductHelper saveProductHelper;
	@Mock
	private ExchangeRateHelper exchangeRateHelper;
	@Mock
	private UserHelper userHelper;
	@Mock
	private SaveProductRecordRepository saveProductRecordRepository;
	@Mock
	private SaveProductRecordRowTempRepository saveProductRecordRowTempRepository;
	@Mock
	private SaveProductRecordRowHelper saveProductRecordRowHelper;
	@Mock
	private ProductRepository productRepository;
	@Mock
	private TempProductSyncHybrisRecordRepository syncHybrisRecordRepository;
	@Mock
	private MessageSource messageSource;
	@Mock
	private ProductStoreStatusRepository productStoreStatusRepository;

	private final Gson gson = new Gson();

	private static final String TEST_STRING = "test";
	private static final long TEST_RECORD_ROW_ID = 1L;

	@BeforeEach()
	void setup() {
		hybrisMqFlowHelper = new HybrisMqFlowHelper(
			saveProductHelper,
			rabbitTemplateHelper,
			userHelper,
			productRepository,
			storeRepository,
			saveProductRecordRepository,
			saveProductRecordRowTempRepository,
			syncHybrisRecordRepository,
			saveProductRecordRowHelper,
			exchangeRateHelper,
			messageSource,
			gson,
			productStoreStatusRepository
		);
	}

	@Test
	void generateDateFromProductMasterUpdateProductRequest_generateDateFrom_ResponseSuccess() {
		//Given
		StoreDo storeDo = new StoreDo();
		storeDo.setStoreCode(TEST_STRING);

		SaveProductRecordRowTempDo saveProductRecordRowTempDo = new SaveProductRecordRowTempDo();
		saveProductRecordRowTempDo.setRecordRowId(TEST_RECORD_ROW_ID);
		saveProductRecordRowTempDo.setTraceId(TEST_STRING);


		when(storeRepository.findByStoreCodeIn(any())).thenReturn(List.of(storeDo));
		when(saveProductRecordRowTempRepository.findByRecordRowId(any())).thenReturn(Optional.of(saveProductRecordRowTempDo));

		//When
		ProductMasterMqDto productMasterMqDto = new ProductMasterMqDto();
		productMasterMqDto.setStores(TEST_STRING);
		productMasterMqDto.setRecordRowId(TEST_RECORD_ROW_ID);
		ResponseDto<Map<SaveProductRecordRowTempDo, HybrisUpdateProductMainMqMessage>> result = hybrisMqFlowHelper.
			generateDateFromProductMasterUpdateProductRequest(HybrisMqHandlingEnum.OPEN_API_BATCH_EDIT_PRICE, TEST_STRING, List.of(productMasterMqDto));

		//Then
		Assertions.assertThat(result.getStatus()).isEqualTo(StatusCodeEnum.SUCCESS.getCode());
		Assertions.assertThat(result.getData().size()).isEqualTo(1);
		Map.Entry<SaveProductRecordRowTempDo, HybrisUpdateProductMainMqMessage> resultMap = result.getData().entrySet().iterator().next();
		Assertions.assertThat(resultMap.getKey().getTraceId()).isEqualTo(TEST_STRING);
		Assertions.assertThat(resultMap.getValue().getAction()).isEqualTo(HybrisMqHandlingEnum.OPEN_API_BATCH_EDIT_PRICE.getAction());
		Assertions.assertThat(resultMap.getValue().getTraceId()).isEqualTo(Long.toString(TEST_RECORD_ROW_ID));
	}

	@Test
	void generateDateFromProductMasterUpdateProductRequest_batchEditProductReadyDays_generateDateFrom_ResponseSuccess() {
		//Given
		StoreDo storeDo = new StoreDo();
		storeDo.setStoreCode(TEST_STRING);
		storeDo.setStorefrontStoreCode("********");

		ProductMasterMqDto productMasterMqDto = new ProductMasterMqDto();
		productMasterMqDto.setProductReadyDays("1");
		productMasterMqDto.setRecordRowId(TEST_RECORD_ROW_ID);
		productMasterMqDto.setStores(TEST_STRING);
		productMasterMqDto.setSkuId("1219test001");


		//When
		when(storeRepository.findByStoreCodeIn(Mockito.any())).thenReturn(List.of(storeDo));
		when(saveProductRecordRowTempRepository.findByRecordRowId(any())).thenReturn(Optional.of(new SaveProductRecordRowTempDo()));
		when(saveProductHelper.generateDefaultStringValue(any())).thenReturn(storeDo.getStorefrontStoreCode());

		ResponseDto<Map<SaveProductRecordRowTempDo, HybrisUpdateProductMainMqMessage>> result = hybrisMqFlowHelper.
			generateDateFromProductMasterUpdateProductRequest(HybrisMqHandlingEnum.OPEN_API_BATCH_EDIT_PRODUCT_READY_DAYS, TEST_STRING, List.of(productMasterMqDto));

		//Then
		Assertions.assertThat(result.getStatus()).isEqualTo(StatusCodeEnum.SUCCESS.getCode());
		Assertions.assertThat(result.getData()).hasSize(1);
		Map.Entry<SaveProductRecordRowTempDo, HybrisUpdateProductMainMqMessage> resultMap = result.getData().entrySet().iterator().next();

		assertNotNull(resultMap.getValue());
		HybrisUpdateReadyPickupDaysActionDto mainMqMessage = (HybrisUpdateReadyPickupDaysActionDto) resultMap.getValue();

		assertEquals(mainMqMessage.getTraceId(), Long.toString(TEST_RECORD_ROW_ID));
		assertEquals(mainMqMessage.getFromSystem(), HybrisMqFromSystemEnum.MMS_PRODUCT.getCode());
		assertEquals(mainMqMessage.getAction(), HybrisMqHandlingEnum.OPEN_API_BATCH_EDIT_PRODUCT_READY_DAYS.getAction());
		assertEquals(mainMqMessage.getMerchantId(), "********");
		assertEquals(mainMqMessage.getSkuCode(), "1219test001");
		assertEquals(mainMqMessage.getReadyPickupDays(), 1);

	}

	@Test
	public void updateRecordRowTempByHybrisProductUpdateResult_hybrisReturnSuccess_SaveFailStatusData() {
		//Given
		SaveProductRecordRowTempDo saveProductRecordRowTempDo = new SaveProductRecordRowTempDo();
		when(saveProductRecordRepository.findByRecordRowId(any())).thenReturn(Optional.of(new SaveProductRecordDo()));
		when(saveProductRecordRowHelper.findSaveProductRecordRowTempByRecordRowIdRetryable(any())).thenReturn(Optional.of(saveProductRecordRowTempDo));

		//When
		HybrisProductResultMqMessageDto productMasterMqDto = new HybrisProductResultMqMessageDto();
		productMasterMqDto.setStatus(StatusCodeEnum.FAIL.name());
		productMasterMqDto.setTraceId(Long.toString(TEST_RECORD_ROW_ID));
		hybrisMqFlowHelper.updateRecordRowTempByHybrisProductUpdateResult(productMasterMqDto);

		//Then
		Assertions.assertThat(saveProductRecordRowTempDo.getStatus()).isEqualTo(SaveProductRecordRowTempStatusEnum.FAIL);
		Mockito.verify(saveProductRecordRowTempRepository, Mockito.times(1)).save(saveProductRecordRowTempDo);
	}

}
