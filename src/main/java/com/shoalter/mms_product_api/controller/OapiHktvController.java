package com.shoalter.mms_product_api.controller;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.service.openApi.OapiHktvBatchEditPriceService;
import com.shoalter.mms_product_api.service.openApi.OapiHktvBatchEditProductReadyDaysService;
import com.shoalter.mms_product_api.service.openApi.OapiHktvCreateEverutsBuyerService;
import com.shoalter.mms_product_api.service.openApi.OapiHktvGetProductDetailService;
import com.shoalter.mms_product_api.service.openApi.OapiHktvGetRecordStatusService;
import com.shoalter.mms_product_api.service.openApi.OapiHktvSingleSaveService;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditMainResponseData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditPriceMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditProductReadyDaysMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiCreateEverutsBuyerMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiCreateEverutsBuyerMainResponseData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiProductInfoBaseMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiProductInfoMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiResponseDto;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiSingleSaveMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiSingleSaveMainResponseData;
import com.shoalter.mms_product_api.service.product.pojo.response.CheckSaveProductRecordsStatusMainResponseData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Open api for hktv")
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/api/s2s/oapi/hktv")
public class OapiHktvController {

	private final Gson gson;

	private final OapiHktvSingleSaveService oapiHktvSingleSaveService;
	@Operation(summary = "單筆新增產品", description = "status 1代表成功-1代表失敗")
	@PostMapping("/single/save")
	public OapiResponseDto<OapiSingleSaveMainResponseData> singleSave(
		@RequestHeader(value = "storeCode") final String storefrontStoreCode,
		@RequestHeader(value = "platformCode") final String platformCode,
		@RequestHeader(value = "businessType") final String businessType,
		@RequestBody OapiSingleSaveMainRequestData oapiSingleSaveMainRequestData) {
		return oapiHktvSingleSaveService.start(storefrontStoreCode, platformCode, businessType, oapiSingleSaveMainRequestData);
	}

	private final OapiHktvGetProductDetailService oapiHktvGetProductDetailService;

	@Operation(summary = "Get product detail by store sku id")
	@GetMapping("/product/details")
	public OapiResponseDto<List<OapiProductInfoBaseMainRequestData>> getProductDetail(
		@RequestHeader(value = "storeCode") final String storefrontStoreCode,
		@RequestHeader(value = "platformCode") final String platformCode,
		@RequestHeader(value = "businessType") final String businessType,
		@RequestBody List<OapiProductInfoMainRequestData> request) {
		return oapiHktvGetProductDetailService.start(storefrontStoreCode, platformCode, businessType, request);
	}

	private final OapiHktvBatchEditPriceService oapiHktvBatchEditPriceService;

	@Operation(summary = "Batch update product price")
	@PostMapping("/batch/edit/price")
	public OapiResponseDto<OapiBatchEditMainResponseData> batchEditPrice(
		@RequestHeader(value = "storeCode") final String storefrontStoreCode,
		@RequestHeader(value = "platformCode") final String platformCode,
		@RequestHeader(value = "businessType") final String businessType,
		@RequestBody List<OapiBatchEditPriceMainRequestData> requestData) {
		return oapiHktvBatchEditPriceService.start(storefrontStoreCode, platformCode, businessType, requestData);
	}

	private final OapiHktvGetRecordStatusService oapiHktvGetRecordStatusService;

	@Operation(summary = "Get product records' status")
	@GetMapping("/record/status")
	public OapiResponseDto<List<CheckSaveProductRecordsStatusMainResponseData>> checkSaveProductRecordsStatus(
		@RequestHeader(value = "storeCode") final String storefrontStoreCode,
		@RequestHeader(value = "platformCode") final String platformCode,
		@RequestHeader(value = "businessType") final String businessType,
		@RequestParam List<Long> recordIds) {
		return oapiHktvGetRecordStatusService.start(storefrontStoreCode, platformCode, businessType, recordIds);
	}

	private final OapiHktvCreateEverutsBuyerService oapiHktvCreateEverutsBuyerService;

	@Operation(summary = "Create/Update Everuts Buyer Id")
	@PostMapping("/everuts/buyer")
	public OapiResponseDto<OapiCreateEverutsBuyerMainResponseData> createEverutsBuyerId(
		@RequestHeader(value = "storeCode") final String storefrontStoreCode,
		@RequestHeader(value = "platformCode") final String platformCode,
		@RequestHeader(value = "businessType") final String businessType,
		@RequestBody OapiCreateEverutsBuyerMainRequestData requestData) {
		return oapiHktvCreateEverutsBuyerService.start(storefrontStoreCode, platformCode, businessType, requestData);
	}

	private final OapiHktvBatchEditProductReadyDaysService oapiHktvBatchEditProductReadyDaysService;

	@Operation(summary = "Batch update product ready days")
	@PostMapping("/batch/edit/product-ready-days")
	public OapiResponseDto<OapiBatchEditMainResponseData> batchEditProductReadyDays(
		@RequestHeader(value = "storeCode") final String storefrontStoreCode,
		@RequestHeader(value = "platformCode") final String platformCode,
		@RequestHeader(value = "businessType") final String businessType,
		@RequestBody List<OapiBatchEditProductReadyDaysMainRequestData> requestData) {
		return oapiHktvBatchEditProductReadyDaysService.start(storefrontStoreCode, platformCode, businessType, requestData);
	}
}
