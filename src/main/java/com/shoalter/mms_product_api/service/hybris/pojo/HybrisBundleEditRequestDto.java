package com.shoalter.mms_product_api.service.hybris.pojo;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleInventoryInfoRequestDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleSettingRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
public class HybrisBundleEditRequestDto {

	private List<HybrisBundleEditParentDto> updateBundleSetList;

	private List<HybrisBundleEditChildDto> updateChildSkuList;

	public static HybrisBundleEditRequestDto generateHybrisBundleEditRequestDto(SingleEditProductDto singleEditProductDto) {

		BundleSettingRequestDto bundleSettingRequestDto = singleEditProductDto.getProduct().getBundleSetting();
		Integer ceilingQty = bundleSettingRequestDto.getMallInventoryInfo().stream()
				.filter(inventory -> inventory.getMall().equalsIgnoreCase(BuCodeEnum.HKTV.name()))
				.findAny().map(BundleInventoryInfoRequestDto::getCeilingQuantity)
				.orElseThrow(() -> new IllegalArgumentException("ceiling quantity not found"));

		List<HybrisBundleEditParentDto> parentDtoList = List.of(
				HybrisBundleEditParentDto.builder().parentSkuCode(
						singleEditProductDto.getProduct().getAdditional().getHktv().getStoreSkuId())
						.ceilingQty(ceilingQty).priority(bundleSettingRequestDto.getPriority())
						.isActive(bundleSettingRequestDto.getIsActive()).build()
		);

		List<HybrisBundleEditChildDto> childDtoList = bundleSettingRequestDto.getChildSkuInfo()
				.stream().map(childInfo -> HybrisBundleEditChildDto.builder()
						.childSkuCode(childInfo.getStorefrontStoreCode() + StringUtil.PRODUCT_SEPARATOR + childInfo.getChildSkuId())
				.ceilingQty(childInfo.getChildCeilingQuantity())
						.isLoop(childInfo.getIsLoop()).build())
				.collect(Collectors.toList());

		return HybrisBundleEditRequestDto.builder().updateBundleSetList(parentDtoList)
				.updateChildSkuList(childDtoList).build();
	}
}
