package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@Builder
public class ProductLittleMallSearchFlattenStoresRequestDto implements Serializable {
	private Integer page;

	@JsonProperty("storefront_store_codes")
	@SerializedName("storefront_store_codes")
	private List<String> storefrontStoreCodes;
}
