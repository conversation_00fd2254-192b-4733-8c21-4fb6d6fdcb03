package com.shoalter.mms_product_api.service.price_alert.pojo;

import com.shoalter.mms_product_api.config.product.ProductPriceAlertStatus;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceAlertDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductCheckDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductDo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductPriceAlertDto {

	private Integer merchantId;
	private String storefrontStoreCode;
	private String skuCode;
	private String productCode;
	private String skuNameCh;
	private BigDecimal tmallOriginalPrice;
	private BigDecimal tmallSellingPrice;
	private BigDecimal hktvOriginalPrice;
	private BigDecimal hktvSellingPrice;
	private String tmallUrl;
	private String status;
	private LocalDateTime lastUpdateDateTime;


	public static ProductPriceAlertDto from(ProductPriceAlertDo alert) {
		return ProductPriceAlertDto.builder()
			.merchantId(alert.getMerchantId())
			.skuCode(alert.getSkuCode())
			.skuNameCh(alert.getSkuNameTchi())
			.productCode(alert.getProductCode())
			.storefrontStoreCode(alert.getStore() != null ? alert.getStore().getStorefrontStoreCode() : null)
			.hktvOriginalPrice(alert.getSourceOriginalPrice())
			.hktvSellingPrice(alert.getSourceSellingPrice())
			.tmallOriginalPrice(alert.getTargetOriginalPrice())
			.tmallSellingPrice(alert.getTargetSellingPrice())
			.tmallUrl(alert.getTargetUrl())
			.status("Scheduled for Offline")
			.lastUpdateDateTime(alert.getLastUpdatedDate())
			.build();
	}

	public static ProductPriceAlertDto from(ProductPriceMonitorProductCheckDo check) {
		ProductPriceMonitorProductDo priceMonitorProduct = check.getProductPriceMonitorProduct();
		return ProductPriceAlertDto.builder()
			.merchantId(priceMonitorProduct.getMerchantId())
			.skuCode(priceMonitorProduct.getSkuCode())
			.skuNameCh(priceMonitorProduct.getSkuName())
			.productCode(priceMonitorProduct.getTargetProductCode())
			.storefrontStoreCode(priceMonitorProduct.getStorefrontStoreCode())
			.hktvOriginalPrice(check.getSourceOriginalPrice())
			.hktvSellingPrice(check.getSourceSellingPrice())
			.tmallOriginalPrice(check.getTargetOriginalPrice())
			.tmallSellingPrice(check.getTargetSellingPrice())
			.tmallUrl(check.getTargetUrl())
			.status("Scheduled for Offline")
			.lastUpdateDateTime(check.getLastUpdatedDate())
			.build();
	}
}
