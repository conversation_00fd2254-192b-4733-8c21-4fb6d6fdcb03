package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.ProductUploadExcelHelper;
import com.shoalter.mms_product_api.service.product.pojo.DownloadCreateProductTemplateRequestDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

@Service
@Slf4j
@RequiredArgsConstructor
public class CreateProductTemplateService implements ITemplateService {
    public final ProductUploadExcelHelper productUploadExcelHelper;

    public HttpEntity<ByteArrayResource> start(UserDto userDto, DownloadCreateProductTemplateRequestDto requestDto) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
		Workbook wb = productUploadExcelHelper.start(userDto, requestDto.getStoreId(), requestDto.getContractId(),
			null, null, null, false);
        try {
            wb.write(os);
            wb.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return new ResponseEntity<>(new ByteArrayResource(os.toByteArray()), getResponseHeader(), HttpStatus.OK);
    }

    @Override
    public String getFileName() {
        return "Create_Product_Template";
    }
}
