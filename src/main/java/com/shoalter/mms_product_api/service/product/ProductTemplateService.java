package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.edit_column.TemplateTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.service.product.helper.EditProductTemplateHelper;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductTemplateService {
	private final EditProductTemplateHelper editProductTemplateHelper;

	public HttpEntity<ByteArrayResource> start(TemplateTypeEnum templateType, boolean isErrorReport) {
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		Workbook workbook = editProductTemplateHelper.generateEditTemplateUnIncludeSysParameter(templateType, isErrorReport);
		try {
			workbook.write(os);
			workbook.close();
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		}
		return new ResponseEntity<>(new ByteArrayResource(os.toByteArray()), getResponseHeader(templateType.getExportName()), HttpStatus.OK);
	}

	public HttpHeaders getResponseHeader(String exportName) {
		String fileName = generateFileName(exportName);
		HttpHeaders header = new HttpHeaders();
		header.setContentType(MediaType.APPLICATION_OCTET_STREAM);
		header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
		return header;
	}

	private String generateFileName(String exportName) {
		String dateString = DateTimeFormatter.ofPattern(ConstantType.DATE_FORMAT_YMD).format(LocalDateTime.now());
		return String.format("%s_template_%s.%s", exportName, dateString, StringUtil.FILE_EXTENSION_EXCEL);
	}
}
