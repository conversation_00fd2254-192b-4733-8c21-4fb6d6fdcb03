package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.HybrisMqHandlingEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.dao.repository.product.ProductStoreStatusRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.mapper.ProductMasterDtoMapper;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.external_system.mms_setting.enums.MmsSettingFunctionEnum;
import com.shoalter.mms_product_api.service.external_system.mms_setting.helper.MmsSettingApiHelper;
import com.shoalter.mms_product_api.service.external_system.mms_setting.pojo.MmsSettingExchangeRateRequest;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ExchangeRatePriceDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterStoreSkuIdResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductData;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@Slf4j
class ExchangeRateHelperTest {

	@InjectMocks
	private ExchangeRateHelper exchangeRateHelper;
	@Mock
	private ProductStoreStatusRepository productStoreStatusRepository;
	@Mock
	private ProductMasterHelper productMasterHelper;
	@Mock
	private SysParmRepository sysParmRepository;
	@Mock
	private MmsSettingApiHelper mmsSettingApiHelper;
	@Mock
	private ProductMasterDtoMapper productMasterDtoMapper;
	@Mock
	private StoreRepository storeRepository;

	private final BigDecimal testRmbRate = BigDecimal.valueOf(1.075);
	private final BigDecimal testOriginalPrice = BigDecimal.valueOf(100);
	private final BigDecimal testSellingPrice = BigDecimal.valueOf(80);
	private final String testSkuId = "SKU001";
	private final String testStorefrontStoreCode = "STORE001";
	private UserDto userDto;
	private SaveProductData saveProductDataNonWhiteListSku;
	private SaveProductData saveProductDataWithWhiteListSku;
	private final Set<String> testHkdToRmbWhiteListSkuIds = Set.of("SKU001", "SKU002", "SKU003");

	@BeforeEach
	void setUp() {
		userDto = UserDto.builder()
			.userId(1)
			.userCode("ADMIN")
			.merchantId(1)
			.roleCode("ADMIN")
			.build();

		saveProductDataNonWhiteListSku = SaveProductData.builder().rmbToHkdExchangeRate(testRmbRate).build();
		saveProductDataWithWhiteListSku = SaveProductData.builder()
			.rmbToHkdExchangeRate(testRmbRate)
			.hkdToRmbWhiteListSkuIds(testHkdToRmbWhiteListSkuIds)
			.build();
	}

	@Test
	void test_GenerateSaveProductData_With_WhiteListSku() {
		Set<String> storefrontStoreCodeSet = Set.of(testStorefrontStoreCode);
		when(sysParmRepository.findBySegmentAndCodeIn(SysParmSegmentEnum.RMB_SKU_WHITELIST.name(), storefrontStoreCodeSet)).thenReturn(getMockWhiteListRmbSysParmList());
		when(exchangeRateHelper.getExchangeRateByCurrency(CurrencyEnum.RMB)).thenReturn(testRmbRate);
		SaveProductData saveProductData = exchangeRateHelper.generateSaveProductData(SaveProductType.SINGLE_EDIT_PRODUCT, storefrontStoreCodeSet);

		assertNotNull(saveProductData);
		assertEquals(testRmbRate, saveProductData.getRmbToHkdExchangeRate());
		assertTrue(saveProductData.getHkdToRmbWhiteListSkuIds().contains("SKU001"));
		assertTrue(saveProductData.getHkdToRmbWhiteListSkuIds().contains("SKU002"));
		assertTrue(saveProductData.getHkdToRmbWhiteListSkuIds().contains("SKU003"));
	}

	@Test
	void test_GenerateSaveProductData_Non_WhiteListSku() {
		Set<String> storefrontStoreCodeSet = Set.of(testStorefrontStoreCode);
		when(exchangeRateHelper.getExchangeRateByCurrency(CurrencyEnum.RMB)).thenReturn(testRmbRate);
		SaveProductData saveProductData = exchangeRateHelper.generateSaveProductData(SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT, storefrontStoreCodeSet);
		assertNotNull(saveProductData);
		assertEquals(testRmbRate, saveProductData.getRmbToHkdExchangeRate());
		assertTrue(CollectionUtil.isEmpty(saveProductData.getHkdToRmbWhiteListSkuIds()));
	}

	@Test
	void test_checkCurrencyAndGenerateHybrisPrice_IsWhiteListSku() {
		BigDecimal syncHybrisOriginalMainlandPrice = new BigDecimal("93.0233");
		ExchangeRatePriceDto exchangeRatePriceDto = exchangeRateHelper.checkCurrencyAndGenerateHybrisPrice(testSkuId, saveProductDataWithWhiteListSku, CurrencyEnum.HKD.name(), testOriginalPrice, testSellingPrice);
		assertNotNull(exchangeRatePriceDto);
		assertEquals(testRmbRate, exchangeRatePriceDto.getRmbToHkdExchangeRate());
		assertEquals(testOriginalPrice, exchangeRatePriceDto.getOriginalPriceHkd());
		assertEquals(syncHybrisOriginalMainlandPrice, exchangeRatePriceDto.getOriginalMainlandPrice());
		assertEquals(testSellingPrice, exchangeRatePriceDto.getSellingPriceHkd());
	}

	@Test
	void test_CheckRmbCurrencyAndGenerateHybrisPrice_IsRmb() {
		ExchangeRatePriceDto exchangeRatePriceDto = exchangeRateHelper.checkCurrencyAndGenerateHybrisPrice(testSkuId, saveProductDataNonWhiteListSku, CurrencyEnum.RMB.name(), testOriginalPrice, testSellingPrice);
		assertEquals(new BigDecimal("107.5000"), exchangeRatePriceDto.getOriginalPriceHkd());
		assertEquals(new BigDecimal("100"), exchangeRatePriceDto.getOriginalMainlandPrice());
		assertEquals(new BigDecimal("86.0000"), exchangeRatePriceDto.getSellingPriceHkd());
	}


	@Test
	void test_CheckRmbCurrencyAndGenerateHybrisPrice_NotRmb() {
		ExchangeRatePriceDto exchangeRatePriceDto = exchangeRateHelper.checkCurrencyAndGenerateHybrisPrice(testSkuId, saveProductDataNonWhiteListSku, CurrencyEnum.HKD.name(), testOriginalPrice, testSellingPrice);
		assertEquals(exchangeRatePriceDto.getOriginalPriceHkd(), testOriginalPrice);
		assertNull(exchangeRatePriceDto.getOriginalMainlandPrice());
		assertEquals(exchangeRatePriceDto.getSellingPriceHkd(), testSellingPrice);
	}

	@Test
	void test_CheckRmbCurrencyAndGenerateHybrisPrice_IsRmb_SellingPriceIsNull() {
		ExchangeRatePriceDto exchangeRatePriceDto = exchangeRateHelper.checkCurrencyAndGenerateHybrisPrice(testSkuId, saveProductDataNonWhiteListSku, CurrencyEnum.RMB.name(), testOriginalPrice, null);
		assertEquals(new BigDecimal("107.5000"), exchangeRatePriceDto.getSellingPriceHkd());
	}


	@Test
	void test_CheckRmbCurrencyAndGenerateHybrisPrice_NotRmb_SellingPriceIsNull() {
		ExchangeRatePriceDto exchangeRatePriceDto = exchangeRateHelper.checkCurrencyAndGenerateHybrisPrice(testSkuId, saveProductDataNonWhiteListSku, CurrencyEnum.HKD.name(), testOriginalPrice, null);
		assertEquals(new BigDecimal("100"), exchangeRatePriceDto.getSellingPriceHkd());
	}

	@Test
	void test_CheckCurrencyGenerateSellingPrice_IsRmb() {
		BigDecimal sellingPrice = exchangeRateHelper.checkCurrencyGenerateSellingPrice(CurrencyEnum.RMB.name(), testRmbRate, testSellingPrice);
		assertEquals(new BigDecimal("86.0000"), sellingPrice);
	}

	@Test
	void test_CheckCurrencyGenerateSellingPrice_NotRmb() {
		BigDecimal sellingPrice = exchangeRateHelper.checkCurrencyGenerateSellingPrice(CurrencyEnum.HKD.name(), BigDecimal.TEN, testSellingPrice);
		assertEquals(sellingPrice, testSellingPrice);
	}

	@Test
	void test_CheckCurrencyAndGeneraLittleMallPrice_IsMainlandContract() {
		ExchangeRatePriceDto exchangeRatePriceDto = exchangeRateHelper.checkCurrencyAndGeneraLittleMallPrice(testRmbRate, testOriginalPrice, testSellingPrice, true);
		assertEquals(new BigDecimal("107.50"), exchangeRatePriceDto.getOriginalPriceHkd());
		assertNull(exchangeRatePriceDto.getOriginalMainlandPrice());
		assertEquals(new BigDecimal("86.00"), exchangeRatePriceDto.getSellingPriceHkd());
	}

	@Test
	void test_CheckCurrencyAndGeneraLittleMallPrice_NotMainlandContract() {
		ExchangeRatePriceDto exchangeRatePriceDto = exchangeRateHelper.checkCurrencyAndGeneraLittleMallPrice(null, testOriginalPrice, testSellingPrice, false);
		assertEquals(exchangeRatePriceDto.getOriginalPriceHkd(), testOriginalPrice);
		assertNull(exchangeRatePriceDto.getOriginalMainlandPrice());
		assertEquals(exchangeRatePriceDto.getSellingPriceHkd(), testSellingPrice);
	}

	@Test
	void convertHybrisMqExchangeRatePrice_Success() {
		ExchangeRatePriceDto result = exchangeRateHelper.convertHybrisMqExchangeRatePrice(
			HybrisMqHandlingEnum.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB,
			testSkuId,
			saveProductDataNonWhiteListSku,
			CurrencyEnum.RMB.name(),
			testOriginalPrice
		);

		assertNotNull(result);
		assertEquals(new BigDecimal("107.5000"), result.getOriginalPriceHkd());
		assertEquals(new BigDecimal("100"), result.getOriginalMainlandPrice());
	}

	@Test
	void convertHybrisMqExchangeRatePrice_NullExchangeRate() {
		SaveProductData saveProductData = SaveProductData.builder().build();
		ExchangeRatePriceDto result = exchangeRateHelper.convertHybrisMqExchangeRatePrice(
			HybrisMqHandlingEnum.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB,
			testSkuId,
			saveProductData,
			CurrencyEnum.RMB.name(),
			BigDecimal.ONE
		);
		assertNotNull(result);
		assertNull(result.getOriginalPriceHkd());
		assertNull(result.getOriginalMainlandPrice());
	}

	@Test
	void convertHybrisMqExchangeRatePrice_WhiteListSku() {
		BigDecimal syncHybrisOriginalMainlandPrice = new BigDecimal("93.0233");
		ExchangeRatePriceDto result = exchangeRateHelper.convertHybrisMqExchangeRatePrice(
			HybrisMqHandlingEnum.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB,
			testSkuId,
			saveProductDataWithWhiteListSku,
			CurrencyEnum.HKD.name(),
			testOriginalPrice
		);
		assertNotNull(result);
		assertEquals(testRmbRate, result.getRmbToHkdExchangeRate());
		assertEquals(testOriginalPrice, result.getOriginalPriceHkd());
		assertEquals(syncHybrisOriginalMainlandPrice, result.getOriginalMainlandPrice());
	}

	@Test
	void getExchangeRateByCurrency_Success() {
		// mock
		BigDecimal expectedRate = new BigDecimal("1.075");
		when(mmsSettingApiHelper.requestExchangeRate(any()))
			.thenReturn(expectedRate);
		// test
		BigDecimal result = exchangeRateHelper.getExchangeRateByCurrency(CurrencyEnum.RMB);

		// assert
		assertEquals(expectedRate, result);
	}

	@Test
	void getExchangeRateByCurrency_EmptyResponse() {
		when(mmsSettingApiHelper.requestExchangeRate(any())).thenReturn(null);

		BigDecimal result = exchangeRateHelper.getExchangeRateByCurrency(CurrencyEnum.RMB);
		assertNull(result);

		verify(mmsSettingApiHelper).requestExchangeRate(getMockMmsSettingExchangeRateRequest());
	}

	@Test
	void findCurrencyRmbMerchantProductMap_Success() {
		// mock mainland contact storeSkuIds
		List<String> mockStoreSkuIds = Arrays.asList("AAA_S_SKU001", "AAA_S_SKU002");
		when(productStoreStatusRepository.findStoreSkuIdsByContractTypes(ContractType.MAINLAND_MERCHANT_CONTRACT_SET))
			.thenReturn(mockStoreSkuIds);
		when(storeRepository.findStorefrontStoreCodeByBuAndMerchants(BuCodeEnum.HKTV.name(), Set.of(1, 2, 3))).thenReturn(new ArrayList<>());

		ProductMasterStoreSkuIdResponseDto mockResponse = new ProductMasterStoreSkuIdResponseDto();
		mockResponse.setStatus(StatusCodeEnum.SUCCESS.name());

		List<ProductMasterResultDto> mockResults = Arrays.asList(
			createMockProductMasterResult(1, "AAA_S_SKU001"),
			createMockProductMasterResult(2, "AAA_S_SKU002"),
			createMockProductMasterResult(3, String.format("%s_S_%s", testStorefrontStoreCode, "SKU001")),
			createMockProductMasterResult(3, String.format("%s_S_%s", testStorefrontStoreCode, "SKU002")),
			createMockProductMasterResult(3, String.format("%s_S_%s", testStorefrontStoreCode, "SKU003"))
		);
		mockResponse.setData(mockResults);

		when(productMasterHelper.requestProductByStoreSkuId(any(), any()))
			.thenReturn(mockResponse);
		when(sysParmRepository.findBySegment(any())).thenReturn(new ArrayList<>());

		// test
		Map<Integer, List<SingleEditProductDto>> result =
			exchangeRateHelper.findCurrencyRmbMerchantProductMap(userDto);

		// assert
		assertNotNull(result);
		assertEquals(3, result.size());
		assertTrue(result.containsKey(1));
		assertTrue(result.containsKey(2));
		assertTrue(result.containsKey(3));
		assertEquals(1, result.get(1).size());
		assertEquals(1, result.get(2).size());
	}

	private ProductMasterResultDto createMockProductMasterResult(int merchantId, String skuId) {
		ProductMasterResultDto result = new ProductMasterResultDto();
		BuProductDto additional = new BuProductDto();
		HktvProductDto hktvDto = new HktvProductDto();
		result.setAdditional(additional);
		additional.setHktv(hktvDto);

		result.setMerchantId(merchantId);
		result.setSkuId(skuId);
		result.setSkuNameEn("Product " + skuId);
		result.setSkuNameCh("產品 " + skuId);
		result.setOriginalPrice(new BigDecimal("100.00"));
		result.getAdditional().getHktv().setSellingPrice(new BigDecimal("80.00"));
		return result;
	}

	private MmsSettingExchangeRateRequest getMockMmsSettingExchangeRateRequest() {
		return MmsSettingExchangeRateRequest.builder()
			.functionCode(MmsSettingFunctionEnum.PRODUCT)
			.targetCurrency(CurrencyEnum.HKD)
			.baseCurrency(CurrencyEnum.RMB)
			.build();
	}

	private List<SysParmDo> getMockWhiteListRmbSysParmList() {
		SysParmDo sysParmDo = new SysParmDo();
		sysParmDo.setCode(testStorefrontStoreCode);
		sysParmDo.setParmValue("SKU001,SKU002,SKU003");
		return List.of(sysParmDo);
	}
}
