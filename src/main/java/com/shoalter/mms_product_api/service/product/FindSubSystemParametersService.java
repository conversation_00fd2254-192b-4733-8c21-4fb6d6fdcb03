package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.SubSysParmMainRequestData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class FindSubSystemParametersService {
	private final SysParmRepository sysParmRepository;


	public ResponseDto<List<SysParmDo>> start(List<SubSysParmMainRequestData> subSysParmMainRequestDataList) {
		if (subSysParmMainRequestDataList.isEmpty()) {
			return ResponseDto.success(new ArrayList<>());
		}

		List<SysParmDo> sysParmDoList = subSysParmMainRequestDataList.stream()
			.flatMap(dto -> sysParmRepository.findByParentSegmentAndParentCodeAndCodeAndBuCode(
				dto.getParentSegment(), dto.getParentCode(), dto.getCode(), dto.getPlatform()).stream())
			.collect(Collectors.toList());

		return ResponseDto.success(sysParmDoList);
	}
}
