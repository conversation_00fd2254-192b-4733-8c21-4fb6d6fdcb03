package com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo;

import com.shoalter.mms_product_api.config.product.ThirdPartySourceEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "AFFILIATE_EXPORT_INFO")
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class AffiliateExportInfoDo extends BuExportHistoryDo{
	@Column(name = "STORE_ID")
	private Integer storeId;

	@Column(name = "AFFILIATE_RESOURCE_TYPE")
	private ThirdPartySourceEnum affiliateResourceType;

}
