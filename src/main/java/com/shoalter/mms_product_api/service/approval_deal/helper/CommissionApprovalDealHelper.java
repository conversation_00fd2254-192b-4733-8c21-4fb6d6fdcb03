package com.shoalter.mms_product_api.service.approval_deal.helper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ProductContractMatchStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.approval_deal.ApprovalDealRepository;
import com.shoalter.mms_product_api.dao.repository.approval_deal.ApprovalDealStatusHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.approval_deal.ApprovalDealTempRepository;
import com.shoalter.mms_product_api.dao.repository.approval_deal.CommissionApprovalDealRepository;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealDo;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealIdAndSkuCodeViewDo;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealIdAndVariantViewDo;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealStatusHistoryDo;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealTempDo;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.CommissionApprovalDealDo;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.CommissionApprovalDealGenerateData;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractProdTermsDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductStoreStatusRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreStatusDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealStatusEnum;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealTempActionEnum;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealTempStatusEnum;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealTypeEnum;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealSendMessageDto;
import com.shoalter.mms_product_api.service.approval_deal.pojo.CheckApprovalDealDto;
import com.shoalter.mms_product_api.service.approval_deal.pojo.CheckCommissionRateApprovalDto;
import com.shoalter.mms_product_api.service.approval_deal.pojo.CheckCommissionRateDto;
import com.shoalter.mms_product_api.service.approval_deal.pojo.CommissionApprovalDealPrepareData;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.mms_product.pojo.BatchProductAsyncDto;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductHelper;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.StringUtil;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class CommissionApprovalDealHelper {

	private final SaveProductHelper saveProductHelper;
	private final ProductMasterHelper productMasterHelper;
	private final ApprovalDealHelper approvalDealHelper;
	private final ApprovalDealStatusHelper approvalDealStatusHelper;
	private final ApprovalDealTempRepository approvalDealTempRepository;
	private final ApprovalDealRepository approvalDealRepository;
	private final ApprovalDealStatusHistoryRepository approvalDealStatusHistoryRepository;
	private final CommissionApprovalDealRepository commissionApprovalDealRepository;
	private final ProductStoreStatusRepository productStoreStatusRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final StoreRepository storeRepository;
	private final Gson gson;
	private final ObjectMapper objectMapper;
	private final MessageSource messageSource;

	@Transactional
	public void batchProcessAndCheckApproval(UserDto userDto, BatchProductAsyncDto batchProductAsyncDto) {
		if (batchProductAsyncDto == null || CollectionUtil.isEmpty(batchProductAsyncDto.getProductList()) || batchProductAsyncDto.getSaveProductRecordDo() == null) {
			return;
		}
		SaveProductRecordDo record = batchProductAsyncDto.getSaveProductRecordDo();
		log.info("CommissionRateApproval batchProcessAndCheckApproval start, record id:{}, uploadType:{}", record.getId(), SaveProductTypeEnum.getProductTypeName(record.getUploadType()));
		CommissionApprovalDealPrepareData prepareData = prepareCommissionApprovalDealData(userDto, batchProductAsyncDto);
		if (prepareData == null) {
			return;
		}
		final BuCodeEnum defaultBuCode = BuCodeEnum.HKTV;
		final ApprovalDealTypeEnum approvalDealType = ApprovalDealTypeEnum.COMMISSION_RATE;

		List<SaveProductRecordRowDo> failRowsAndWaitingApprovalRows = new ArrayList<>();
		List<ApprovalDealTempDo> needToSaveApprovalDealTemps = new ArrayList<>();
		Map<String, Set<SaveProductRecordRowDo>> cancelPrimarySkuRowMap = new HashMap<>();
		Map<String, List<ApprovalDealTempDo>> cancelApprovalMap = new HashMap<>();

		for (ProductMasterDto editProduct : prepareData.getEditProducts()) {
			SaveProductRecordRowDo editRow = prepareData.getEditRowUuidMap().get(editProduct.getUuid());
			// fail row
			if (editRow == null) {
				log.info("SaveProductRecordRowDo not found for uuid :{}", editProduct.getUuid());
				continue;
			}

			ProductMasterResultDto originalProduct = prepareData.getAllOriginalProductsUuidMap().get(editProduct.getUuid());
			if (approvalDealHelper.isAnyHktvDataNull(editProduct, originalProduct)) {
				log.info("editProduct or originalProduct fields null");
				continue;
			}

			CheckCommissionRateApprovalDto checkDto = CheckCommissionRateApprovalDto.generateWithChecks(editProduct, originalProduct);
			log.info("CheckCommissionRateApprovalDto:{}", gson.toJson(checkDto));
			String storefrontStoreCodeAndProductCode = prepareData.getStorefrontStoreCode() + "-" + editProduct.getProductId();

			if (checkDto.isPrimaryCategoryModified() && ConstantType.CONSTANT_NO.equals(editProduct.getAdditional().getHktv().getIsPrimarySku())) {
				ApprovalDealIdAndVariantViewDo primarySkuAndVariantSku = prepareData.getPrimarySkuAndVariantSkuMap().get(storefrontStoreCodeAndProductCode);
				if (primarySkuAndVariantSku != null && primarySkuAndVariantSku.getPrimarySkuCount() > 0) {
					log.error("Cannot modify the variant SKU's primary category code before the primary SKU's approval deal is approved, " +
						"storefrontStoreCodeAndProductCode:{}, sku:{}, uuid:{}, row id:{}", editProduct.getSkuId(), storefrontStoreCodeAndProductCode, editProduct.getUuid(), editRow.getId());
					editRow.setStatus(SaveProductStatus.FAIL);
					editRow.setErrorMessage(messageSource.getMessage("message310", null, null));
					failRowsAndWaitingApprovalRows.add(editRow);
					continue;
				}
			}

			if (checkDto.isPrimaryCategoryOrBrandModified() && checkDto.isProductReadyMethodModified()) {
				log.error("isPrimaryCategoryOrBrandModified && isProductReadyMethodModified, sku:{}, uuid:{}, row id:{}", editProduct.getSkuId(), editProduct.getUuid(), editRow.getId());
				editRow.setStatus(SaveProductStatus.FAIL);
				editRow.setErrorMessage(messageSource.getMessage("message293", null, null));
				failRowsAndWaitingApprovalRows.add(editRow);
				continue;
			}

			CheckCommissionRateDto checkCommissionRateResult = isCommissionRateSmallThanOriginal(editProduct);
			log.info("checkCommissionRateResult:{}", gson.toJson(checkCommissionRateResult));
			if (CollectionUtil.isNotEmpty(checkCommissionRateResult.getErrorMessage())) {
				log.error("checkCommissionRateResult error, sku:{}, uuid:{}, row id:{}, error:{}", editProduct.getSkuId(), editProduct.getUuid(), editRow.getId(), checkCommissionRateResult.getErrorMessage());
				editRow.setStatus(SaveProductStatus.FAIL);
				editRow.setErrorMessage(checkCommissionRateResult.getErrorMessage().toString());
				failRowsAndWaitingApprovalRows.add(editRow);
				continue;
			}

			Long approvalRecordId = prepareData.getApprovalDealsSkuCodeMap().get(editProduct.getSkuId());
			if (!checkDto.isPrimarySkuBrandChange() && isNormalEditProcess(checkDto, approvalRecordId == null, checkCommissionRateResult.isSmallThanOriginal())) {
				continue;
			}

			// cancel
			List<ApprovalDealTempDo> cancelApprovalList = cancelApprovalMap.computeIfAbsent(storefrontStoreCodeAndProductCode, k -> new ArrayList<>());
			if (checkDto.isPrimarySkuBrandChange()) {
				ApprovalDealIdAndVariantViewDo primarySkuAndVariantSku = prepareData.getPrimarySkuAndVariantSkuMap().get(storefrontStoreCodeAndProductCode);
				if (primarySkuAndVariantSku != null) {
					String variantSkuIdsString = primarySkuAndVariantSku.getVariantSkuIds();
					ApprovalDealTempDo cancelApprovalDealTempDo = ApprovalDealTempDo.generateApprovalDealTempCancelData(userDto.getUserCode(), defaultBuCode, approvalDealType, approvalRecordId, variantSkuIdsString, editRow.getId());
					if (cancelApprovalDealTempDo != null) {
						cancelApprovalList.add(cancelApprovalDealTempDo);
						cancelPrimarySkuRowMap.computeIfAbsent(storefrontStoreCodeAndProductCode, k -> new HashSet<>()).add(editRow);
						log.info("cancel primarySku approvalDealTemp:IsPrimarySku brand modified, approvalRecordId:{}, variantSkuIds:{}, storefrontStoreCodeAndProductCode:{}, skuCode:{}, row id:{}", approvalRecordId, variantSkuIdsString, storefrontStoreCodeAndProductCode, editProduct.getSkuId(), editRow.getId());
					}
				}
			} else if (isNeedToCancelCommissionApproval(checkDto, approvalRecordId != null)) {
				ApprovalDealTempDo cancelApprovalDealTempDo = ApprovalDealTempDo.generateApprovalDealTempCancelData(userDto.getUserCode(), defaultBuCode, approvalDealType, approvalRecordId, null, editRow.getId());
				if (cancelApprovalDealTempDo != null) {
					cancelApprovalList.add(cancelApprovalDealTempDo);
					log.info("cancel sku approvalDealTemp: approvalRecordId:{}, skuCode:{}, row id:{}", approvalRecordId, editProduct.getSkuId(), editRow.getId());
				}
			}

			// create
			if (isNeedToCreateCommissionApproval(checkDto.isPrimaryCategoryOrBrandModified(), checkCommissionRateResult.isSmallThanOriginal())) {
				// prevent storefrontStoreCode null for different product save type
				editProduct.getAdditional().getHktv().setStorefrontStoreCode(prepareData.getStorefrontStoreCode());
				String tempContent = getCommissionApprovalDealTempContent(editProduct, originalProduct, userDto.getUserCode(), editRow.getId(), defaultBuCode, checkCommissionRateResult);
				if (StringUtil.isNotEmpty(tempContent)) {
					ApprovalDealTempDo createApprovalDealTempDo = ApprovalDealTempDo.generateApprovalDealTempCreateData(userDto.getUserCode(), defaultBuCode, approvalDealType, editRow.getId(), tempContent);
					needToSaveApprovalDealTemps.add(createApprovalDealTempDo);
					// if need to waiting approval, set original value for find variant sku
					SaveProductRecordRowDo convertToOriginalValueRow = convertWaitingApprovalFieldsFromOriginalValue(editRow, originalProduct);
					failRowsAndWaitingApprovalRows.add(convertToOriginalValueRow);
					log.info("create sku approvalDealTemp: row id:{}, sku:{}", editRow.getId(), editProduct.getSkuId());
				}
			}
		}
		processBatchEditCancelApprovalWithVariant(cancelApprovalMap, cancelPrimarySkuRowMap, needToSaveApprovalDealTemps);
		saveProductRecordRowRepository.saveAll(failRowsAndWaitingApprovalRows);

		List<ApprovalDealTempDo> validApprovalDealTemps = needToSaveApprovalDealTemps.stream()
			.filter(Objects::nonNull)
			.collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(validApprovalDealTemps)) {
			approvalDealTempRepository.saveAll(validApprovalDealTemps);
		}

		log.info("batchProcessAndCheckCommissionRateApproval end");
	}

	@Transactional
	public CheckApprovalDealDto processAndCheckApproval(UserDto userDto, SaveProductRecordRowDo row, ProductMasterResultDto originalProduct) {
		CheckApprovalDealDto checkApprovalDealDto = new CheckApprovalDealDto();
		checkApprovalDealDto.setNeedToApproval(false);
		if (row == null) return checkApprovalDealDto;
		log.info("processAndCheckCommissionRateApproval start, record row id: {}", row.getId());


		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		ProductMasterDto editProduct = singleEditProductDto.getProduct();
		if (approvalDealHelper.isAnyHktvDataNull(editProduct, originalProduct)) {
			log.info("editProduct or originalProduct fields null");
			return checkApprovalDealDto;
		}

		final BuCodeEnum defaultBuCode = BuCodeEnum.HKTV;
		final ApprovalDealTypeEnum approvalDealType = ApprovalDealTypeEnum.COMMISSION_RATE;
		final String storefrontStoreCode = findStorefrontStoreCodeByStoreCode(singleEditProductDto);
		// prevent storefrontStoreCode null for different product save type
		editProduct.getAdditional().getHktv().setStorefrontStoreCode(storefrontStoreCode);

		CheckCommissionRateApprovalDto checkDto = CheckCommissionRateApprovalDto.generateWithChecks(editProduct, originalProduct);
		log.info("CheckCommissionRateApprovalDto:{}", gson.toJson(checkDto));
		Optional<Long> approvalDealId = approvalDealRepository.findIdByBuAndStorefrontStoreCodeAndSkuCodeAndApprovalTypeAndStatus(
			defaultBuCode.name(), storefrontStoreCode, editProduct.getSkuId(), approvalDealType.name(), ApprovalDealStatusEnum.PENDING_APPROVALS_STATUS_SET);

		if (checkDto.isPrimaryCategoryModified() && ConstantType.CONSTANT_NO.equals(editProduct.getAdditional().getHktv().getIsPrimarySku())) {
			int primarySkuApprovalDealCount = approvalDealRepository.countIsPrimarySkuByBuAndStorefrontStoreCodeAndProductCodeAndApprovalTypeAndStatus(
				defaultBuCode.name(), storefrontStoreCode, editProduct.getProductId(), approvalDealType.name(), ApprovalDealStatusEnum.PENDING_APPROVALS_STATUS_SET, true);
			if (primarySkuApprovalDealCount > 0) {
				log.error("Cannot modify the variant SKU's primary category code before the primary SKU's approval deal is approved, " +
					"storefrontStoreCode:{}, ProductCode:{}, sku:{}, uuid:{}, row id:{}", storefrontStoreCode, editProduct.getProductId(), editProduct.getSkuId(), editProduct.getUuid(), row.getId());
				checkApprovalDealDto.setErrorMessage(List.of(messageSource.getMessage("message310", null, null)));
				return checkApprovalDealDto;
			}
		}

		CheckCommissionRateDto checkCommissionRateResult = isCommissionRateSmallThanOriginal(editProduct);
		log.info("checkCommissionRateResult:{}", gson.toJson(checkCommissionRateResult));
		if (CollectionUtil.isNotEmpty(checkCommissionRateResult.getErrorMessage())) {
			log.error("checkCommissionRateResult error, sku:{}, uuid:{}, row id:{}, error:{}", editProduct.getSkuId(), editProduct.getUuid(), row.getId(), checkCommissionRateResult.getErrorMessage());
			checkApprovalDealDto.setErrorMessage(checkCommissionRateResult.getErrorMessage());
			return checkApprovalDealDto;
		}

		if (!checkDto.isPrimarySkuBrandChange() && isNormalEditProcess(checkDto, approvalDealId.isEmpty(), checkCommissionRateResult.isSmallThanOriginal())) {
			return checkApprovalDealDto;
		}
		List<ApprovalDealTempDo> needToSaveApprovalDealTemps = new ArrayList<>();
		if (checkDto.isPrimarySkuBrandChange()) {
			Set<Long> variantSkuNeedToCancelApprovalIds = approvalDealRepository.findAllByBuAndStorefrontStoreCodeAndProductCodeAndApprovalTypeAndStatus(
				defaultBuCode.name(), storefrontStoreCode, editProduct.getProductId(), approvalDealType.name(), ApprovalDealStatusEnum.PENDING_APPROVALS_STATUS_SET);
			if (CollectionUtil.isNotEmpty(variantSkuNeedToCancelApprovalIds)) {
				approvalDealId.ifPresent(variantSkuNeedToCancelApprovalIds::remove);
				String variantSkuIdsString = variantSkuNeedToCancelApprovalIds.stream()
					.map(String::valueOf)
					.collect(Collectors.joining(StringUtil.COMMA));
				ApprovalDealTempDo cancelApprovalDealTempDo = ApprovalDealTempDo.generateApprovalDealTempCancelData(userDto.getUserCode(), defaultBuCode, approvalDealType, approvalDealId.orElse(null), variantSkuIdsString, row.getId());
				if (cancelApprovalDealTempDo != null) {
					needToSaveApprovalDealTemps.add(cancelApprovalDealTempDo);
					log.info("cancel primarySku approvalDealTemp:IsPrimarySku brand modified, approvalRecordId:{}, variantSkuIds:{}, storefrontStoreCode:{}, ProductCode:{}, skuCode:{}, row id:{}", approvalDealId.orElse(null), variantSkuIdsString, storefrontStoreCode, editProduct.getProductId(), editProduct.getSkuId(), row.getId());
				}
			}
		} else if (isNeedToCancelCommissionApproval(checkDto, approvalDealId.isPresent())) {
			ApprovalDealTempDo cancelApprovalDealTempDo = ApprovalDealTempDo.generateApprovalDealTempCancelData(userDto.getUserCode(), defaultBuCode, approvalDealType, approvalDealId.get(), null, row.getId());
			if (cancelApprovalDealTempDo != null) {
				needToSaveApprovalDealTemps.add(cancelApprovalDealTempDo);
				log.info("cancel sku approvalDealTemp: approvalRecordId:{}, skuCode:{}, row id:{}", approvalDealId, editProduct.getSkuId(), row.getId());
			}
		}

		if (isNeedToCreateCommissionApproval(checkDto.isPrimaryCategoryOrBrandModified(), checkCommissionRateResult.isSmallThanOriginal())) {
			String tempContent = getCommissionApprovalDealTempContent(editProduct, originalProduct, userDto.getUserCode(), row.getId(), defaultBuCode, checkCommissionRateResult);
			ApprovalDealTempDo createApprovalDealTempDo = ApprovalDealTempDo.generateApprovalDealTempCreateData(userDto.getUserCode(), defaultBuCode, approvalDealType, row.getId(), tempContent);
			if (createApprovalDealTempDo != null) {
				needToSaveApprovalDealTemps.add(createApprovalDealTempDo);
				checkApprovalDealDto.setNeedToApproval(true);
				log.info("create sku approvalDealTemp: row id:{}, sku:{}", row.getId(), editProduct.getSkuId());
			}
		}

		List<ApprovalDealTempDo> validApprovalDealTemps = needToSaveApprovalDealTemps.stream()
			.filter(Objects::nonNull)
			.collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(validApprovalDealTemps)) {
			approvalDealTempRepository.saveAll(validApprovalDealTemps);
		}
		log.info("processAndCheckCommissionRateApproval end");
		return checkApprovalDealDto;
	}

	@Transactional
	public ResponseDto<List<ApprovalDealSendMessageDto>> createOrCancelApprovalDealAndGenerateMessage(Long recordId, Integer uploadType, List<SaveProductRecordRowDo> editRows) {
		List<ApprovalDealSendMessageDto> approvalDealSendMessages = null;
		log.info("createOrCancelApprovalDealAndGenerateMessage start record id:{}, uploadType:{}", recordId, SaveProductTypeEnum.getProductTypeName(uploadType));

		Map<Long, String> recordRowIdWithUuidMap = new HashMap<>();
		List<CommissionApprovalDealDo> commissionApprovalDealDos = new ArrayList<>();
		List<SaveProductRecordRowDo> failRows = new ArrayList<>();
		try {
			Map<Long, List<ApprovalDealTempDo>> approvalTempRecordRowIdMap = approvalDealHelper.findWaitingCreateAndCancelApprovalTempByRecordRows(editRows, ApprovalDealTypeEnum.COMMISSION_RATE);
			if (approvalTempRecordRowIdMap.isEmpty()) {
				log.info("No need to save approvalDeal");
				return null;
			}
			log.info("approvalTempRecordRowIdMap size:{}", approvalTempRecordRowIdMap.size());
			for (SaveProductRecordRowDo editRow : editRows) {
				SpringBeanProvider.getBean(CommissionApprovalDealHelper.class).createOrCancelApprovalDealFromTemp(approvalTempRecordRowIdMap, commissionApprovalDealDos, editRow, recordRowIdWithUuidMap, failRows);
			}
		} catch (Exception e) {
			log.error("createOrCancelApprovalDealAndGenerateMessage Error, recordId:{}", recordId, e);
			return ResponseDto.fail(null);
		}
		if (CollectionUtil.isNotEmpty(failRows)) {
			saveProductRecordRowRepository.saveAll(failRows);
		}
		approvalDealSendMessages = generateApprovalDealSendMessages(commissionApprovalDealDos, recordRowIdWithUuidMap);
		log.info("createOrCancelApprovalDealAndGenerateMessage end record id:{}", recordId);
		return ResponseDto.success(approvalDealSendMessages);
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void createOrCancelApprovalDealFromTemp(Map<Long, List<ApprovalDealTempDo>> approvalTempRecordRowIdMap, List<CommissionApprovalDealDo> commissionApprovalDealDos, SaveProductRecordRowDo editRow, Map<Long, String> recordRowIdWithUuidMap, List<SaveProductRecordRowDo> failRows) {
		try {
			List<ApprovalDealTempDo> approvalDealTemps = approvalTempRecordRowIdMap.get(editRow.getId());
			if (CollectionUtil.isEmpty(approvalDealTemps)) {
				return;
			}
			if (editRow.getStatus() == SaveProductStatus.SUCCESS) {
				for (ApprovalDealTempDo approvalDealTemp : approvalDealTemps) {
					if (approvalDealTemp.getAction() == ApprovalDealTempActionEnum.CANCEL) {
						cancelApprovalDeal(approvalDealTemp);
					} else if (approvalDealTemp.getAction() == ApprovalDealTempActionEnum.CREATE) {
						createApprovalDeal(approvalDealTemp, recordRowIdWithUuidMap, editRow, commissionApprovalDealDos);
					}
				}
			} else if (editRow.getStatus() == SaveProductStatus.FAIL) {
				List<ApprovalDealTempDo> updateTemps = new ArrayList<>();
				approvalDealTemps.forEach(approvalDealTemp -> {
						approvalDealTemp.setStatus(ApprovalDealTempStatusEnum.FAIL);
						updateTemps.add(approvalDealTemp);
					}
				);
				// update temp status
				if (CollectionUtil.isNotEmpty(updateTemps)) {
					approvalDealTempRepository.saveAll(updateTemps);
				}
			}
		} catch (Exception e) {
			log.error("createOrCancelApprovalDealFromTemp Error, rowId:{}", editRow.getId(), e);
			editRow.setStatus(SaveProductStatus.FAIL);
			editRow.setErrorMessage(messageSource.getMessage("message319", null, null));
			failRows.add(editRow);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
		}
	}

	private void cancelApprovalDeal(ApprovalDealTempDo approvalDealTemp) {
		Set<Long> updateApprovalDealStatusToCancelIds = new HashSet<>();
		List<ApprovalDealStatusHistoryDo> saveApprovalDealHistorys = new ArrayList<>();

		Optional.ofNullable(approvalDealTemp.getApprovalDealId()).ifPresent(updateApprovalDealStatusToCancelIds::add);
		if (StringUtils.isNotBlank(approvalDealTemp.getApprovalDealIdVariant())) {
			updateApprovalDealStatusToCancelIds.addAll(
				Arrays.stream(approvalDealTemp.getApprovalDealIdVariant().split(StringUtil.COMMA))
					.map(Long::valueOf).collect(Collectors.toSet()));
		}
		List<ApprovalDealDo> cancelApprovalDeals = approvalDealRepository.findAllById(updateApprovalDealStatusToCancelIds);
		if (CollectionUtil.isNotEmpty(cancelApprovalDeals)) {
			cancelApprovalDeals.forEach(cancelApprovalDeal -> {
				log.info("cancel approval deal: approvalDealId:{}, rowId:{}, approvalType:{}, merchantId:{}, bu:{}, storefrontStoreCode:{}, productCode:{}, sku:{}, approvalStatus:{}",
					cancelApprovalDeal.getId(), cancelApprovalDeal.getRecordRowId(), cancelApprovalDeal.getApprovalType(), cancelApprovalDeal.getMerchantId(),
					cancelApprovalDeal.getBu(), cancelApprovalDeal.getStorefrontStoreCode(), cancelApprovalDeal.getProductCode(), cancelApprovalDeal.getSkuCode(), cancelApprovalDeal.getApprovalStatus());

				ApprovalDealStatusEnum originalStatus = cancelApprovalDeal.getApprovalStatus();
				// approvalDeal update status to MERCHANT_CANCELLED
				cancelApprovalDeal.setApprovalStatus(ApprovalDealStatusEnum.MERCHANT_CANCELLED);
				// generate approvalDealStatusHistory
				ApprovalDealStatusHistoryDo approvalDealStatusHistoryDo = ApprovalDealStatusHistoryDo.generateHistoryForUpdateApprovalStatus(cancelApprovalDeal, originalStatus);
				saveApprovalDealHistorys.add(approvalDealStatusHistoryDo);
			});
			approvalDealRepository.saveAll(cancelApprovalDeals);
			// save approvalDealStatusHistory
			approvalDealStatusHistoryRepository.saveAll(saveApprovalDealHistorys);
			// update temp status
			approvalDealTemp.setStatus(ApprovalDealTempStatusEnum.SUCCESS);
			approvalDealTempRepository.save(approvalDealTemp);
		}
	}

	private void createApprovalDeal(ApprovalDealTempDo approvalDealTemp, Map<Long, String> recordRowIdWithUuidMap, SaveProductRecordRowDo editRow, List<CommissionApprovalDealDo> commissionApprovalDealDos) {
		if (approvalDealTemp.getContent() == null) {
			return;
		}
		CommissionApprovalDealDo saveCommissionApprovalDeal = convertJsonToCommissionApprovalDealDo(approvalDealTemp.getContent());
		if (saveCommissionApprovalDeal != null) {
			CommissionApprovalDealDo commissionApprovalDeal = commissionApprovalDealRepository.save(saveCommissionApprovalDeal);
			log.info("create approval deal: approvalDealId:{}, rowId:{}, approvalType:{}, merchantId:{}, bu:{}, storefrontStoreCode:{}, productCode:{}, sku:{}, approvalStatus:{}",
				commissionApprovalDeal.getId(), commissionApprovalDeal.getRecordRowId(), commissionApprovalDeal.getApprovalType(), commissionApprovalDeal.getMerchantId(),
				commissionApprovalDeal.getBu(), commissionApprovalDeal.getStorefrontStoreCode(), commissionApprovalDeal.getProductCode(), commissionApprovalDeal.getSkuCode(), commissionApprovalDeal.getApprovalStatus());

			ApprovalDealStatusHistoryDo approvalDealStatusHistoryDo = ApprovalDealStatusHistoryDo.generateHistoryForCreateApproval(commissionApprovalDeal);
			// save approvalDealStatusHistory
			approvalDealStatusHistoryRepository.save(approvalDealStatusHistoryDo);
			// update temp status
			approvalDealTemp.setStatus(ApprovalDealTempStatusEnum.SUCCESS);
			approvalDealTempRepository.save(approvalDealTemp);

			commissionApprovalDealDos.add(commissionApprovalDeal);
			recordRowIdWithUuidMap.putIfAbsent(editRow.getId(), editRow.getUuid());
		}
	}

	public SaveProductRecordRowDo convertWaitingApprovalFieldsFromOriginalValue(SaveProductRecordRowDo editRecordRow, ProductMasterResultDto originalProduct) {
		if (editRecordRow == null) return editRecordRow;
		SingleEditProductDto singleEditProductDto = gson.fromJson(editRecordRow.getContent(), SingleEditProductDto.class);
		ProductMasterDto editProduct = singleEditProductDto.getProduct();
		if (approvalDealHelper.isAnyHktvDataNull(editProduct, originalProduct)) {
			return editRecordRow;
		}
		editProduct.getAdditional().getHktv().setProductTypeCode(originalProduct.getAdditional().getHktv().getProductTypeCode());
		editProduct.getAdditional().getHktv().setPrimaryCategoryCode(originalProduct.getAdditional().getHktv().getPrimaryCategoryCode());
		editProduct.setBrandId(originalProduct.getBrandId());
		editProduct.getAdditional().getHktv().setCommissionRate(originalProduct.getAdditional().getHktv().getCommissionRate());
		editProduct.setPackingBoxType(originalProduct.getPackingBoxType());

		singleEditProductDto.setProduct(editProduct);
		editRecordRow.setContent(gson.toJson(singleEditProductDto));
		return editRecordRow;
	}

	public SaveProductRecordRowDo convertWaitingApprovalFieldsToEditValue(SaveProductRecordRowDo editRecordRow, ApprovalDealTempDo waitingApprovalTemp) {
		if (editRecordRow == null || waitingApprovalTemp == null) {
			return editRecordRow;
		}
		SingleEditProductDto singleEditProductDto = gson.fromJson(editRecordRow.getContent(), SingleEditProductDto.class);
		ProductMasterDto editProduct = singleEditProductDto.getProduct();
		if (approvalDealHelper.isAnyHktvDataNull(editProduct)) {
			return editRecordRow;
		}
		CommissionApprovalDealDo commissionApprovalDealDo = convertJsonToCommissionApprovalDealDo(waitingApprovalTemp.getContent());
		if (commissionApprovalDealDo == null) {
			return editRecordRow;
		}
		editProduct.getAdditional().getHktv().setProductTypeCode(commissionApprovalDealDo.getProductTypeCode());
		editProduct.getAdditional().getHktv().setPrimaryCategoryCode(commissionApprovalDealDo.getPrimaryCategoryCode());
		editProduct.setBrandId(commissionApprovalDealDo.getBrandId());
		editProduct.getAdditional().getHktv().setCommissionRate(commissionApprovalDealDo.getCommissionRate());
		editProduct.setPackingBoxType(commissionApprovalDealDo.getPackingBoxType());
		singleEditProductDto.setProduct(editProduct);
		editRecordRow.setContent(gson.toJson(singleEditProductDto));
		return editRecordRow;
	}

	private CommissionApprovalDealPrepareData prepareCommissionApprovalDealData(UserDto userDto, BatchProductAsyncDto batchProductAsyncDto) {
		List<ProductMasterDto> editProducts = new ArrayList<>();
		List<String> editUuids = new ArrayList<>();
		List<String> skuCodes = new ArrayList<>();
		Set<String> storefrontStoreCodes = new HashSet<>();
		Set<String> productCodes = new HashSet<>();
		// batch edit only one store
		String storefrontStoreCode = findStorefrontStoreCodeByStoreCode(batchProductAsyncDto.getProductList().get(0));
		storefrontStoreCodes.add(storefrontStoreCode);
		for (SingleEditProductDto singleEditProductDto : batchProductAsyncDto.getProductList()) {
			ProductMasterDto product = singleEditProductDto.getProduct();
			editProducts.add(product);
			editUuids.add(product.getUuid());
			skuCodes.add(product.getSkuId());
			productCodes.add(product.getProductId());
		}

		List<ProductMasterResultDto> allOriginalProducts = productMasterHelper.requestProductsByUuid(userDto, ProductMasterSearchRequestDto.builder().uuids(editUuids).build());
		if (CollectionUtil.isEmpty(allOriginalProducts)) {
			return null;
		}
		final BuCodeEnum defaultBuCode = BuCodeEnum.HKTV;
		final ApprovalDealTypeEnum approvalDealType = ApprovalDealTypeEnum.COMMISSION_RATE;
		Map<String, ProductMasterResultDto> allOriginalProductsUuidMap = allOriginalProducts.stream().collect(Collectors.toMap(ProductMasterResultDto::getUuid, Function.identity()));
		List<ApprovalDealIdAndSkuCodeViewDo> approvalDeals = approvalDealRepository.findAllByBuAndStorefrontStoreCodeAndSkuCodeAndApprovalTypeAndStatus(
			defaultBuCode.name(), storefrontStoreCode, skuCodes, approvalDealType.name(), ApprovalDealStatusEnum.PENDING_APPROVALS_STATUS_SET);
		Map<String, Long> approvalDealsSkuCodeMap = approvalDeals.stream().collect(Collectors.toMap(ApprovalDealIdAndSkuCodeViewDo::getSkuCode, ApprovalDealIdAndSkuCodeViewDo::getId, (existing, replacement) -> replacement));

		// batch edit service checking success row
		Map<String, SaveProductRecordRowDo> editRowUuidMap = saveProductRecordRowRepository.findByRecordIdAndStatus(batchProductAsyncDto.getSaveProductRecordDo().getId(), SaveProductStatus.PROCESSING)
			.stream().collect(Collectors.toMap(SaveProductRecordRowDo::getUuid, Function.identity()));

		Map<String, ApprovalDealIdAndVariantViewDo> primarySkuAndVariantSkuMap = approvalDealHelper.getPrimarySkuAndVariantSkuMap(defaultBuCode, storefrontStoreCodes, productCodes, approvalDealType);
		return CommissionApprovalDealPrepareData.builder()
			.editProducts(editProducts)
			.editRowUuidMap(editRowUuidMap)
			.allOriginalProductsUuidMap(allOriginalProductsUuidMap)
			.primarySkuAndVariantSkuMap(primarySkuAndVariantSkuMap)
			.approvalDealsSkuCodeMap(approvalDealsSkuCodeMap)
			.storefrontStoreCode(storefrontStoreCode)
			.build();
	}

	private void processBatchEditCancelApprovalWithVariant(
		Map<String, List<ApprovalDealTempDo>> cancelApprovalMap,
		Map<String, Set<SaveProductRecordRowDo>> cancelPrimarySkuRowMap,
		List<ApprovalDealTempDo> needToSaveApprovalDealTemps
	) {
		for (Map.Entry<String, List<ApprovalDealTempDo>> entry : cancelApprovalMap.entrySet()) {
			String key = entry.getKey();
			List<ApprovalDealTempDo> approvalDealTemps = entry.getValue();

			// If a duplicate variant SKU has isPrimarySku set to 'Y' and the brand is modified,
			// these row will fail. Skip saving approvalDealTemp for this variant.
			Set<SaveProductRecordRowDo> duplicateRows = cancelPrimarySkuRowMap.get(key);
			if (duplicateRows != null && duplicateRows.size() > 1) {
				log.error("duplicate primarySku, storefrontStoreCodeAndProductCode = {}", key);
				continue;
			}
			// if hava primary sku cancel, only save this approvalDealTemp, it hava find other variant approvalId
			Optional<ApprovalDealTempDo> variantApproval = approvalDealTemps.stream()
				.filter(temp -> StringUtils.isNotBlank(temp.getApprovalDealIdVariant()))
				.findFirst();
			if (variantApproval.isPresent()) {
				needToSaveApprovalDealTemps.add(variantApproval.get());
			} else {
				needToSaveApprovalDealTemps.addAll(approvalDealTemps);
			}
		}
	}

	private String findStorefrontStoreCodeByStoreCode(SingleEditProductDto editProductDto) {
		if (editProductDto == null || editProductDto.getProduct() == null || editProductDto.getProduct().getAdditional() == null || editProductDto.getProduct().getAdditional().getHktv() == null) {
			return null;
		}
		String storeCode = editProductDto.getProduct().getAdditional().getHktv().getStores();
		Optional<StoreDo> storeDo = storeRepository.findHktvStoreByStoreCode(storeCode);
		return storeDo.map(StoreDo::getStorefrontStoreCode).orElseThrow(() -> new SystemI18nException("message311", storeCode));
	}

	private boolean isNeedToCreateCommissionApproval(boolean isPrimaryCategoryOrBrandModified, boolean isCommissionRateSmallThanOriginal) {
		return isPrimaryCategoryOrBrandModified && isCommissionRateSmallThanOriginal;
	}

	private boolean isNormalEditProcess(CheckCommissionRateApprovalDto checkDto, boolean isApprovalRecordEmpty, boolean isCommissionRateSmallThanOriginal) {
		return (!checkDto.isPrimaryCategoryOrBrandModified() && !checkDto.isProductReadyMethodModified() && !checkDto.isPackingBoxTypeModified())
			|| (!checkDto.isPrimaryCategoryOrBrandModified() && isApprovalRecordEmpty)
			|| (checkDto.isPrimaryCategoryOrBrandModified() && isApprovalRecordEmpty && !isCommissionRateSmallThanOriginal);
	}

	private boolean isNeedToCancelCommissionApproval(CheckCommissionRateApprovalDto checkDto, boolean hasApprovalRecord) {
		return hasApprovalRecord &&
			(checkDto.isPrimaryCategoryOrBrandModified() || checkDto.isProductReadyMethodModified() || checkDto.isPackingBoxTypeModified());
	}

	public CheckCommissionRateDto isCommissionRateSmallThanOriginal(ProductMasterDto editProduct) {
		Optional<ProductStoreStatusDo> originalProductStoreStatus = productStoreStatusRepository.findBySkuUuid(editProduct.getUuid());
		Integer storeId = originalProductStoreStatus.map(ProductStoreStatusDo::getStoreId).orElse(null);
		HktvProductDto editHktvProduct = editProduct.getAdditional().getHktv();
		ContractProdTermsDo newContractTerms =
			saveProductHelper.findContractProductTerm(editHktvProduct.getContractNo(), storeId,
				editHktvProduct.getPrimaryCategoryCode(), editHktvProduct.getProductReadyMethod(), editProduct.getBrandId(), editProduct.getSkuId(), editHktvProduct.getTermName());
		if (newContractTerms == null) {
			log.error("calculate new contractProductTerm is null");
			return CheckCommissionRateDto.builder().errorMessage(List.of(messageSource.getMessage("message12", null, null))).build();
		}
		// if original commission rate not found, no need to check approve, use new commission rate edit
		if (originalProductStoreStatus.isEmpty()
			|| originalProductStoreStatus.get().getCommissionRate() == null
			|| Objects.equals(originalProductStoreStatus.get().getStatus(), ProductContractMatchStatusEnum.NO_MATCH.getCode())) {
			log.error("originalProductStoreStatus or commissionRate is null or status no match, uuid = {}", editProduct.getUuid());
			return CheckCommissionRateDto.builder()
				.isSmallThanOriginal(true)
				.originalProductStoreStatus(originalProductStoreStatus.orElse(null))
				.newCommissionRate(newContractTerms.getCommissionRate())
				.newContractProdTermsId(newContractTerms.getId())
				.build();
		}
		boolean isSmallThanOriginal = newContractTerms.getCommissionRate().compareTo(originalProductStoreStatus.get().getCommissionRate()) < 0;
		log.info("sku:{}, new CommissionRate:{}, original CommissionRate:{}", editProduct.getSkuId(), newContractTerms.getCommissionRate(), originalProductStoreStatus.get().getCommissionRate());
		return CheckCommissionRateDto.builder()
			.isSmallThanOriginal(isSmallThanOriginal)
			.originalProductStoreStatus(originalProductStoreStatus.get())
			.newCommissionRate(newContractTerms.getCommissionRate())
			.newContractProdTermsId(newContractTerms.getId())
			.build();
	}

	private String getCommissionApprovalDealTempContent(ProductMasterDto editProduct, ProductMasterResultDto originalProduct, String userCode, Long recordRowId, BuCodeEnum buCode, CheckCommissionRateDto checkCommissionRateResult) {
		CommissionApprovalDealGenerateData generateData = CommissionApprovalDealGenerateData.builder()
			.userCode(userCode)
			.merchantId(editProduct.getMerchantId())
			.recordRowId(recordRowId)
			.buCode(buCode)
			.editProduct(editProduct)
			.originalProduct(originalProduct)
			.newCommissionRate(checkCommissionRateResult.getNewCommissionRate())
			.newContractProdTermsId(checkCommissionRateResult.getNewContractProdTermsId())
			.originalProductStoreStatus(checkCommissionRateResult.getOriginalProductStoreStatus())
			.build();
		CommissionApprovalDealDo tempContent = CommissionApprovalDealDo.generateCommissionApprovalDealDo(generateData);
		checkNonNullFieldsCannotBeNull(tempContent);
		String content = null;
		try {
			content = objectMapper.writeValueAsString(tempContent);
		} catch (JsonProcessingException e) {
			log.error("commissionApprovalDealTempContent is parse error", e);
		}
		return content;
	}

	public CommissionApprovalDealDo convertJsonToCommissionApprovalDealDo(String json) {
		CommissionApprovalDealDo commissionApprovalDealDo = null;
		if (StringUtils.isBlank(json)) {
			return commissionApprovalDealDo;
		}
		try {
			commissionApprovalDealDo = objectMapper.readValue(json, CommissionApprovalDealDo.class);
		} catch (JsonProcessingException e) {
			log.error("CommissionApprovalDealDo json parse error", e);
		}
		return commissionApprovalDealDo;
	}

	private void checkNonNullFieldsCannotBeNull(CommissionApprovalDealDo tempContent) {
		if (tempContent.getPrimaryCategoryCode() == null
			|| CollectionUtil.isEmpty(tempContent.getProductTypeCode())
			|| tempContent.getBrandId() == null
			|| tempContent.getCommissionRate() == null
			|| tempContent.getContractProdTermsId() == null
			|| tempContent.getPackingBoxType() == null
			|| tempContent.getRecordRowId() == null
			|| tempContent.getMerchantId() == null
			|| tempContent.getBu() == null
			|| tempContent.getStorefrontStoreCode() == null
			|| tempContent.getProductCode() == null
			|| tempContent.getSkuCode() == null
			|| tempContent.getSkuName() == null
			|| tempContent.getSkuNameTchi() == null
			|| tempContent.getMerchantName() == null
			|| tempContent.getApprovalType() == null
			|| tempContent.getApprovalStatus() == null
			|| tempContent.getIsPrimarySku() == null
			|| tempContent.getCreatedBy() == null
			|| tempContent.getLastUpdatedBy() == null
		) {
			try {
				log.error("checkNonNullFieldsCannotBeNull CommissionApprovalDealDo = {}", objectMapper.writeValueAsString(tempContent));
			} catch (JsonProcessingException e) {
				log.error("CommissionApprovalDealDo json parse error", e);
			}
			throw new SystemI18nException("message318");
		}

	}


	public List<ApprovalDealSendMessageDto> generateApprovalDealSendMessages(List<CommissionApprovalDealDo> commissionApprovalDealDos, Map<Long, String> recordRowIdWithUuidMap) {
		if (CollectionUtil.isEmpty(commissionApprovalDealDos) || recordRowIdWithUuidMap.isEmpty()) {
			return null;
		}
		return commissionApprovalDealDos.stream().map(commissionApprovalDeal ->
				ApprovalDealSendMessageDto.builder()
					.approvalDealStatusChange(commissionApprovalDeal.getApprovalStatus())
					.receiverUsers(approvalDealStatusHelper.findApprovalDealCreatorAndContractRms(
						commissionApprovalDeal.getMerchantId(),
						commissionApprovalDeal.getStorefrontStoreCode(),
						commissionApprovalDeal.getCreatedBy()))
					.uuid(recordRowIdWithUuidMap.get(commissionApprovalDeal.getRecordRowId()))
					.skuCode(commissionApprovalDeal.getSkuCode())
					.storefrontStoreCode(commissionApprovalDeal.getStorefrontStoreCode())
					.build())
			.collect(Collectors.toList());
	}

}
