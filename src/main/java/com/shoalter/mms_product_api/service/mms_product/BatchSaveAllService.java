package com.shoalter.mms_product_api.service.mms_product;

import com.shoalter.mms_product_api.asynctask.CreateSaveProductRecordTask;
import com.shoalter.mms_product_api.config.product.BatchImportSourceEnum;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.StoreMerchantViewDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreWarehouseRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractTypeDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.mms_product.pojo.MmsBatchSaveProductDto;
import com.shoalter.mms_product_api.service.product.helper.CheckProductHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdByHktvStoreProductMasterRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.ValidationCheckUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class BatchSaveAllService {
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final PermissionHelper permissionHelper;
	private final CheckProductHelper checkProductHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final StoreWarehouseRepository storeWarehouseRepository;
	private final StoreRepository storeRepository;
	private final MessageSource messageSource;
	private final ProductMasterHelper productMasterHelper;
	private final CreateSaveProductRecordTask createSaveProductRecordTask;

	public ResponseDto<Void> start(UserDto userDto, MmsBatchSaveProductDto mmsBatchSaveProductDto, String clientIp) {
		Pair<ResponseDto<Void>, SaveProductRecordDo> responseDto = SpringBeanProvider.getBean(BatchSaveAllService.class).processDataAndSaveProductRecord(userDto, mmsBatchSaveProductDto, clientIp);
		if (responseDto.getLeft().getStatus() == SaveProductStatusEnum.SUCCESS.getCode() && responseDto.getRight() != null) {
			createSaveProductRecordTask.checkPrimarySkuAndAddVariantProductWithSameProductCodeAsync(userDto, responseDto.getRight());
		}
		return responseDto.getLeft();
	}

	@Transactional
	public Pair<ResponseDto<Void>, SaveProductRecordDo> processDataAndSaveProductRecord(UserDto userDto, MmsBatchSaveProductDto mmsBatchSaveProductDto, String clientIp) {
		permissionHelper.checkPermission(userDto, mmsBatchSaveProductDto.getMerchantId());

		// generate all sku list
		List<String> skuList = new ArrayList<>();
		Set<String> stores = new HashSet<>();
		for (SingleEditProductDto product : mmsBatchSaveProductDto.getProducts()) {
			skuList.add(product.getProduct().getSkuId());
			stores.add(product.getProduct().getAdditional().getHktv().getStores());
		}

		// check contract type
		List<String> everutsContractTypeStores = storeRepository.findContractTypeByBuCodesAndStoreCodesOrStorefrontStoreCodes(BuCodeEnum.HKTV.name(), stores).stream()
			.filter(storeContractTypeDo -> ContractType.EVERUTS.equals(storeContractTypeDo.getContractType()))
			.map(StoreContractTypeDo::getStorefrontCode).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(everutsContractTypeStores)) {
			return Pair.of(ResponseDto.fail(List.of(messageSource.getMessage("message325", null, null))), null);
		}

		Map<String, String> storefrontStoreCodeToStoreCodeMap = new HashMap<>();
		for (StoreMerchantViewDo storeMerchantViewDo : storeRepository.findStoreMerchantViewDoByStorefrontStoreCodes(stores)) {
			storefrontStoreCodeToStoreCodeMap.put(storeMerchantViewDo.getStorefrontStoreCode(), storeMerchantViewDo.getStoreCode());
		}

		BatchImportSourceEnum source = mmsBatchSaveProductDto.getSource();
		List<String> existsSkus = new ArrayList<>();
		// check sku Id exists or not
		if (CollectionUtil.isNotEmpty(mmsBatchSaveProductDto.getProducts())
			&& mmsBatchSaveProductDto.getProducts().get(0).getProduct().getAdditional() != null
			&& mmsBatchSaveProductDto.getProducts().get(0).getProduct().getAdditional().getHktv() != null
			&& mmsBatchSaveProductDto.getProducts().get(0).getProduct().getAdditional().getHktv().getStores() != null) {

			String hktvStore = mmsBatchSaveProductDto.getProducts().get(0).getProduct().getAdditional().getHktv().getStores();
			String storesCode = (source != null && BatchImportSourceEnum.BATCH_IMPORT_HKTV_SOURCE_SET.contains(source)) ? storefrontStoreCodeToStoreCodeMap.get(hktvStore) : hktvStore;

			CheckSkuIdByHktvStoreProductMasterRequestDto checkSkuIdDto = new CheckSkuIdByHktvStoreProductMasterRequestDto(storesCode, skuList);
			CheckSkuIdResultDto checkSkuIdResultDto = productMasterHelper.requestCheckSkuIdByHktvStore(userDto, checkSkuIdDto);

			if (checkSkuIdResultDto == null) {
				List<String> errorMessageList = new ArrayList<>();
				log.error("Unable to request product master check sku.");
				errorMessageList.add(messageSource.getMessage("message21", new String[]{ErrorMessageTypeCode.PRODUCT_MASTER_GET_PRODUCTS_PRODUCT_STATUS_ERROR}, null));
				return Pair.of(ResponseDto.fail(errorMessageList), null);
			}

			existsSkus = checkSkuIdResultDto.getExistsSkus();
		}

		SaveProductRecordDo saveProductRecordDo = createProductRecordDo(userDto, mmsBatchSaveProductDto, clientIp);

		Map<String, Long> skuIdCountMap = mmsBatchSaveProductDto.getProducts().stream().collect(Collectors.groupingBy(product -> product.getProduct().getSkuId(), Collectors.counting()));
		Map<String, Integer> storeWarehouseMap = new HashMap<>();
		for (SingleEditProductDto product : mmsBatchSaveProductDto.getProducts()) {
			List<String> errorMessages = new ArrayList<>();
			if (source != null && BatchImportSourceEnum.BATCH_IMPORT_HKTV_SOURCE_SET.contains(source)) {

				// this error message is not null when call hybris create option value fail
				if (StringUtils.isNotEmpty(product.getErrorMessage())) {
					errorMessages.add(product.getErrorMessage());
				}

				String storeWarehouseCode = product.getProduct().getAdditional().getHktv().getWarehouseCode();
				String storefrontStoreCode = product.getProduct().getAdditional().getHktv().getStores();
				String[] warehouseCodeSegment = storeWarehouseCode.split("-");
				//find warehouse no.
				if (!warehouseCodeSegment[0].equals(storefrontStoreCode)) {
					errorMessages.add(messageSource.getMessage("message264", new String[]{"Warehouse"}, null));
				} else if (!storeWarehouseMap.containsKey(storeWarehouseCode)) {
					Integer storeWarehouseId = storeWarehouseRepository.findIdByStorefrontStoreCodeAndHktvBusUnitAndWarehouseSeqNo(warehouseCodeSegment[0], Integer.parseInt(warehouseCodeSegment[1])).orElse(null);
					storeWarehouseMap.put(storeWarehouseCode, storeWarehouseId);
				}

				//convert storefront store code to store code and set warehouse id
				product.getProduct().getAdditional().getHktv().setWarehouseId(storeWarehouseMap.get(storeWarehouseCode));
				if (storefrontStoreCodeToStoreCodeMap.containsKey(storefrontStoreCode)) {
					product.getProduct().getAdditional().getHktv().setStores(storefrontStoreCodeToStoreCodeMap.get(storefrontStoreCode));
				}

				String skuId = product.getProduct().getSkuId();
				if (existsSkus.contains(skuId)) {
					errorMessages.add(messageSource.getMessage("message6", new String[]{skuId}, null));
				}

				if (saveProductRecordDo.getUploadType().equals(SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL)) {
					ValidationCheckUtil.checkObjectFourByteEmoji(errorMessages, product.getProduct().getSkuNameEn(), "SKU Name (EN)");
					ValidationCheckUtil.checkObjectFourByteEmoji(errorMessages, product.getProduct().getSkuNameCh(), "SKU Name (Chi)");
					ValidationCheckUtil.checkObjectFourByteEmoji(errorMessages, product.getProduct().getSkuNameSc(), "SKU Name (SC)");
				}
			}
			if (skuIdCountMap.get(product.getProduct().getSkuId()) > 1) {
				errorMessages.add(messageSource.getMessage("message137", null, null));
			}

			if (!errorMessages.isEmpty()) {
				saveProductRecordRowHelper.createProductRecordRowDo(saveProductRecordDo.getId(), product, SaveProductStatus.FAIL, StringUtils.join(errorMessages, StringUtils.LF));
			} else {
				SaveProductRecordRowDo row = saveProductRecordRowHelper.createProductRecordRowDo(saveProductRecordDo.getId(), product, SaveProductStatus.PROCESSING, null);
				product.getProduct().setRecordRowId(row.getId());
			}
		}
		List<SingleEditProductDto> productList = mmsBatchSaveProductDto.getProducts();
		checkProductHelper.checkBarcodeEnable(productList);
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), mmsBatchSaveProductDto.getProducts().size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));

		return Pair.of(ResponseDto.success(null), saveProductRecordDo);
	}

	private SaveProductRecordDo createProductRecordDo(UserDto userDto, MmsBatchSaveProductDto mmsBatchSaveProductDto, String clientIp) {
		int uploadType = findUploadTypeByImportSource(mmsBatchSaveProductDto.getSource());
		SaveProductRecordDo saveProductRecordDo = new SaveProductRecordDo();
		saveProductRecordDo.setUploadTime(new Date());
		saveProductRecordDo.setMerchantId(mmsBatchSaveProductDto.getMerchantId());
		saveProductRecordDo.setStatus(SaveProductStatus.WAIT_START);
		saveProductRecordDo.setUploadType(uploadType);
		saveProductRecordDo.setUploadUserId(userDto.getUserId());
		saveProductRecordDo.setFileName(mmsBatchSaveProductDto.getFileName());
		saveProductRecordDo.setUserIp(clientIp);
		return saveProductRecordRepository.save(saveProductRecordDo);
	}

	private int findUploadTypeByImportSource(BatchImportSourceEnum sourceEnum) {
		if (sourceEnum == null) {
			return SaveProductType.BATCH_CREATE_PRODUCT;
		}
		switch (sourceEnum) {
			case EXCEL:
				return SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL;
			case TMALL:
				return SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL;
			default:
				return SaveProductType.BATCH_CREATE_PRODUCT;
		}
	}
}
