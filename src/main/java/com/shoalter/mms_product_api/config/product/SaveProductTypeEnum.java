package com.shoalter.mms_product_api.config.product;

public enum SaveProductTypeEnum {
	SINGLE_CREATE_PRODUCT(1, "SINGLE_CREATE_PRODUCT"),
	SINGLE_EDIT_PRODUCT(2, "SINGLE_EDIT_PRODUCT"),
	BATCH_CREATE_PRODUCT(3, "BATCH_CREATE_PRODUCT"),
	BATCH_EDIT_PRODUCT(4, "BATCH_EDIT_PRODUCT"),
	BATCH_EDIT_PRODUCT_COMMONLY_USED(5, "BATCH_EDIT_PRODUCT_COMMONLY_USED"),
	SINGLE_EDIT_PRODUCT_PACKAGING_INFO(7, "SINGLE_EDIT_PRODUCT_PACKAGING_INFO"),
	SYNC_SAME_PRODUCT_CODE(9, "SYNC_SAME_PRODUCT_CODE"),
	BATCH_EDIT_LITTLE_MALL_PRODUCT_COMMONLY_USED(10, "BATCH_EDIT_LITTLE_MALL_PRODUCT_COMMONLY_USED"),
	BATCH_EDIT_STORE_ONLINE_STATUS(12, "BATCH_EDIT_STORE_ONLINE_STATUS"),
	BATCH_EDIT_OVERSEA_DELIVERY(13, "BATCH_EDIT_OVERSEA_DELIVERY"),
	BATCH_EDIT_PRODUCT_PACKAGING_INFO(14, "BATCH_EDIT_PRODUCT_PACKAGING_INFO"),
	SINGLE_CREATE_LITTLE_MALL_PRODUCT(15, "SINGLE_CREATE_LITTLE_MALL_PRODUCT"),
	SINGLE_EDIT_LITTLE_MALL_PRODUCT(16, "SINGLE_EDIT_LITTLE_MALL_PRODUCT"),
	BATCH_CREATE_LITTLE_MALL_PRODUCT(17, "BATCH_CREATE_LITTLE_MALL_PRODUCT"),
	SINGLE_CREATE_BUNDLE(18, "SINGLE_CREATE_BUNDLE"),
	SINGLE_EDIT_BUNDLE(19, "SINGLE_EDIT_BUNDLE"),
	SYNC_OFFLINE_BUNDLE(20, "SYNC_OFFLINE_BUNDLE"),
	BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE(21, "BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE"),
	BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV(22, "BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV"),
	BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL(23, "BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL"),
	BATCH_CREATE_PRODUCT_FROM_TMALL(24, "BATCH_CREATE_PRODUCT_FROM_TMALL"),
	BATCH_EDIT_PRODUCT_FROM_EXCEL(25, "BATCH_EDIT_PRODUCT_FROM_EXCEL"),
	BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION(26, "BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION"),
	BATCH_EDIT_PRODUCT_PRICE(27, "BATCH_EDIT_PRODUCT_PRICE"),
	BATCH_EDIT_PRODUCT_ONLINE_STATUS(28, "BATCH_EDIT_PRODUCT_ONLINE_STATUS"),
	BATCH_EDIT_PRODUCT_VISIBILITY(29, "BATCH_EDIT_PRODUCT_VISIBILITY"),
	SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT(30, "SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT"),
	BATCH_CRATE_BINDING_EXTENDED_WARRANTY(31, "BATCH_CRATE_BINDING_EXTENDED_WARRANTY"),
	BATCH_DELETE_BINDING_EXTENDED_WARRANTY(32, "BATCH_DELETE_BINDING_EXTENDED_WARRANTY"),
	BATCH_EDIT_LITTLE_MALL_PRODUCT(33, "BATCH_EDIT_LITTLE_MALL_PRODUCT"),
	BATCH_EDIT_DEPRECATED_PRODUCT_IMAGES(34, "BATCH_EDIT_DEPRECATED_PRODUCT_IMAGES"),
	BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION(35, "BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION"),
	COMMISSION_RATE_APPROVE(36, "COMMISSION_RATE_APPROVE"),
    BATCH_EDIT_HKTV_PRODUCT_TRANSLATE(37, "BATCH_EDIT_HKTV_PRODUCT_TRANSLATE"),
	THE_PLACE_SWITCH_PRIMARY(38, "THE_PLACE_SWITCH_PRIMARY"),
	LITTLE_MALL_FLATTEN_PRODUCTS(39, "LITTLE_MALL_FLATTEN_PRODUCTS"),
	BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB(40, "BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB"),
	BATCH_EDIT_PRODUCT_READY_DAYS(41, "BATCH_EDIT_PRODUCT_READY_DAYS"),
	BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL(42, "BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL"),
	;
	private final int code;
	private final String name;

	SaveProductTypeEnum(int code, String name) {
		this.code = code;
		this.name = name;
	}

	public int getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public static String getProductTypeName(int code) {
		for (SaveProductTypeEnum type : values()) {
			if (type.getCode() == code) {
				return type.getName();
			}
		}
		return "UNKNOWN_SAVE_PRODUCT_TYPE";
	}
}
