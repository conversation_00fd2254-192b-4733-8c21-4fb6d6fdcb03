package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.template.EditProductCommonlyUsedTemplateEnum;
import com.shoalter.mms_product_api.dao.mapper.product.ProductMapper;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.EditProductCommonlyUsedTemplateHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.pojo.EditProductCommonlyUsedInfoDto;
import com.shoalter.mms_product_api.util.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.hibernate.service.spi.ServiceException;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class EditProductCommonlyUsedTemplateService extends AbstractReport implements ITemplateService {

    private final ProductMapper productMapper;
    public final PermissionHelper permissionHelper;
    public final EditProductCommonlyUsedTemplateHelper editProductCommonlyUsedTemplateHelper;

    public HttpEntity<ByteArrayResource> start(List<String> skuList, UserDto userDto, Integer merchantId, Integer contractId) throws ServiceException {
        permissionHelper.checkPermission(userDto, merchantId);

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        Workbook workbook = editProductCommonlyUsedTemplateHelper.start();
        addBodyColumn(workbook, skuList, merchantId, contractId);
        try {
            workbook.write(os);
            workbook.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return new ResponseEntity<>(new ByteArrayResource(os.toByteArray()), getResponseHeader(), HttpStatus.OK);
    }

    private void addBodyColumn(Workbook workbook, List<String> skuList, Integer merchantId, Integer contractId) {
        List<EditProductCommonlyUsedInfoDto> dataMap = productMapper.findCommonlyUsedTemplateData(skuList, merchantId, contractId);
        int rowNum = 1;
        Sheet dataSheet = workbook.getSheet(EditProductCommonlyUsedTemplateHelper.SHEET_NAME);
        CellStyle bodyStyle = ExcelUtil.createBodyStyle(workbook, HorizontalAlignment.CENTER, false, false, false, false, true);

		Map<String,String> validationMap = new HashMap<>();
		validationMap.put("Y","Y : Yes");
		validationMap.put("N","N : No");

        for (EditProductCommonlyUsedInfoDto data : dataMap) {
            setCellValue(dataSheet, bodyStyle, rowNum, EditProductCommonlyUsedTemplateEnum.SKU_CODE.getColumnNumber(), data.getSkuCode());
            setCellValue(dataSheet, bodyStyle, rowNum, EditProductCommonlyUsedTemplateEnum.PRICE.getColumnNumber(), String.valueOf(data.getPrice()));
            setCellValue(dataSheet, bodyStyle, rowNum, EditProductCommonlyUsedTemplateEnum.INVISIBLE.getColumnNumber(), validationMap.get(data.getInvisible()));
            setCellValue(dataSheet, bodyStyle, rowNum, EditProductCommonlyUsedTemplateEnum.ONLINE.getColumnNumber(), data.getOnline().name());
            rowNum++;
        }
    }

    @Override
    public String getFileName() {
        return "Edit_Product_Commonly_Used_Template";
    }
}
