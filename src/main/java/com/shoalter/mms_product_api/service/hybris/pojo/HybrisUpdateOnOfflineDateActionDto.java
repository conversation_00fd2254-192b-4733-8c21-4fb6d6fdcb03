package com.shoalter.mms_product_api.service.hybris.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class HybrisUpdateOnOfflineDateActionDto extends HybrisUpdateProductMainMqMessage {

	private String onlineDate;

	private String offlineDate;
}
