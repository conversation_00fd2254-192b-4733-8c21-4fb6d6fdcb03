package com.shoalter.mms_product_api.dao.repository.product.pojo;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;
@Entity
@Table(name = "PRODUCT_TEMP_ATTRIBUTES")
@Data
public class ProductTempAttributesDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition = "INT")
    private Integer id;
    @Column(name = "CREATED_BY")
    private String createdBy;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "LAST_UPDATED_BY")
    private String lastUpdatedBy;
    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;
    @Column(name = "GOODS_TYPE")
    private String goodsType;
    @Column(name = "WARRANTY_PERIOD_UNIT")
    private String warrantyPeriodUnit;
    @Column(name = "WARRANTY_PERIOD")
    private Integer warrantyPeriod;
    @Column(name = "WARRANTY_SUPPLIER_CH")
    private String warrantySupplierCh;
    @Column(name = "WARRANTY_SUPPLIER_EN")
    private String warrantySupplierEn;
    @Column(name = "SERVICE_CENTRE_ADDRESS_CH")
    private String serviceCentreAddressCh;
    @Column(name = "SERVICE_CENTRE_ADDRESS_EN")
    private String serviceCentreAddressEn;
    @Column(name = "SERVICE_CENTRE_EMAIL")
    private String serviceCentreEmail;
    @Column(name = "SERVICE_CENTRE_CONTACT")
    private String serviceCentreContact;
    @Column(name = "WARRANTY_REMARK")
    private String warrantyRemark;
    @Column(name = "WARRANTY_REMARK_EN")
    private String warrantyRemarkEn;
    @Column(name = "WARRANTY_REMARK_CH")
    private String warrantyRemarkCh;
    @Column(name = "HKTVMALL_PRODUCT")
    private String hktvmallProduct;
    @Column(name = "HKTVMALL_DEEPLINK")
    private String hktvmallDeeplink;
    @Column(name = "HKTVMALL_ID")
    private String hktvmallId;
    @Column(name = "STORE_WAREHOUSE_ID")
    private Integer storeWarehouseId;
    @Column(name = "VOUCHER_TEMPLATE_TYPE")
    private String voucherTemplateType;
    @Column(name = "MINIMUM_SHELF_LIFE")
    private Integer minimumShelfLife;
    @Column(name = "VIRTUAL_STORE")
    private String virtualStore;
    @Column(name = "RM_CODE")
    private String rmCode;
    @Column(name = "STORAGE_TYPE")
    private String storageType;
    @Column(name = "PRE_SELL_FRUIT")
    private String preSellFruit;
    @Column(name = "PHYSICAL_STORE")
    private String physicalStore;
}

