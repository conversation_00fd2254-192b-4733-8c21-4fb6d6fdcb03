package com.shoalter.mms_product_api.service.gateway;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.exception.RetryException;
import com.shoalter.mms_product_api.helper.HttpRequestHelper;
import com.shoalter.mms_product_api.helper.pojo.HttpRequestDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.MmsgwApiResultDto;
import com.shoalter.mms_product_api.service.product.pojo.MmsgwRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.MmsgwResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.MmsgwStorefrontRequestDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.EncryptUtil;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.retry.RetryContext;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.stereotype.Service;

import java.security.PrivateKey;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class GatewayHelper {
	private static final int MAX_ATTEMPTS = 3;
	private static final String HYBRIS_ERROR_RESPONSE_IMAGE_DUPLICATE_KEYWORD = "正在使用相同的键";

	@Value("${gateway.service.key}")
	private String gatewayServiceKey;
	@Value("${gateway.service.url}")
	private String gatewayServiceUrl;
	@Value("${gateway.service.name}")
	private String gatewayServiceName;
	@Value("${gateway.service.method}")
	private String gatewayServiceMethod;

	private final HttpRequestHelper httpRequestHelper;
	private final Gson gson;

//  MS-8141 ,Comment the retry method
//	@Retryable(
//		value = {RetryException.class},
//		maxAttempts = MAX_ATTEMPTS,
//		backoff = @Backoff(multiplier = 2, delay = 200)
//	)
	public MmsgwResponseDto requestMmsGateway(
		UserDto userDto, List<MmsgwStorefrontRequestDto> mmsgwStorefrontRequestDto, boolean oapi) {
		String url = gatewayServiceUrl + gatewayServiceName;
		String method = gatewayServiceMethod;
		MmsgwRequestDto requestDto = new MmsgwRequestDto();
		requestDto.setSystem("MMS");
		requestDto.setCallBy(userDto.getUserCode());
		requestDto.setRequest(mmsgwStorefrontRequestDto);
		MmsgwResponseDto mmsgwResponseDto;

		try {
			mmsgwResponseDto = httpRequestHelper.requestForBody(HttpRequestDto.<MmsgwRequestDto, MmsgwResponseDto>builder()
				.serviceName(mmsgwStorefrontRequestDto.get(0).getRequestSystem())
				.url(url)
				.method(HttpMethod.resolve(method))
				.customHeaders(generateGatewayHeader(oapi))
				.resultClass(MmsgwResponseDto.class)
				.body(requestDto)
				.user(userDto)
				.build());

		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		}
//      MS-8141 ,Comment the retry method
//		RetryContext ctx = RetrySynchronizationManager.getContext();
//		if (isHybrisDuplicateRetry(mmsgwResponseDto, ctx)) {
//			throw new RetryException("Hybris API retry failed:" + HYBRIS_ERROR_RESPONSE_IMAGE_DUPLICATE_KEYWORD);
//		}
		return mmsgwResponseDto;
	}

	private boolean isHybrisDuplicateRetry(MmsgwResponseDto mmsgwResponseDto, RetryContext ctx) {
		if (mmsgwResponseDto == null || CollectionUtil.isEmpty(mmsgwResponseDto.getResponse())) {
			return false;
		}
		int retryCount = (ctx == null ? 0 : ctx.getRetryCount());
		boolean isNeedRetry = retryCount < (MAX_ATTEMPTS - 1);
		boolean isHybrisDuplicateRetry = false;
		MmsgwApiResultDto apiResultDto = new MmsgwApiResultDto();
		if (isNeedRetry && CollectionUtil.isNotEmpty(mmsgwResponseDto.getResponse()) && mmsgwResponseDto.getResponse().get(0).getResponseData() != null) {
			apiResultDto = gson.fromJson(mmsgwResponseDto.getResponse().get(0).getResponseData(), MmsgwApiResultDto.class);
			if (apiResultDto != null && StringUtils.equals(apiResultDto.getStatus(), ConstantType.ERROR_RESPONSE_FAILED)
				&& apiResultDto.getResult().contains(HYBRIS_ERROR_RESPONSE_IMAGE_DUPLICATE_KEYWORD)) {
				isHybrisDuplicateRetry = true;
			}
		}

		if (isHybrisDuplicateRetry) {
			log.warn("Hybris API retry failed: {}, Retry count: {}, error: {}", HYBRIS_ERROR_RESPONSE_IMAGE_DUPLICATE_KEYWORD, retryCount, apiResultDto.getResult());
		}
		return isHybrisDuplicateRetry;
	}

	private HttpHeaders generateGatewayHeader(boolean oapi) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		HashMap<String, Object> claims = new HashMap<>();
		claims.put("sub", ConstantType.PLATFORM_CODE_LOWER_CASE_HKTV);
		claims.put("iat", new Date());
		String jwtToken = generateGatewayJwtToken(claims, gatewayServiceKey);
		headers.add("Server-Token", jwtToken);
		if (oapi) {
			headers.add("oapi", "true");
		}
		return headers;
	}

	private String generateGatewayJwtToken(HashMap<String, Object> claims, String privateKey) {
		JwtBuilder jwtBuilder = Jwts.builder();
		PrivateKey key = EncryptUtil.getPrivateKey(privateKey, "RSA");
		return jwtBuilder
			.setHeaderParam("alg", "RS256")
			.setHeaderParam("typ", "JWT")
			.setClaims(claims)
			.signWith(SignatureAlgorithm.RS256, key).compact();
	}
}
