package com.shoalter.mms_product_api.dao.repository.approval_deal.pojo;

import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.convert.StringListConverter;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreStatusDo;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealStatusEnum;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealTypeEnum;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ProductHistoryContentDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductStatusDto;
import com.vladmihalcea.hibernate.type.json.JsonType;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Entity
@Table(name = "COMMISSION_APPROVAL_DEAL")
@Setter
@Getter
@TypeDef(name = "json", typeClass = JsonType.class)
@Slf4j
public class CommissionApprovalDealDo extends ApprovalDealDo {

	@Column(name = "PRODUCT_HISTORY_CONTENT")
	@Type(type = "json")
	private ProductHistoryContentDto productHistoryContent;

	@Column(name = "PRIMARY_CATEGORY_CODE")
	private String primaryCategoryCode;

	@Column(name = "PRODUCT_TYPE_CODE")
	@Convert(converter = StringListConverter.class)
	private List<String> productTypeCode;

	@Column(name = "BRAND_ID")
	private Integer brandId;

	@Column(name = "COMMISSION_RATE")
	private BigDecimal commissionRate;

	@Column(name = "CONTRACT_PROD_TERMS_ID")
	private Integer contractProdTermsId;

	@Column(name = "PACKING_BOX_TYPE")
	private String packingBoxType;

	public static CommissionApprovalDealDo generateCommissionApprovalDealDo(CommissionApprovalDealGenerateData generateData) {
		ProductMasterDto editProduct = generateData.getEditProduct();
		CommissionApprovalDealDo commissionApprovalDeal = new CommissionApprovalDealDo();
		// ApprovalDealDo
		commissionApprovalDeal.setRecordRowId(generateData.getRecordRowId());
		commissionApprovalDeal.setMerchantId(generateData.getMerchantId());
		commissionApprovalDeal.setBu(generateData.getBuCode());
		commissionApprovalDeal.setStorefrontStoreCode(editProduct.getAdditional().getHktv().getStorefrontStoreCode());
		commissionApprovalDeal.setProductCode(editProduct.getProductId());
		commissionApprovalDeal.setSkuCode(editProduct.getSkuId());
		commissionApprovalDeal.setSkuName(editProduct.getSkuNameEn());
		commissionApprovalDeal.setSkuNameTchi(editProduct.getSkuNameCh());
		commissionApprovalDeal.setMerchantName(generateData.getOriginalProduct().getMerchantName());
		commissionApprovalDeal.setApprovalType(ApprovalDealTypeEnum.COMMISSION_RATE);
		commissionApprovalDeal.setApprovalStatus(ApprovalDealStatusEnum.MERCHANT_SUBMITTED);
		commissionApprovalDeal.setIsPrimarySku(ConstantType.CONSTANT_YES.equals(editProduct.getAdditional().getHktv().getIsPrimarySku()));
		commissionApprovalDeal.setCreatedBy(generateData.getUserCode());
		commissionApprovalDeal.setLastUpdatedBy(generateData.getUserCode());

		// CommissionApprovalDealDo
		ProductHistoryContentDto productHistoryContentDto = ProductHistoryContentDto.generateProductHistoryContent(generateData.getBuCode(), editProduct, generateData.getOriginalProduct(), generateData.getOriginalProductStoreStatus());
		commissionApprovalDeal.setProductHistoryContent(productHistoryContentDto);
		commissionApprovalDeal.setPrimaryCategoryCode(editProduct.getAdditional().getHktv().getPrimaryCategoryCode());
		commissionApprovalDeal.setProductTypeCode(editProduct.getAdditional().getHktv().getProductTypeCode());
		commissionApprovalDeal.setBrandId(editProduct.getBrandId());
		commissionApprovalDeal.setCommissionRate(generateData.getNewCommissionRate());
		commissionApprovalDeal.setContractProdTermsId(generateData.getNewContractProdTermsId());
		commissionApprovalDeal.setPackingBoxType(editProduct.getPackingBoxType());
		return commissionApprovalDeal;
	}
}
