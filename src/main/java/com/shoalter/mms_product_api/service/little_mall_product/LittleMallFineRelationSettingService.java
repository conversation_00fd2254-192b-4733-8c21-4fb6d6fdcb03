package com.shoalter.mms_product_api.service.little_mall_product;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.little_mall_product.pojo.request.LittleMallFindRelationSettingMainRequestData;
import com.shoalter.mms_product_api.service.little_mall_product.pojo.response.LittleMallFindRelationSettingMainResponseData;
import com.shoalter.mms_product_api.service.little_mall_product.pojo.response.LittleMallFindRelationSettingResponseData;
import com.shoalter.mms_product_api.service.little_mall_product.pojo.response.ProductFieldCategroyResponseData;
import com.shoalter.mms_product_api.service.little_mall_product.pojo.response.ProductFieldOptionResponseData;
import com.shoalter.mms_product_api.service.little_mall_product.pojo.response.ProductFieldResponseData;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.StoreApiHelper;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.request.ProductMasterRelationSettingRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterProductFieldCategoryResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterProductFieldOptionResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterProductFieldResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterRelationSettingResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.request.StoreApiSearchProductFieldOptionsRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.response.StorePidsResponseData;
import com.shoalter.mms_product_api.service.product.pojo.response.StoreSearchProductFieldOptionsMainResponseData;
import com.shoalter.mms_product_api.service.product.pojo.response.StoreSidsResponseData;
import com.shoalter.mms_product_api.service.product.pojo.response.StoreVidsResponseData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class LittleMallFineRelationSettingService {

	private final ProductMasterHelper productMasterHelper;
	private final StoreApiHelper storeApiHelper;

	public ResponseDto<LittleMallFindRelationSettingMainResponseData> start(UserDto userDto, LittleMallFindRelationSettingMainRequestData littleMallFindRelationSettingMainRequestData) {

		if (littleMallFindRelationSettingMainRequestData.getRelations() == null || littleMallFindRelationSettingMainRequestData.getRelations().isEmpty()) {
			return ResponseDto.success(null);
		}

		List<ProductMasterRelationSettingRequestDto> productMasterRequestList = generateProductMasterRelationSettingRequest(littleMallFindRelationSettingMainRequestData);
		List<ProductMasterRelationSettingResponseDto> productMasterResponseList = productMasterHelper.requestLittleMallRelationSettingByParams(userDto, productMasterRequestList);

		if (productMasterResponseList == null || productMasterResponseList.isEmpty()) {
			log.info("[LittleMallFineRelationSettingService] product master search variant relations is empty or null. response: {}", productMasterResponseList);
			return ResponseDto.success(null);
		}

		List<LittleMallFindRelationSettingResponseData> relationSettingResponseList = new ArrayList<>();
		productMasterResponseList.forEach(productMasterResponse -> {

			StoreApiSearchProductFieldOptionsRequestDto storeRequest = generateStoreApiSearchProductFieldOptionsRequest(productMasterResponse);
			StoreSearchProductFieldOptionsMainResponseData storeResponse = storeApiHelper.searchProductFieldOptionsApi(userDto, storeRequest);

			if (storeResponse == null) {
				log.info("[LittleMallFineRelationSettingService] store search product field options is empty or null. response: {}", storeResponse);
				return;
			}

			relationSettingResponseList.add(generateLittleMallFindRelationSettingResponse(productMasterResponse, storeResponse));
		});

		return ResponseDto.success(LittleMallFindRelationSettingMainResponseData.generate(relationSettingResponseList));
	}

	private List<ProductMasterRelationSettingRequestDto> generateProductMasterRelationSettingRequest(LittleMallFindRelationSettingMainRequestData mainRequest) {
		return mainRequest.getRelations().stream()
			.map(request -> ProductMasterRelationSettingRequestDto.generate(request.getStorefrontStoreCode(), request.getProductId()))
			.collect(Collectors.toList());
	}

	private StoreApiSearchProductFieldOptionsRequestDto generateStoreApiSearchProductFieldOptionsRequest(ProductMasterRelationSettingResponseDto productMasterResponse) {
		List<ProductMasterProductFieldResponseDto> productFields = Optional.ofNullable(productMasterResponse.getProductField())
			.orElse(Collections.emptyList())
			.stream()
			.filter(Objects::nonNull)
			.collect(Collectors.toList());

		List<Integer> pids = productFields.stream()
			.map(ProductMasterProductFieldResponseDto::getPid)
			.filter(Objects::nonNull)
			.collect(Collectors.toList());

		List<Integer> sids = productFields.stream()
			.flatMap(productField -> Optional.ofNullable(productField.getProductFieldCategory())
				.orElse(Collections.emptyList())
				.stream())
			.map(ProductMasterProductFieldCategoryResponseDto::getSid)
			.collect(Collectors.toList());

		List<Integer> vids = productFields.stream()
			.flatMap(productField -> Optional.ofNullable(productField.getProductFieldOption())
				.orElse(Collections.emptyList())
				.stream())
			.map(ProductMasterProductFieldOptionResponseDto::getVid)
			.collect(Collectors.toList());

		return StoreApiSearchProductFieldOptionsRequestDto.generate(
			BuCodeEnum.LITTLE_MALL.name(),
			productMasterResponse.getStorefrontStoreCode(),
			pids, sids, vids
		);
	}

	private LittleMallFindRelationSettingResponseData generateLittleMallFindRelationSettingResponse(
		ProductMasterRelationSettingResponseDto productMasterResponse,
		StoreSearchProductFieldOptionsMainResponseData storeResponse) {

		LittleMallFindRelationSettingResponseData response = new LittleMallFindRelationSettingResponseData();

		if (productMasterResponse == null || storeResponse == null) {
			return response;
		}

		response.setStorefrontStoreCode(productMasterResponse.getStorefrontStoreCode());
		response.setProductId(productMasterResponse.getProductId());
		response.setEnable(productMasterResponse.getEnable());
		response.setSkusPath(productMasterResponse.getSkusPath());

		Map<Integer, StorePidsResponseData> storePidMap = storeResponse.getPids()
			.stream()
			.collect(Collectors.toMap(StorePidsResponseData::getPid, pid -> pid));

		Map<Integer, StoreSidsResponseData> storeSidMap = storeResponse.getSids()
			.stream()
			.collect(Collectors.toMap(StoreSidsResponseData::getSid, sid -> sid));

		Map<Integer, StoreVidsResponseData> storeVidMap = storeResponse.getVids()
			.stream()
			.collect(Collectors.toMap(StoreVidsResponseData::getVid, vid -> vid));

		List<ProductFieldResponseData> productFieldResponses = productMasterResponse.getProductField().stream()
			.map(productMasterProductField -> {
				ProductFieldResponseData productFieldResponse = new ProductFieldResponseData();
				productFieldResponse.setPid(productMasterProductField.getPid());
				productFieldResponse.setPriority(productMasterProductField.getPriority());

				StorePidsResponseData storePidData = storePidMap.get(productMasterProductField.getPid());
				if (storePidData != null) {
					productFieldResponse.setType(storePidData.getType());
					productFieldResponse.setNameEn(storePidData.getNameEn());
					productFieldResponse.setNameZh(storePidData.getNameZh());
				}

				if (productMasterProductField.getProductFieldCategory() != null) {
					List<ProductFieldCategroyResponseData> categroyResponses = productMasterProductField.getProductFieldCategory()
						.stream()
						.map(productMasterCategroy -> {
							ProductFieldCategroyResponseData categroyResponse = new ProductFieldCategroyResponseData();
							categroyResponse.setSid(productMasterCategroy.getSid());
							categroyResponse.setPid(productMasterCategroy.getPid());
							categroyResponse.setPriority(productMasterCategroy.getPriority());

							StoreSidsResponseData storeSidData = storeSidMap.get(productMasterCategroy.getSid());
							if (storeSidData != null) {
								categroyResponse.setType(storeSidData.getType());
								categroyResponse.setNameEn(storeSidData.getNameEn());
								categroyResponse.setNameZh(storeSidData.getNameZh());
							}

							return categroyResponse;
						})
						.collect(Collectors.toList());

					productFieldResponse.setProductFieldCategory(categroyResponses);
				}

				if (productMasterProductField.getProductFieldOption() != null) {
					List<ProductFieldOptionResponseData> optionResponses = productMasterProductField.getProductFieldOption()
						.stream()
						.map(productMasterOption -> {
							ProductFieldOptionResponseData optionResponse = new ProductFieldOptionResponseData();
							optionResponse.setVid(productMasterOption.getVid());
							optionResponse.setPid(productMasterOption.getPid());
							optionResponse.setSid(productMasterOption.getSid());
							optionResponse.setPriority(productMasterOption.getPriority());

							StoreVidsResponseData storeVidData = storeVidMap.get(productMasterOption.getVid());
							if (storeVidData != null) {
								optionResponse.setType(storeVidData.getType());
								optionResponse.setNameEn(storeVidData.getNameEn());
								optionResponse.setNameZh(storeVidData.getNameZh());
							}

							return optionResponse;
						})
						.collect(Collectors.toList());

					productFieldResponse.setProductFieldOption(optionResponses);
				}

				return productFieldResponse;
			})
			.collect(Collectors.toList());

		response.setProductField(productFieldResponses);

		return response;
	}
}
