package com.shoalter.mms_product_api.config.product.template;

import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import lombok.Getter;

@Getter
public enum EditProductPackingInfoTemplateColumnEnum {
    SKU_ID(0, "SKU ID", null, null, null),
	PACKING_SPEC_ENG(1, "Packing Spec (Eng)", null, null, null),
	PACKING_SPEC_CHI(2, "Packing Spec (Chi)", null, null, null),
	PACKING_SPEC_SC(3, "Packing Spec (SC)", null, null, null),
	PACKING_HEIGHT(4, "Packing Height", null, null, null),
	PACKING_LENGTH(5, "Packing Length", null, null, null),
	PACKING_DEPTH(6, "Packing Depth", null, null, null),
	PACKING_DIMENSION_UNIT(7, "Packing Dimension Unit", ExcelValidationName.VALIDATION_PACK_DIMENSION_UNIT, null, null),
	WEIGHT(8, "Weight", null, null, null),
	WEIGHT_UNIT(9, "Weight Unit", ExcelValidationName.VALIDATION_WEIGHT_UNIT, null, null),
	PACKING_BOX_TYPE(10, "Packing box type", ExcelValidationName.VALIDATION_PACK_BOX_TYPE, null, null),
	CARTON_HEIGHT(11, "Carton Height(mm)", null, null, null),
	CARTON_DEPTH(12, "Carton Depth(mm)", null, null, null),
	CARTON_LENGTH(13, "Carton Length(mm)", null, null, null);

    private final Integer columnNumber;
    private final String colName;
    private final String validationName;
    private final EditProductPackingInfoTemplateColumnEnum parent;
    private final String parentSegment;

    EditProductPackingInfoTemplateColumnEnum(Integer colNum, String colName, String validationName, EditProductPackingInfoTemplateColumnEnum parent, String parentSegment) {
        this.columnNumber = colNum;
        this.colName = colName;
        this.validationName = validationName;
        this.parent = parent;
        this.parentSegment = parentSegment;
    }
}
