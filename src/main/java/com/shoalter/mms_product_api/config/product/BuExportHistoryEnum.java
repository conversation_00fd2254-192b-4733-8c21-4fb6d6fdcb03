package com.shoalter.mms_product_api.config.product;

import lombok.Getter;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;
import java.util.stream.Stream;

@Getter
public enum BuExportHistoryEnum {
	TMALL("Tmall", null),
	HKTV("HKTV", 3),
	LITTLE_MALL("LITTLE_MALL", 3),
	AFFILIATE("Affiliate", null);

	private final String exportHistoryBu;
	private final Integer expiredDay;

	public static final List<String> ALL_BU_LIST = List.of(HKTV.getExportHistoryBu(), LITTLE_MALL.getExportHistoryBu(), TMALL.getExportHistoryBu(), AFFILIATE.getExportHistoryBu());
	public static final List<String> DEFAULT_BU_LIST = List.of(HKTV.getExportHistoryBu(), LITTLE_MALL.getExportHistoryBu());
	public static final List<String> THIRD_PARTY_BU_LIST = List.of(TMALL.getExportHistoryBu(), AFFILIATE.getExportHistoryBu());

	BuExportHistoryEnum(String exportHistoryBu, Integer expiredDay) {
		this.exportHistoryBu = exportHistoryBu;
		this.expiredDay = expiredDay;
	}

}

@Converter(autoApply = true)
class BuExportHistoryEnumConverter implements AttributeConverter<BuExportHistoryEnum, String> {
	@Override
	public String convertToDatabaseColumn(BuExportHistoryEnum attribute) {
		if (attribute == null) {
			return "";
		}
		return attribute.getExportHistoryBu();
	}

	@Override
	public BuExportHistoryEnum convertToEntityAttribute(String dbData) {
		if (dbData == null || "".equals(dbData)) {
			return null;
		}
		return Stream.of(BuExportHistoryEnum.values()).filter(c -> c.getExportHistoryBu().equals(dbData)).findFirst()
			.orElseThrow(IllegalArgumentException::new);
	}
}
