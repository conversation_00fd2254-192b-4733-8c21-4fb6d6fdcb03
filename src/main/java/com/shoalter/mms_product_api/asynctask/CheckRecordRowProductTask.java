package com.shoalter.mms_product_api.asynctask;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.asynctask.dto.CheckRecordRowProductData;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.ProductMasterBusinessUnitType;
import com.shoalter.mms_product_api.config.product.SaveProductSource;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.business.BuProductCategoryRepository;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BuProductCategoryDo;
import com.shoalter.mms_product_api.dao.repository.contract.ContractRepository;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractProdTermsDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealTypeEnum;
import com.shoalter.mms_product_api.service.approval_deal.helper.ApprovalDealHelper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.MembershipPricingEventSetDto;
import com.shoalter.mms_product_api.service.product.helper.BatchEditHelper;
import com.shoalter.mms_product_api.service.product.helper.CheckBuHelper;
import com.shoalter.mms_product_api.service.product.helper.CheckLittleMallProductHelper;
import com.shoalter.mms_product_api.service.product.helper.CheckProductHelper;
import com.shoalter.mms_product_api.service.product.helper.ContractHelper;
import com.shoalter.mms_product_api.service.product.helper.GenerateIIDSDataHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductPreProcessingHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductPriceMonitorProductHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductHelper;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditOverseaDeliveryRequestDetailDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditStoreOnlineStatusDto;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.CartonSizeDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckContractProdTermResultDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallProductSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductBarcodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ThirdPLBUHktvDto;
import com.shoalter.mms_product_api.service.product.pojo.ThirdPLBUInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.ThirdPLProductDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.thirdparty.api.SkuValidateRequestDto;
import com.shoalter.mms_product_api.service.shopline.ProductBatchImportHelper;
import com.shoalter.mms_product_api.util.BatchOverseaDeliveryUtil;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class CheckRecordRowProductTask {

	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final StoreRepository storeRepository;
	private final BuProductCategoryRepository buProductCategoryRepository;
	private final ContractRepository contractRepository;

	private final CheckProductHelper checkProductHelper;
	private final ProductMasterHelper productMasterHelper;
	private final PermissionHelper permissionHelper;
	private final ContractHelper contractHelper;
	private final ProductBatchImportHelper productBatchImportHelper;
	private final CheckLittleMallProductHelper checkLittleMallProductHelper;
	private final ProductPreProcessingHelper productPreProcessingHelper;
	private final GenerateIIDSDataHelper generateIIDSDataHelper;
	private final CheckBuHelper checkBuHelper;
	private final BatchEditHelper batchEditHelper;
	private final SaveProductHelper saveProductHelper;
	private final ApprovalDealHelper approvalDealHelper;
	private final ProductPriceMonitorProductHelper productPriceMonitorProductHelper;

	private final Gson gson;
	private final MessageSource messageSource;

	@Async("checkRecordRowProductTaskExecutor")
	public CompletableFuture<Boolean> startWithCheckRecordRowProductTaskExecutor(SaveProductRecordDo productRecord, SaveProductRecordRowDo row, CheckRecordRowProductData checkRowData) {
		return start(productRecord, row, checkRowData);
	}

	@Async("checkQueueProtocolRecordRowProductTaskExecutor")
	public CompletableFuture<Boolean> startWithCheckQueueProtocolRecordRowProductTaskExecutor(SaveProductRecordDo productRecord, SaveProductRecordRowDo row, CheckRecordRowProductData checkRowData) {
		return start(productRecord, row, checkRowData);
	}

	private CompletableFuture<Boolean> start(SaveProductRecordDo productRecord, SaveProductRecordRowDo row, CheckRecordRowProductData checkRowData) {
		try {
			log.info("start check record row id: {}, skuCode: {}, product record id: {}", row.getId(), row.getSku(), productRecord.getId());

			ProductDo beforeProductFromDb = row.getUuid() == null ? null : checkRowData.getProductFromDbMap().get(row.getUuid());
			ProductMasterResultDto beforeProductFromProductMaster = getOldProductByProductMaster(checkRowData.getUserDto(), productRecord.getUploadType(), row, checkRowData.getProductFromPmMap());
			MembershipPricingEventSetDto checkPricingResult = row.getUuid() == null ? null : checkRowData.getMembershipPricingEventMap().get(row.getUuid());

			if (beforeProductFromProductMaster == null) {
				row.setStatus(SaveProductStatus.FAIL);
				row.setErrorMessage(messageSource.getMessage("message51", null, null));
				log.error("record id: {}, uuid: {}, record row id: {}, error message: {}, status: {}", productRecord.getId(), row.getUuid(), row.getId(), messageSource.getMessage("message51", null, null), SaveProductStatusEnum.getProductStatusName(SaveProductStatus.FAIL));
			} else {
				if (SaveProductType.BATCH_EDIT_PRODUCT_TYPE_SET.contains(productRecord.getUploadType())) {
					batchEditHelper.updateSaveProductRecordRowContent(productRecord, row, beforeProductFromProductMaster);
				}
				// if sku need to waiting approval, some fields set edit value for checking,
				// when checking success, set original value send to product master.
				if (SaveProductType.CHECKING_APPROVAL_TYPE_SET.contains(productRecord.getUploadType()) && !checkRowData.getWaitingApprovalTempRowIdMap().isEmpty()) {
					row = approvalDealHelper.convertWaitingApprovalFieldsToEditValue(row, checkRowData.getWaitingApprovalTempRowIdMap().get(row.getId()), ApprovalDealTypeEnum.COMMISSION_RATE);
				}

				CheckProductResultDto check3plResult = preprocessing(checkRowData.getUserDto(), productRecord, row, beforeProductFromProductMaster);

				ResponseDto<Void> checkResponse = checkProduct(checkRowData.getUserDto(), productRecord, row, check3plResult, beforeProductFromProductMaster, beforeProductFromDb, checkPricingResult, checkRowData.getCkeckLittleMallVariantMap(), checkRowData.getRmbRate());
				checkBuHelper.checkUpdateBuList(productRecord, row);
				saveProductHelper.setFieldValueNullByRuleAndType(productRecord, row);
				checkResponse = handleSpecialRecordTypeContent(checkRowData.getUserDto(), checkResponse, productRecord, row);

				if (checkResponse.getStatus() == 1) {
					if (SaveProductType.CHECKING_APPROVAL_TYPE_SET.contains(productRecord.getUploadType()) && !checkRowData.getWaitingApprovalTempRowIdMap().isEmpty()) {
						row = approvalDealHelper.convertWaitingApprovalFieldsFromOriginalValue(row, beforeProductFromProductMaster, ApprovalDealTypeEnum.COMMISSION_RATE);
					}
					row.setStatus(SaveProductStatus.REQUESTING_PM);
				} else {
					row.setStatus(SaveProductStatus.FAIL);
					String errorMessage = generateErrorMessage(checkResponse.getErrorMessageList());
					row.setErrorMessage(errorMessage);
					log.error("record id: {}, uuid: {}, record row id: {}, error message: {}, status: {}", productRecord.getId(), row.getUuid(), row.getId(), errorMessage, SaveProductStatusEnum.getProductStatusName(SaveProductStatus.FAIL));
				}
			}
		}catch (SystemException e){
			row.setStatus(SaveProductStatus.FAIL);
			row.setErrorMessage(e.getMessage());
			log.error("record id: {}, uuid: {}, record row id: {}, error message: {}, status: {}", productRecord.getId(), row.getUuid(), row.getId(), e.getMessage(), SaveProductStatusEnum.getProductStatusName(SaveProductStatus.FAIL));
		}catch (Exception e) {
			row.setStatus(SaveProductStatus.FAIL);
			row.setErrorMessage(messageSource.getMessage("message21", new Object[]{ErrorMessageTypeCode.CHECK_TASK_EXCEPTION}, null));
			log.error("System error code: " + ErrorMessageTypeCode.CHECK_TASK_EXCEPTION, e);
		}

		saveProductRecordRowRepository.save(row);
		return CompletableFuture.completedFuture(true);
	}

	private ProductMasterResultDto getOldProductByProductMaster(UserDto userDto, int uploadType, SaveProductRecordRowDo row,
																Map<String, ProductMasterResultDto> productFromPmMap) {
		ProductMasterResultDto beforeProduct;
		switch (uploadType) {
			case SaveProductType.BATCH_EDIT_PRODUCT:
			case SaveProductType.SYNC_SAME_PRODUCT_CODE:
			case SaveProductType.BATCH_EDIT_STORE_ONLINE_STATUS:
			case SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL:
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION:
			case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
			case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
			case SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY:
			case SaveProductType.BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION:
			case SaveProductType.BATCH_EDIT_PRODUCT_COMMONLY_USED:
			case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT_COMMONLY_USED:
			case SaveProductType.BATCH_EDIT_OVERSEA_DELIVERY:
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_INFO:
			case SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE:
			case SaveProductType.BATCH_EDIT_PRODUCT_READY_DAYS:
				// check uuid
				String uuid = row.getUuid();
				if (StringUtil.isEmpty(uuid)){
					log.error("uuid is null");
					return null;
				}
				beforeProduct = productFromPmMap.get(uuid);
				break;
			case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT:
				SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
				LittleMallProductSearchRequestDto requestDto = LittleMallProductSearchRequestDto.builder()
					.page(1)
					.size(1)
					.merchantId(singleEditProductDto.getProduct().getMerchantId())
					.storeCode(singleEditProductDto.getProduct().getAdditional().getLittleMall().getStoreCode())
					.skuIds(Collections.singletonList(singleEditProductDto.getProduct().getSkuId()))
					.productId(singleEditProductDto.getProduct().getProductId())
					.build();
				ProductOverviewResultDto productOverviewResultDto = productMasterHelper.requestLittleMallProductsByParams(userDto, requestDto);
				beforeProduct = productOverviewResultDto.getContent().isEmpty() ? null : productOverviewResultDto.getContent().get(0);
				break;
			default:
				log.warn("upload type: {}, no need to get old product, row detail: record_id: {}, sku: {}, row_id: {}, uuid: {}",
					uploadType, row.getRecordId(), row.getSku(), row.getId(), row.getUuid());
				beforeProduct = new ProductMasterResultDto();
		}
		return beforeProduct;
	}

	private String generateErrorMessage(List<String> errorMessageList) {
		StringBuilder stringBuilder = new StringBuilder();
		for (int i = 0; i < errorMessageList.size(); i++) {
			if (i != 0) {
				stringBuilder.append(", \n");
			}
			stringBuilder.append(errorMessageList.get(i));
		}
		return stringBuilder.toString();
	}

	private ResponseDto<Void> checkProduct(UserDto userDto, SaveProductRecordDo productRecord, SaveProductRecordRowDo row, CheckProductResultDto check3plResult,
										   ProductMasterResultDto beforeProductFromPM, ProductDo beforeProductFromDb, MembershipPricingEventSetDto checkPricingResult, Map<String, Boolean> ckeckLittleMallVariantMap, BigDecimal rmbRate) {
		try {
			switch (productRecord.getUploadType()) {
				case SaveProductType.BATCH_CREATE_PRODUCT:
				case SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL:
				case SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL:
					ResponseDto<Void> checkCreateResponseDto = checkProductHelper.checkCreateProductHandler(userDto, productRecord, row, check3plResult, rmbRate);
					productPriceMonitorProductHelper.priceMonitorCreateProcess(row, userDto);
					return checkCreateResponseDto;
				case SaveProductType.SINGLE_EDIT_PRODUCT_PACKAGING_INFO:
				case SaveProductType.SYNC_OFFLINE_BUNDLE:
					return ResponseDto.<Void>builder().status(1).build();
				case SaveProductType.BATCH_EDIT_PRODUCT:
				case SaveProductType.SYNC_SAME_PRODUCT_CODE:
				case SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL:
				case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION:
				case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
				case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
				case SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY:
				case SaveProductType.BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION:
				case SaveProductType.BATCH_EDIT_PRODUCT_READY_DAYS:
					ResponseDto<Void> checkUpdateResponseDto = checkProductHelper.checkEditProductHandler(userDto, productRecord, row, check3plResult, beforeProductFromPM, beforeProductFromDb, checkPricingResult, rmbRate);
					productPriceMonitorProductHelper.priceMonitorUpdateProcess(row, beforeProductFromPM, userDto);
					return checkUpdateResponseDto;
				case SaveProductType.BATCH_EDIT_PRODUCT_COMMONLY_USED:
					return checkProductCommonlyUsed(userDto, productRecord, row, beforeProductFromPM);
				case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT_COMMONLY_USED:
					return checkLittleMallProductCommonlyUsed(userDto, productRecord, row, beforeProductFromPM);
				case SaveProductType.BATCH_EDIT_OVERSEA_DELIVERY:
					return checkBatchEditOverseaDelivery(userDto, productRecord.getMerchantId(), row, beforeProductFromPM);
				case SaveProductType.BATCH_EDIT_STORE_ONLINE_STATUS:
					return checkBatchEditStoreOnlineStatus(row, beforeProductFromPM);
				case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_INFO:
					return checkProductPackingInfo(userDto, productRecord, row, beforeProductFromPM);
				case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE:
				case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV:
				case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL:
					return checkLittleMallProductHelper.checkEditLittleMallProduct(row);
				case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT:
				case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT:
					return checkLittleMallProductHelper.checkBatchLittleMallProduct(row, ckeckLittleMallVariantMap);
				// If no need to process product-checking, return success response
				// Add new product type here if no need to process product-checking
				case SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE:
					return successResponseForSkipChecking();
				default:
					return ResponseDto.<Void>builder().errorMessageList(List.of(messageSource.getMessage("message21", new String[]{ErrorMessageTypeCode.UPLOAD_TYPE_NO_IMPLEMENT}, null))).build();

			}
		} catch (SystemException e) {
			return ResponseDto.<Void>builder().errorMessageList(List.of(e.getMessage())).build();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResponseDto.<Void>builder().errorMessageList(List.of(messageSource.getMessage("message62", null, null))).build();
		}
	}

	private static ResponseDto<Void> successResponseForSkipChecking() {
		return ResponseDto.success(null);
	}

	private ResponseDto<Void> checkProductCommonlyUsed(UserDto userDto, SaveProductRecordDo productRecord, SaveProductRecordRowDo row, ProductMasterResultDto beforeProductFromPM) {
		SingleEditProductDto newData = gson.fromJson(row.getContent(), SingleEditProductDto.class);

		if (beforeProductFromPM == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message127", null, null)));
		}
		ProductMasterDto oldData = ProductMasterDto.convertFromProductMasterResultDto(beforeProductFromPM);

		HktvProductDto oldHktvProductDto = oldData.getAdditional().getHktv();
		boolean	isCheckOffLineBundle = isChangeOnlineStatus(newData.getProduct().getAdditional().getHktv().getOnlineStatus(), oldHktvProductDto.getOnlineStatus());
		oldHktvProductDto.setOnlineStatus(Optional.ofNullable(newData.getProduct().getAdditional().getHktv().getOnlineStatus()).orElse(oldHktvProductDto.getOnlineStatus()));
		oldHktvProductDto.setVisibility(Optional.ofNullable(newData.getProduct().getAdditional().getHktv().getVisibility()).orElse(oldHktvProductDto.getVisibility()));

		oldHktvProductDto.setCheckOffLineBundle(isCheckOffLineBundle);
		oldData.setRecordRowId(row.getId());
		oldData.setMmsModifiedTime(LocalDateTime.ofInstant(productRecord.getUploadTime().toInstant(), ZoneId.systemDefault()));
		oldData.setMmsModifiedUser(userDto.getUserCode());
		newData.setProduct(oldData);

		row.setContent(gson.toJson(newData));
		saveProductRecordRowRepository.save(row);

		return ResponseDto.success(null);
	}

	private ResponseDto<Void> checkLittleMallProductCommonlyUsed(UserDto userDto, SaveProductRecordDo productRecord, SaveProductRecordRowDo row, ProductMasterResultDto beforeProductFromPM) {
		SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);

		if (beforeProductFromPM == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message127", null, null)));
		}
		beforeProductFromPM.getAdditional().getLittleMall().setStoreCategory(singleEditProductDto.getProduct().getAdditional().getLittleMall().getStoreCategory());

		ProductMasterDto productMasterDto = ProductMasterDto.convertFromProductMasterResultDto(beforeProductFromPM);
		productMasterDto.setRecordRowId(row.getId());
		productMasterDto.setBuToSend(List.of(ProductMasterBusinessUnitType.LITTLE_MALL));
		productMasterDto.setMmsModifiedTime(LocalDateTime.ofInstant(productRecord.getUploadTime().toInstant(), ZoneId.systemDefault()));
		productMasterDto.setMmsModifiedUser(userDto.getUserCode());
		singleEditProductDto.setProduct(productMasterDto);

		row.setContent(gson.toJson(singleEditProductDto));

		generateIIDSDataHelper.generateIIDSData(row);

		saveProductRecordRowRepository.save(row);
		return ResponseDto.success(null);
	}

	private ResponseDto<Void> checkBatchEditStoreOnlineStatus(SaveProductRecordRowDo row, ProductMasterResultDto product) {
		if (product.getAdditional() == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message127", null, null)));
		} else if (product.getAdditional().getHktv() == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message128", null, null)));
		}
		HktvProductDto hktvProduct = product.getAdditional().getHktv();
		BatchEditStoreOnlineStatusDto editDto = gson.fromJson(row.getContent(), BatchEditStoreOnlineStatusDto.class);

		// If edit dto that online status is null means update contract, so maintain online status by contract product term
		if(editDto.getOnlineStatus() == null){
			editDto.setOnlineStatus(hktvProduct.getOnlineStatus());
		}

		Integer contractId = Optional.ofNullable(editDto.getContractId()).orElse(hktvProduct.getContractNo());
		log.info("BATCH_EDIT_STORE_ONLINE_STATUS check sku: {} online status: {}, edit contract: {} original contract: {}",  row.getSku(), editDto.getOnlineStatus(), editDto.getContractId(), hktvProduct.getContractNo());
		if (editDto.getOnlineStatus() == OnlineStatusEnum.ONLINE) {
			BuProductCategoryDo primaryCategory = buProductCategoryRepository.findByProductCatCode(ConstantType.PLATFORM_CODE_HKTV, hktvProduct.getPrimaryCategoryCode()).orElse(null);
			CheckContractProdTermResultDto contractProdTermResultDto = checkProductHelper.checkContractProdTerm(hktvProduct.getTermName(), contractId, editDto.getStoreId(), hktvProduct.getProductReadyMethod(), row.getSku(), primaryCategory, product.getBrandId());
			ContractProdTermsDo contractProdTermsDo = contractProdTermResultDto.getContractProdTerms();

			if (contractProdTermsDo == null) {
				hktvProduct.setOnlineStatus(OnlineStatusEnum.OFFLINE);
				hktvProduct.setCheckOffLineBundle(true);
				log.info("sku: {} not match contract_prod_term", row.getSku());
			} else {
				log.info("sku: {} match contract_prod_term,id {}, name {},commission rate {},", row.getSku(), contractProdTermsDo.getId(), contractProdTermsDo.getTermsName(), contractProdTermsDo.getCommissionRate());
				String contractType = contractRepository.findMainContractTypeInContract(contractProdTermsDo.getContractId());
				String termName = contractHelper.getTermName(contractType, contractProdTermsDo);

				hktvProduct.setOnlineStatus(OnlineStatusEnum.ONLINE);
				hktvProduct.setCommissionRate(contractProdTermsDo.getCommissionRate());
				hktvProduct.setTermName(termName);
				if (product.getAdditional().getThirdParty() != null) {
					product.getAdditional().getThirdParty().setCommissionRate(contractProdTermsDo.getCommissionRate());
				}
			}
		} else if (editDto.getOnlineStatus() == OnlineStatusEnum.OFFLINE) {
			if(hktvProduct.getOnlineStatus() == OnlineStatusEnum.ONLINE){
				hktvProduct.setOnlineStatus(OnlineStatusEnum.OFFLINE);
				hktvProduct.setCheckOffLineBundle(true);
			}
		}
		hktvProduct.setContractNo(contractId);

		product.getAdditional().setHktv(hktvProduct);
		ProductMasterDto productMasterDto = ProductMasterDto.convertFromProductMasterResultDto(product);
		productMasterDto.setRecordRowId(row.getId());
		SingleEditProductDto editProductDto = new SingleEditProductDto();
		editProductDto.setProduct(productMasterDto);
		row.setContent(gson.toJson(editProductDto));
		return ResponseDto.success(null);
	}

	private ResponseDto<Void> checkBatchEditOverseaDelivery(UserDto userDto, Integer merchantId, SaveProductRecordRowDo row, ProductMasterResultDto beforeProductFromPM) {
		BatchEditOverseaDeliveryRequestDetailDto requestDto = gson.fromJson(row.getContent(), BatchEditOverseaDeliveryRequestDetailDto.class);
		List<String> errorMessageList = checkBatchEditOverseaDeliveryRequest(userDto, merchantId, row, requestDto);
		if (CollectionUtil.isNotEmpty(errorMessageList)) {
			return ResponseDto.fail(errorMessageList);
		}

		if (beforeProductFromPM == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message127", null, null)));
		}

		List<String> deliveryDistrictList = BatchOverseaDeliveryUtil.generateDeliveryDistrictList(requestDto, beforeProductFromPM.getAdditional().getHktv().getDeliveryDistrict());
		String deliveryMethodCode = beforeProductFromPM.getAdditional().getHktv().getDeliveryMethod();
		StoreDo store = storeRepository.findHktvStoreByStoreCode(beforeProductFromPM.getAdditional().getHktv().getStores()).orElse(null);
		if (store == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message69", null, null)));
		}
		String primaryCategoryCode = beforeProductFromPM.getAdditional().getHktv().getPrimaryCategoryCode();
		BuProductCategoryDo primaryCategory = buProductCategoryRepository.findByProductCatCode(ConstantType.PLATFORM_CODE_HKTV, primaryCategoryCode).orElse(null);
		if (primaryCategory == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message49", new String[]{primaryCategoryCode}, null)));
		}

		List<String> originalDeliveryDistrictList = beforeProductFromPM.getAdditional().getHktv().getDeliveryDistrict();
		String readyMethodCode = beforeProductFromPM.getAdditional().getHktv().getProductReadyMethod();
		CheckProductResultDto checkOverseaDeliveryDistrictFormatResult = checkProductHelper.checkOverseaDeliveryDistrict(SaveProductSource.MMS, userDto,
			deliveryDistrictList, store, primaryCategory, deliveryMethodCode, originalDeliveryDistrictList, readyMethodCode);
		errorMessageList.addAll(checkOverseaDeliveryDistrictFormatResult.getErrorMessageList());

		return ResponseDto.<Void>builder()
				.status(errorMessageList.isEmpty() ? 1 : -1)
				.errorMessageList(errorMessageList)
				.build();
	}

	private List<String> checkBatchEditOverseaDeliveryRequest(UserDto userDto, Integer merchantId, SaveProductRecordRowDo row, BatchEditOverseaDeliveryRequestDetailDto requestDto) {
		List<String> errorMessageList = new ArrayList<>();
		if (requestDto == null) {
			errorMessageList.add("no has request");
		} else {
			if (StringUtil.isEmpty(requestDto.getStoreId())) {
				errorMessageList.add(messageSource.getMessage("message133", new String[]{"Store ID"}, null));
			}
			if (StringUtil.isEmpty(requestDto.getSkuId())) {
				errorMessageList.add(messageSource.getMessage("message133", new String[]{"Sku ID"}, null));
			}
			if (requestDto.getAction() == null) {
				errorMessageList.add(messageSource.getMessage("message133", new String[]{"Action "}, null));
			}
			if (StringUtil.isEmpty(requestDto.getOverseaDistrict())) {
				errorMessageList.add(messageSource.getMessage("message133", new String[]{"Oversea District"}, null));
			}
		}

		if (ConstantType.NON_EXISTENT_MERCHANT_ID.equals(merchantId)) {
			errorMessageList.add(messageSource.getMessage("message134", null, null));
		} else {
			try {
				permissionHelper.checkPermission(userDto, merchantId);
			} catch (Exception e) {
				errorMessageList.add(messageSource.getMessage("message132", new Object[]{Optional.ofNullable(requestDto).orElse(new BatchEditOverseaDeliveryRequestDetailDto()).getStoreId()}, null));
			}
		}

		int skuInUpload = saveProductRecordRowRepository.countByRecordIdAndSku(row.getRecordId(), row.getSku());
		if (skuInUpload > 1) {
			errorMessageList.add(messageSource.getMessage("message137", null, null));
		}

		return errorMessageList;
	}

	private CheckProductResultDto preprocessing(UserDto userDto, SaveProductRecordDo record, SaveProductRecordRowDo row, ProductMasterResultDto beforeProduct) {
		CheckProductResultDto check3PlResult = null;
		switch (record.getUploadType()) {
			case SaveProductType.BATCH_CREATE_PRODUCT:
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL:
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL:
			case SaveProductType.BATCH_EDIT_PRODUCT:
			case SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL:
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION:
			case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
			case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
			case SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY:
			case SaveProductType.BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION:
			case SaveProductType.BATCH_EDIT_PRODUCT_READY_DAYS:
				check3PlResult = productPreProcessingHelper.preProcessingHktvProduct(userDto, record, row, beforeProduct);
				generateIIDSDataHelper.generateIIDSData(row);
				break;
			case SaveProductType.SYNC_SAME_PRODUCT_CODE:
				generateIIDSDataHelper.generateIIDSData(row);
				break;
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL:
			case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT:
				productPreProcessingHelper.preProcessingThePlaceProduct(userDto, record, row);
				generateIIDSDataHelper.generateIIDSData(row);
				break;
			default:
		}
		return check3PlResult;
	}

	private ResponseDto<Void> checkProductPackingInfo(UserDto userDto, SaveProductRecordDo productRecord, SaveProductRecordRowDo row, ProductMasterResultDto beforeProductFromPM) {
		SingleEditProductDto newData = gson.fromJson(row.getContent(), SingleEditProductDto.class);

		if (beforeProductFromPM == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message127", null, null)));
		}

		ProductMasterDto oldData = ProductMasterDto.convertFromProductMasterResultDto(beforeProductFromPM);
		//check params can not be null or empty
		List<String> errorMessageList = new ArrayList<>(checkPackingParams(newData));
		if (CollectionUtil.isNotEmpty(errorMessageList)) {
			return ResponseDto.<Void>builder().errorMessageList(errorMessageList).build();
		}

		String buCode = ConstantType.PLATFORM_CODE_HKTV;
		String primaryCategoryCode = oldData.getAdditional().getHktv().getPrimaryCategoryCode();
		BuProductCategoryDo primaryCategory = buProductCategoryRepository.findByProductCatCode(buCode, primaryCategoryCode).orElse(null);
		if (primaryCategory == null) {
			return ResponseDto.<Void>builder().status(-1).errorMessageList(List.of(messageSource.getMessage("message49", new String[]{primaryCategoryCode}, null))).build();
		}

		boolean tplBuExist = oldData.getAdditional().getThirdParty() != null;
		boolean is3PLProductReadyMethod = ProductReadyMethodType.THIRD_PARTY.equals(oldData.getAdditional().getHktv().getProductReadyMethod());
		SkuValidateRequestDto skuValidateRequestDto = editPackingInfoGenerateSkuValidateRequestDto(oldData, newData.getProduct(), tplBuExist);
		CheckProductResultDto checkPackInfoBy3PLResult = checkProductHelper.checkPackInfoBy3PL(userDto, List.of(skuValidateRequestDto), newData.getProduct().getSkuId());
		if (tplBuExist && !checkPackInfoBy3PLResult.isResult() && is3PLProductReadyMethod){
			throw new SystemException(gson.toJson(checkPackInfoBy3PLResult.getErrorMessageList()));
		}

		ProductMasterDto newProductMasterDto = newData.getProduct();
		HktvProductDto hktvProductDto = oldData.getAdditional().getHktv();

		//check packing box type with primary category code in system param
		CheckProductResultDto checkPrimaryCategoryAndPackingBoxTypeResult =
				checkProductHelper.checkCategoryCodeAndPackingBoxType(buCode, primaryCategory, newProductMasterDto.getPackingBoxType());
		errorMessageList.addAll(checkPrimaryCategoryAndPackingBoxTypeResult.getErrorMessageList());
        //check if product ready method -> eVoucher can not with eVoucher as same time and product ready method -> merchant delivery can not with Fresh Meat/Fish as same time
		CheckProductResultDto checkReadyMethodAndPackingBoxTypeResult =
				checkProductHelper.checkReadyMethodAndPackingBoxType(hktvProductDto.getProductReadyMethod(), newProductMasterDto.getPackingBoxType(), hktvProductDto.getContractNo(),
					hktvProductDto.getPrimaryCategoryCode());
		errorMessageList.addAll(checkReadyMethodAndPackingBoxTypeResult.getErrorMessageList());
		//check product ready method and packing info logic
		CheckProductResultDto checkPackingInformationResult =
				checkProductHelper.checkPackingInformation(hktvProductDto.getProductReadyMethod(), newProductMasterDto.getWeight()
						, newProductMasterDto.getPackingHeight(), newProductMasterDto.getPackingDepth(), newProductMasterDto.getPackingLength(), newProductMasterDto.getWeightUnit(), newProductMasterDto.getPackingDimensionUnit());
		errorMessageList.addAll(checkPackingInformationResult.getErrorMessageList());
		//Fill one of carton length height depth that all carton dimension should be fill , and All carton dimension cannot be equal to 0
		if(newProductMasterDto.getCartonSizeList() != null){
			CartonSizeDto cartonSizeDto = newProductMasterDto.getCartonSizeList().get(0);
			CheckProductResultDto checkCartonPackingInfoResult =
					checkProductHelper.checkCartonPackingInformation(cartonSizeDto.getHeight(), cartonSizeDto.getLength(), cartonSizeDto.getWidth());
			errorMessageList.addAll(checkCartonPackingInfoResult.getErrorMessageList());
		}
		//check weight limit
		CheckProductResultDto checkWeightLimitByDeliveryResult =
				checkProductHelper.checkWeightLimitByDeliveryMethod(buCode, hktvProductDto.getDeliveryMethod(), newProductMasterDto.getWeight(), newProductMasterDto.getWeightUnit());
		errorMessageList.addAll(checkWeightLimitByDeliveryResult.getErrorMessageList());

		boolean isAdd3PLData = !(Boolean.FALSE.equals(tplBuExist) && Boolean.FALSE.equals(checkPackInfoBy3PLResult.isResult()));
		//covert data from product master data to edit product data
		generatePackingInfoEditData(oldData, newData, row.getId(), isAdd3PLData);

		// add Bu to send
		List<String> updateBuList = new ArrayList<>();
		newProductMasterDto.setMmsModifiedTime(LocalDateTime.ofInstant(productRecord.getUploadTime().toInstant(), ZoneId.systemDefault()));
		newProductMasterDto.setMmsModifiedUser(userDto.getUserCode());
		addBuToSendByAdditional(newData.getProduct().getAdditional(), updateBuList);
		addBuToSendByAdditional(oldData.getAdditional(), updateBuList);
		newData.getProduct().setBuToSend(updateBuList);

		row.setContent(gson.toJson(newData));
		saveProductRecordRowRepository.save(row);

		return ResponseDto.<Void>builder()
				.status(errorMessageList.isEmpty() ? 1 : -1)
				.errorMessageList(errorMessageList)
				.build();
	}

	private void generatePackingInfoEditData(ProductMasterDto oldData, SingleEditProductDto newData, Long rowId, boolean isAdd3PLData) {
		ProductMasterDto newProductMasterDto = newData.getProduct();
		newProductMasterDto.setUuid(oldData.getUuid());
		newProductMasterDto.setRecordRowId(rowId);

		if (isAdd3PLData) {
			BuProductDto oldAdditional = oldData.getAdditional();
			HktvProductDto oldHktvProductDto = oldAdditional.getHktv();
			ThirdPLProductDto thirdPLDto = oldData.getAdditional().getThirdParty();
			if (thirdPLDto == null) {
				thirdPLDto = new ThirdPLProductDto();
				thirdPLDto.setBuInfo(new ThirdPLBUInfoDto());
				thirdPLDto.setSkuShortDescriptionCh(oldHktvProductDto.getSkuShortDescriptionCh());
				thirdPLDto.setSkuShortDescriptionEn(oldHktvProductDto.getSkuShortDescriptionEn());
				thirdPLDto.setMainPhoto(oldHktvProductDto.getMainPhoto());
				thirdPLDto.setSellingPrice(oldHktvProductDto.getSellingPrice());
				thirdPLDto.setCommissionRate(oldHktvProductDto.getCommissionRate());

				ThirdPLBUHktvDto thirdPLBUHktvDto = new ThirdPLBUHktvDto();
				thirdPLBUHktvDto.setStoreSkuId(oldHktvProductDto.getStoreSkuId());
				thirdPLBUHktvDto.setProductReadyMethod(oldHktvProductDto.getProductReadyMethod());
				thirdPLDto.getBuInfo().setHktv(thirdPLBUHktvDto);

				newProductMasterDto.getAdditional().setThirdParty(thirdPLDto);
			} else {
				newProductMasterDto.getAdditional().setThirdParty(oldData.getAdditional().getThirdParty());
			}
		} else {
			newProductMasterDto.getAdditional().setThirdParty(null);
		}
	}

	private List<String> checkPackingParams(SingleEditProductDto newData){
		List<String> errorMessageList = new ArrayList<>();
		ProductMasterDto editProduct = newData.getProduct();

		if(StringUtil.isEmpty(editProduct.getPackingBoxType())){
			errorMessageList.add(messageSource.getMessage("message142", null, null));
		}
		if(StringUtil.isEmpty(editProduct.getPackingDimensionUnit())){
			errorMessageList.add(messageSource.getMessage("message143", null, null));
		}
		if(StringUtil.isEmpty(editProduct.getWeightUnit())){
			errorMessageList.add(messageSource.getMessage("message144", null, null));
		}
		if (editProduct.getWeight() == null){
			errorMessageList.add(messageSource.getMessage("message92", null, null));
		}
		if (editProduct.getPackingHeight() == null){
			errorMessageList.add(messageSource.getMessage("message84", null, null));
		}
		if (editProduct.getPackingDepth() == null){
			errorMessageList.add(messageSource.getMessage("message85", null, null));
		}
		if (editProduct.getPackingLength() == null){
			errorMessageList.add(messageSource.getMessage("message86", null, null));
		}
		return errorMessageList;
	}

	private static SkuValidateRequestDto editPackingInfoGenerateSkuValidateRequestDto(ProductMasterDto product, ProductMasterDto newData, boolean tplBuExist) {
		String uuid = tplBuExist ? product.getUuid() : null;
		List<String> barCodeList = product.getBarcodes() == null ? new ArrayList<>() : product.getBarcodes().stream().map(ProductBarcodeDto::getEan).collect(Collectors.toList());

		return new SkuValidateRequestDto(product.getMerchantId(), uuid, newData.getPackingLength(), newData.getPackingDepth(), newData.getPackingHeight()
				, newData.getWeight(), newData.getPackingBoxType(), barCodeList, newData.getPackingDimensionUnit(), newData.getWeightUnit());
	}

	private boolean isChangeOnlineStatus(OnlineStatusEnum editStatus, OnlineStatusEnum beforeStatus){
		// check is change online status
		if(Objects.nonNull(editStatus) && OnlineStatusEnum.ONLINE == beforeStatus) {
			return OnlineStatusEnum.OFFLINE == editStatus;
		}
		return false;
	}

	private void addBuToSendByAdditional(BuProductDto additional, List<String> updateBuList) {
		if (additional.getHktv() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.HKTV);
		}
		if (additional.getThirdParty() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.THIRD_PL);
		}
		if (additional.getLittleMall() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.LITTLE_MALL);
		}
		if (additional.getIids() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.IIDS);
		}
		Set<String> updateBuSet = new HashSet<>(updateBuList);
		updateBuList.clear();
		updateBuList.addAll(updateBuSet);
	}

	private ResponseDto<Void> handleSpecialRecordTypeContent(UserDto userDto, ResponseDto<Void> checkResponse, SaveProductRecordDo productRecord, SaveProductRecordRowDo row) {
		if (checkResponse.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
			switch (productRecord.getUploadType()) {
				case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE:
				case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV:
				case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL:
					checkResponse = productBatchImportHelper.addLittleMallProductMigrationContent(userDto, row);
					break;
				case SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL:
					checkResponse = productBatchImportHelper.addHktvProductMigrationContent(userDto, row);
					break;
				default:
					break;
			}
		}

		return checkResponse;
	}
}
