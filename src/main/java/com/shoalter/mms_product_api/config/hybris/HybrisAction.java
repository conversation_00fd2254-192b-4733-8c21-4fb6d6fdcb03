package com.shoalter.mms_product_api.config.hybris;

import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;

import java.util.Map;
import java.util.Set;

public final class HybrisAction {
    public static final String PRODUCT_SYNC_MODE_ALL = "updateAll";
    public static final String PRODUCT_SYNC_MODE_ONOFFLINE = "updateOnOfflineDate";
    public static final String PRODUCT_SYNC_MODE_PRICE = "updatePrice";
    public static final String PRODUCT_SYNC_MODE_MALL_DOLLAR = "updateMallDollar";
    public static final String PRODUCT_SYNC_MODE_VISIBILITY = "updateInvisibleFlag";
	public static final String PRODUCT_SYNC_MODE_SIMPLIFIED_CHINESE = "updateSimplifiedChinese";
	public static final String PRODUCT_SYNC_MODE_PICK_UP_DAYS = "updateReadyPickupDays";

    private HybrisAction() {}

	private static final Map<Integer, String> actionMap = Map.of(
		SaveProductType.SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT, PRODUCT_SYNC_MODE_MALL_DOLLAR,
		SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS, PRODUCT_SYNC_MODE_ONOFFLINE,
		SaveProductType.BATCH_EDIT_PRODUCT_PRICE, PRODUCT_SYNC_MODE_PRICE,
		SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY, PRODUCT_SYNC_MODE_VISIBILITY,
		SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE, PRODUCT_SYNC_MODE_SIMPLIFIED_CHINESE,
		SaveProductType.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB, PRODUCT_SYNC_MODE_PRICE,
		SaveProductType.BATCH_EDIT_PRODUCT_READY_DAYS, PRODUCT_SYNC_MODE_PICK_UP_DAYS
	);

	public static final Set<String> UPDATE_DISCOUNT_ACTIONS = Set.of(PRODUCT_SYNC_MODE_ALL, PRODUCT_SYNC_MODE_PRICE);

	public static String getAction(Integer uploadType) {
		return actionMap.getOrDefault(uploadType, PRODUCT_SYNC_MODE_ALL);
	}
}
