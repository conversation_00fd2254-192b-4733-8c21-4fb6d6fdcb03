package com.shoalter.mms_product_api.config.security.xss;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StringDeserializer;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class XssStringDeserializer extends StringDeserializer {

	@Override
	public String deserialize(JsonParser parser, DeserializationContext context) throws IOException {

		return 	XssUtils.xssClean(parser.getText());
	}
}
