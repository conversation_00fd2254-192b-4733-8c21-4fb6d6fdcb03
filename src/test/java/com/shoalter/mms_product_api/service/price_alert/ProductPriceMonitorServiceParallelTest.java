package com.shoalter.mms_product_api.service.price_alert;

import com.shoalter.mms_product_api.config.product.MonitorProductPriceStatus;
import com.shoalter.mms_product_api.dao.repository.product.ProductPriceMonitorProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductDo;
import com.shoalter.mms_product_api.service.product.helper.BusUnitHelper;
import com.shoalter.mms_product_api.service.product.helper.MmsThirdPartySkuHelper;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailResponseDataDto;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.onebound.OneBoundDetailSkuDto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for the parallel processing capabilities of ProductPriceMonitorService
 *
 * These tests focus on the processProductGroupsInParallel method which handles:
 * - Parallel processing of product groups
 * - Batching of tasks
 * - Thread pool management
 * - Exception handling
 * - Executor shutdown
 */
@ExtendWith(MockitoExtension.class)
class ProductPriceMonitorServiceParallelTest {

    // Mocks
    @Mock(lenient = true)
    private ProductPriceMonitorProductRepository productPriceMonitorProductRepository;

    @Mock(lenient = true)
    private MmsThirdPartySkuHelper mmsThirdPartySkuHelper;

    @Mock(lenient = true)
    private BusUnitHelper busUnitHelper;

    @Spy
    @InjectMocks
    private ProductPriceMonitorService productPriceMonitorService;

    @Captor
    private ArgumentCaptor<List<ProductPriceMonitorProductDo>> productsCaptor;

    // Test constants
    private static final String TARGET_PRODUCT_CODE_1 = "PROD-1";
    private static final String TARGET_PRODUCT_CODE_2 = "PROD-2";
    private static final String TARGET_PRODUCT_CODE_3 = "PROD-3";
    private static final String TARGET_SKU_CODE_1 = "SKU-1";
    private static final String TARGET_SKU_CODE_2 = "SKU-2";
    private static final String TARGET_SKU_CODE_3 = "SKU-3";

    // Test data
    private Map<String, List<ProductPriceMonitorProductDo>> productGroups;
    private ProductPriceMonitorProductDo product1, product2, product3;

    @BeforeEach
    void setUp() {
        // Set oneBoundThreads field via reflection
        ReflectionTestUtils.setField(productPriceMonitorService, "oneBoundThreads", 2);

        // Create test products
        product1 = createTestProduct(1, TARGET_PRODUCT_CODE_1, TARGET_SKU_CODE_1);
        product2 = createTestProduct(2, TARGET_PRODUCT_CODE_2, TARGET_SKU_CODE_2);
        product3 = createTestProduct(3, TARGET_PRODUCT_CODE_3, TARGET_SKU_CODE_3);

        // Set up product groups
        productGroups = new HashMap<>();
        productGroups.put(TARGET_PRODUCT_CODE_1, Collections.singletonList(product1));
        productGroups.put(TARGET_PRODUCT_CODE_2, Collections.singletonList(product2));
        productGroups.put(TARGET_PRODUCT_CODE_3, Collections.singletonList(product3));
    }

    private ProductPriceMonitorProductDo createTestProduct(int id, String productCode, String skuCode) {
        ProductPriceMonitorProductDo product = new ProductPriceMonitorProductDo();
        product.setId(id);
        product.setTargetProductCode(productCode);
        product.setTargetSkuCode(skuCode);
        product.setPriceStatus(MonitorProductPriceStatus.PENDING.getValue());
        return product;
    }

    /**
     * Tests for successful processing scenarios
     */
    @Nested
    @DisplayName("Success Scenarios")
    class SuccessTests {

        @BeforeEach
        void setUp() {
            // Mock the processProductGroup method to return the size of products list
            doAnswer(invocation -> {
                List<ProductPriceMonitorProductDo> products = invocation.getArgument(1);
                return products.size();
            }).when(productPriceMonitorService).processProductGroup(anyString(), anyList());
        }

        @Test
        @DisplayName("Should process all product groups in parallel and return total count")
        void processProductGroupsInParallel_Success() throws Exception {
            // Given
            // Setup is in @BeforeEach

            // When
			int result = productPriceMonitorService.processProductGroupsInParallel(productGroups);

            // Then
            assertEquals(3, result, "Should return the total number of processed products");

            // Verify processProductGroup was called for each product group
            verify(productPriceMonitorService, times(1)).processProductGroup(eq(TARGET_PRODUCT_CODE_1), anyList());
            verify(productPriceMonitorService, times(1)).processProductGroup(eq(TARGET_PRODUCT_CODE_2), anyList());
            verify(productPriceMonitorService, times(1)).processProductGroup(eq(TARGET_PRODUCT_CODE_3), anyList());

            // Verify shutdownExecutor was called
            verify(productPriceMonitorService, times(1)).shutdownExecutor(any(ExecutorService.class), anyInt());
        }

        @Test
        @DisplayName("Should handle empty map gracefully")
        void processProductGroupsInParallel_EmptyMap() {
            // Given
            Map<String, List<ProductPriceMonitorProductDo>> emptyMap = new HashMap<>();

            // When
			int result = productPriceMonitorService.processProductGroupsInParallel(emptyMap);

            // Then
            assertEquals(0, result, "Should return 0 for empty map");

            // Verify processProductGroup was never called
            verify(productPriceMonitorService, never()).processProductGroup(anyString(), anyList());

            // Verify shutdownExecutor was never called since we return early
            verify(productPriceMonitorService, never()).shutdownExecutor(any(ExecutorService.class), anyInt());
        }

        @Test
        @DisplayName("Should handle null map gracefully")
        void processProductGroupsInParallel_NullMap() {
            // When
			int result = productPriceMonitorService.processProductGroupsInParallel(null);

            // Then
            assertEquals(0, result, "Should return 0 for null map");

            // Verify processProductGroup was never called
            verify(productPriceMonitorService, never()).processProductGroup(anyString(), anyList());

            // Verify shutdownExecutor was never called since we return early
            verify(productPriceMonitorService, never()).shutdownExecutor(any(ExecutorService.class), anyInt());
        }

        @Test
        @DisplayName("Should use thread pool size from configuration")
        void processProductGroupsInParallel_UseConfiguredThreadPool() {
            // Given
            ReflectionTestUtils.setField(productPriceMonitorService, "oneBoundThreads", 5);

            // When
            productPriceMonitorService.processProductGroupsInParallel(productGroups);

            // Then
            // This is harder to test directly without exposing the executor,
            // but we can verify the method was called the correct number of times
            verify(productPriceMonitorService, times(3)).processProductGroup(anyString(), anyList());
        }
    }

    /**
     * Tests for error handling scenarios
     */
    @Nested
    @DisplayName("Error Handling")
    class ErrorHandlingTests {

        @Test
        @DisplayName("Should handle exceptions from processProductGroup")
        void processProductGroupsInParallel_HandlesProcessingException() throws Exception {
            // Given
            // Mock the processProductGroup method to throw an exception for one product code
            doAnswer(invocation -> {
                List<ProductPriceMonitorProductDo> products = invocation.getArgument(1);
                return products.size();
            }).when(productPriceMonitorService).processProductGroup(eq(TARGET_PRODUCT_CODE_1), anyList());

            doAnswer(invocation -> {
                List<ProductPriceMonitorProductDo> products = invocation.getArgument(1);
                return products.size();
            }).when(productPriceMonitorService).processProductGroup(eq(TARGET_PRODUCT_CODE_3), anyList());

            doThrow(new RuntimeException("Test exception"))
                .when(productPriceMonitorService).processProductGroup(eq(TARGET_PRODUCT_CODE_2), anyList());

            // When
			int result = productPriceMonitorService.processProductGroupsInParallel(productGroups);

            // Then
            // Should still get results from non-failed tasks
            assertEquals(2, result, "Should return count from successful tasks only");

            // Verify shutdownExecutor was called
            verify(productPriceMonitorService, times(1)).shutdownExecutor(any(ExecutorService.class), anyInt());
        }

    }

    /**
     * Tests for the actual batch processing logic
     */
    @Nested
    @DisplayName("Batch Processing Logic")
    class BatchProcessingTests {

        @Test
        @DisplayName("Should process entries in appropriate batch sizes")
        void processBatches_UseCorrectBatchSize() throws Exception {
            // Given
            // Create a large map with 10 product codes
            Map<String, List<ProductPriceMonitorProductDo>> largeMap = new HashMap<>();
            for (int i = 1; i <= 10; i++) {
                String productCode = "PROD-" + i;
                ProductPriceMonitorProductDo product = createTestProduct(i, productCode, "SKU-" + i);
                largeMap.put(productCode, Collections.singletonList(product));
            }

            // Mock processProductBatch to return batch size
            doAnswer(invocation -> {
                List<?> batch = invocation.getArgument(0);
                return batch.size();
            }).when(productPriceMonitorService).processProductBatch(anyList(), any(ExecutorService.class));

            // Set oneBoundThreads to control batch size
            ReflectionTestUtils.setField(productPriceMonitorService, "oneBoundThreads", 3);

            // When
			int result = productPriceMonitorService.processProductGroupsInParallel(largeMap);

            // Then
            assertEquals(10, result, "Should process all 10 products");

            // With batch size 3 and 10 items, we should have 4 batches:
            // 3 + 3 + 3 + 1 = 10
            verify(productPriceMonitorService, times(4)).processProductBatch(anyList(), any(ExecutorService.class));
        }
    }
}
