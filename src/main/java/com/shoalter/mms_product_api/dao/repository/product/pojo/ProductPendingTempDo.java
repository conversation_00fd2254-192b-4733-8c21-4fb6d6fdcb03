package com.shoalter.mms_product_api.dao.repository.product.pojo;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Data;
import javax.persistence.*;
@Entity
@Table(name = "PRODUCT_PENDING_TEMP")
@Data
public class ProductPendingTempDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition="int")
    private Integer id;
    @Column(name = "PRODUCT_ID")
    private Integer productId;
    @Column(name = "CONTRACT_ID")
    private Integer contractId;
    @Column(name = "MERCHANT_ID")
    private Integer merchantId;
    @Column(name = "STORE_ID")
    private Integer storeId;
    @Column(name = "ACTION")
    private String action;
    @Column(name = "PRODUCT_CODE")
    private String productCode;
    @Column(name = "SKU_CODE")
    private String skuCode;
    @Column(name = "SKU_NAME")
    private String skuName;
    @Column(name = "SKU_NAME_TCHI")
    private String skuNameTchi;
    @Column(name = "SKU_S_TITLE_HKTV_EN")
    private String skuSTitleHktvEn;
    @Column(name = "SKU_S_TITLE_HKTV_CH")
    private String skuSTitleHktvCh;
    @Column(name = "SKU_S_DESC_HKTV_EN", columnDefinition="text")
    private String skuSDescHktvEn;
    @Column(name = "SKU_S_DESC_HKTV_CH", columnDefinition="text")
    private String skuSDescHktvCh;
    @Column(name = "SKU_L_TITLE_HKTV_EN")
    private String skuLTitleHktvEn;
    @Column(name = "SKU_L_TITLE_HKTV_CH")
    private String skuLTitleHktvCh;
    @Column(name = "SKU_L_DESC_HKTV_EN", columnDefinition="text")
    private String skuLDescHktvEn;
    @Column(name = "SKU_L_DESC_HKTV_CH", columnDefinition="text")
    private String skuLDescHktvCh;
    @Column(name = "VIDEO_LINK")
    private String videoLink;
    @Column(name = "VIDEO_LINK_EN")
    private String videoLinkEn;
    @Column(name = "VIDEO_LINK_CH")
    private String videoLinkCh;
    @Column(name = "BARCODE")
    private String barcode;
    @Column(name = "MANU_COUNTRY")
    private String manuCountry;
    @Column(name = "WEIGHT")
    private BigDecimal weight;
    @Column(name = "WEIGHT_UNIT")
    private String weightUnit;
    @Column(name = "PACK_HEIGHT")
    private BigDecimal packHeight;
    @Column(name = "PACK_LENGTH")
    private BigDecimal packLength;
    @Column(name = "PACK_DEPTH")
    private BigDecimal packDepth;
    @Column(name = "PACK_DIMENSION_UNIT")
    private String packDimensionUnit;
    @Column(name = "PACK_BOX_TYPE")
    private String packBoxType;
    @Column(name = "PACK_SPEC_EN")
    private String packSpecEn;
    @Column(name = "PACK_SPEC_CH")
    private String packSpecCh;
    @Column(name = "CURRENCY_CODE")
    private String currencyCode;
    @Column(name = "ORIGINAL_PRICE")
    private BigDecimal originalPrice;
    @Column(name = "SELLING_PRICE")
    private BigDecimal sellingPrice;
    @Column(name = "DELIVERY_DETAILS_EN", columnDefinition="text")
    private String deliveryDetailsEn;
    @Column(name = "DELIVERY_DETAILS_CH", columnDefinition="text")
    private String deliveryDetailsCh;
    @Column(name = "DELIVERY_COMPLETION_DAYS")
    private Integer deliveryCompletionDays;
    @Column(name = "COLOR_EN")
    private String colorEn;
    @Column(name = "COLOR_CH")
    private String colorCh;
    @Column(name = "SIZE_SYSTEM")
    private String sizeSystem;
    @Column(name = "SIZE")
    private String size;
    @Column(name = "INVISIBLE_FLAG")
    private String invisibleFlag;
    @Column(name = "FORCE_OUT_OF_STOCK")
    private String forceOutOfStock;
    @Column(name = "STATUS")
    private String status;
    @Column(name = "COLOR_FAMILIAR")
    private String colorFamiliar;
    @Column(name = "CONSUMABLE")
    private String consumable;
    @Column(name = "PRIORITY")
    private Integer priority;
    @Column(name = "FEATURE_START_TIME")
    private Date featureStartTime;
    @Column(name = "FEATURE_END_TIME")
    private Date featureEndTime;
    @Column(name = "VOUCHER_TYPE")
    private String voucherType;
    @Column(name = "VOUCHER_DISPLAY_TYPE")
    private String voucherDisplayType;
    @Column(name = "BUY_MAX")
    private BigDecimal buyMax;
    @Column(name = "WEBSITE")
    private String website;
    @Column(name = "REMARKS")
    private String remarks;
    @Column(name = "REDEEM_ID")
    private String redeemId;
    @Column(name = "REDEEM_BUS_NAME")
    private String redeemBusName;
    @Column(name = "REDEEM_LATITUDE")
    private String redeemLatitude;
    @Column(name = "REDEEM_LONGITUDE")
    private String redeemLongitude;
    @Column(name = "REDEEM_LOC_CITY")
    private String redeemLocCity;
    @Column(name = "REDEEM_ADDRESS")
    private String redeemAddress;
    @Column(name = "OPTION_DISPLAY_ORDER")
    private Integer optionDisplayOrder;
    @Column(name = "REDEEM_START_DATE")
    private String redeemStartDate;
    @Column(name = "REDEEM_END_DATE")
    private Date redeemEndDate;
    @Column(name = "REDEEM_TYPE")
    private String redeemType;
    @Column(name = "EXPIRY_TYPE")
    private String expiryType;
    @Column(name = "FIXED_REDEMPTION_END_DATE")
    private Date fixedRedemptionEndDate;
    @Column(name = "NO_OF_DAY_AFTER_FEATURE_END")
    private String noOfDayAfterFeatureEnd;
    @Column(name = "UPON_PURCHASE_DATE")
    private Integer uponPurchaseDate;
    @Column(name = "PAYMENT_TERMS")
    private String paymentTerms;
    @Column(name = "FINE_PRINT_TITLE_EN")
    private String finePrintTitleEn;
    @Column(name = "FINE_PRINT_TITLE_CH")
    private String finePrintTitleCh;
    @Column(name = "FINE_PRINT_EN", columnDefinition="text")
    private String finePrintEn;
    @Column(name = "FINE_PRINT_CH", columnDefinition="text")
    private String finePrintCh;
    @Column(name = "DELIVERY_TITLE_EN")
    private String deliveryTitleEn;
    @Column(name = "DELIVERY_TITLE_CH")
    private String deliveryTitleCh;
    @Column(name = "COMMISSION_RATE")
    private BigDecimal commissionRate;
    @Column(name = "COST")
    private BigDecimal cost;
    @Column(name = "CREATED_BY")
    private String createdBy;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "LAST_UPDATED_BY")
    private String lastUpdatedBy;
    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;
    @Column(name = "STORAGE_FEE")
    private BigDecimal storageFee;
    @Column(name = "REMOVAL_SERVICES")
    private String removalServices;
    @Column(name = "CONTRACT_PROD_TERMS_ID")
    private Integer contractProdTermsId;
    @Column(name = "FIELD1")
    private String field1;
    @Column(name = "VALUE1")
    private String value1;
    @Column(name = "FIELD2")
    private String field2;
    @Column(name = "VALUE2")
    private String value2;
    @Column(name = "FIELD3")
    private String field3;
    @Column(name = "VALUE3")
    private String value3;
    @Column(name = "WEIGHT_LEVEL")
    private String weightLevel;
    @Column(name = "DISCOUNT_TEXT", columnDefinition="text")
    private String discountText;
    @Column(name = "DISCOUNT_TEXT_TCHI", columnDefinition="text")
    private String discountTextTchi;
    @Column(name = "STYLE")
    private String style;
    @Column(name = "MAIN_PHOTO_HKTVMALL")
    private String mainPhotoHktvmall;
    @Column(name = "OTHER_PRODUCT_PHOTO_HKTVMALL", columnDefinition="text")
    private String otherProductPhotoHktvmall;
    @Column(name = "OTHER_PHOTO_HKTVMALL", columnDefinition="text")
    private String otherPhotoHktvmall;
    @Column(name = "STORE_STATUS")
    private String storeStatus;
    @Column(name = "ATTRIBUTES_ID")
    private Integer attributesId;
    @Column(name = "BRAND_ID")
    private Integer brandId;
    @Column(name = "DELIVERY_METHOD")
    private String deliveryMethod;
    @Column(name = "FIXED_DELIVERY_DATE")
    private Date fixedDeliveryDate;
    @Column(name = "FIXED_DELIVERY_TIMESLOT")
    private String fixedDeliveryTimeslot;
    @Column(name = "FLASH_SALE")
    private String flashSale;
    @Column(name = "INVOICE_REMARKS_CH")
    private String invoiceRemarksCh;
    @Column(name = "INVOICE_REMARKS_EN")
    private String invoiceRemarksEn;
    @Column(name = "IS_PRIMARY_SKU")
    private String isPrimarySku;
    @Column(name = "MALL_DOLLAR")
    private BigDecimal mallDollar;
    @Column(name = "MALL_DOLLAR_VIP")
    private BigDecimal mallDollarVip;
    @Column(name = "ONLINE_DATE")
    private Date onlineDate;
    @Column(name = "PICKUP_DAYS")
    private String pickupDays;
    @Column(name = "PICKUP_TIMESLOT")
    private String pickupTimeslot;
    @Column(name = "PRODUCT_READY_DAYS")
    private String productReadyDays;
    @Column(name = "PRODUCT_READY_METHOD")
    private String productReadyMethod;
    @Column(name = "READY_PICKUP_DAYS")
    private String readyPickupDays;
    @Column(name = "RECOM_SELLING_PRICE")
    private String recomSellingPrice;
    @Column(name = "RECOM_SELLING_PRICE_EN")
    private String recomSellingPriceEn;
    @Column(name = "RETURN_DAYS")
    private Integer returnDays;
    @Column(name = "RSP")
    private String rsp;
    @Column(name = "RSP_FONT_BAK_COLOR")
    private String rspFontBakColor;
    @Column(name = "RSP_FONT_COLOR")
    private String rspFontColor;
    @Column(name = "SKU_L_DESC_HKB_CH", columnDefinition="text")
    private String skuLDescHkbCh;
    @Column(name = "SKU_L_DESC_HKB_EN", columnDefinition="text")
    private String skuLDescHkbEn;
    @Column(name = "SKU_L_TITLE_HKB_CH")
    private String skuLTitleHkbCh;
    @Column(name = "SKU_L_TITLE_HKB_EN")
    private String skuLTitleHkbEn;
    @Column(name = "SKU_S_DESC_HKB_CH", columnDefinition="text")
    private String skuSDescHkbCh;
    @Column(name = "SKU_S_DESC_HKB_EN", columnDefinition="text")
    private String skuSDescHkbEn;
    @Column(name = "SKU_S_TITLE_HKB_CH")
    private String skuSTitleHkbCh;
    @Column(name = "SKU_S_TITLE_HKB_EN")
    private String skuSTitleHkbEn;
    @Column(name = "URGENT")
    private String urgent;
    @Column(name = "USER_MAX")
    private BigDecimal userMax;
    @Column(name = "WAREHOUSE_ID")
    private String warehouseId;
}
