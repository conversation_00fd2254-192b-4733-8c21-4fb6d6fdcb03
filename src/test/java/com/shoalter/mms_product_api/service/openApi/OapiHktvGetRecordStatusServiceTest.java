package com.shoalter.mms_product_api.service.openApi;

import com.shoalter.mms_product_api.config.product.OapiStatusCodeEnum;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractMerchantDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.openApi.helper.OapiHelper;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiResponseDto;
import com.shoalter.mms_product_api.service.product.CheckSaveProductRecordsStatusService;
import com.shoalter.mms_product_api.service.product.pojo.response.CheckSaveProductRecordsStatusMainResponseData;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.util.List;

@ExtendWith(MockitoExtension.class)
public class OapiHktvGetRecordStatusServiceTest {
	@InjectMocks
	private OapiHktvGetRecordStatusService oapiHktvGetRecordStatusService;

	@Mock
	private OapiHelper oapiHelper;

	@Mock
	private MessageSource messageSource;

	@Mock
	private CheckSaveProductRecordsStatusService checkSaveProductRecordsStatusService;


	private final static String TEST_STRING = "test";

	@Test
	public void start_validateFail_returnFail() {
		//Given
		Mockito.when(oapiHelper.validateStore(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ResponseDto.fail(null));

		//When
		OapiResponseDto<List<CheckSaveProductRecordsStatusMainResponseData>> result =
			oapiHktvGetRecordStatusService.start(TEST_STRING, TEST_STRING, TEST_STRING, null);

		//Then
		Assertions.assertThat(result.getCode()).isEqualTo(OapiStatusCodeEnum.FAIL.getCode());
	}

	@Test
	public void start_recordNotFound_returnFail() {
		//Given
		Mockito.when(oapiHelper.validateStore(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(ResponseDto.success(getStoreContractMerchantDo()));
		Mockito.when(checkSaveProductRecordsStatusService.start(Mockito.any()))
			.thenReturn(ResponseDto.success(null));
		Mockito.when(messageSource.getMessage(Mockito.anyString(), Mockito.any(), Mockito.any()))
			.thenReturn(TEST_STRING);

		//When
		OapiResponseDto<List<CheckSaveProductRecordsStatusMainResponseData>> result =
			oapiHktvGetRecordStatusService.start(TEST_STRING, TEST_STRING, TEST_STRING, null);

		//Then
		Assertions.assertThat(result.getCode()).isEqualTo(OapiStatusCodeEnum.FAIL.getCode());
	}

	@Test
	public void start_process_returnSuccess() {
		//Given
		Mockito.when(oapiHelper.validateStore(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(ResponseDto.success(getStoreContractMerchantDo()));
		Mockito.when(checkSaveProductRecordsStatusService.start(Mockito.any()))
			.thenReturn(ResponseDto.success(List.of(new CheckSaveProductRecordsStatusMainResponseData(1L, null, null))));

		//When
		OapiResponseDto<List<CheckSaveProductRecordsStatusMainResponseData>> result =
			oapiHktvGetRecordStatusService.start(TEST_STRING, TEST_STRING, TEST_STRING, null);

		//Then
		Assertions.assertThat(result.getCode()).isEqualTo(OapiStatusCodeEnum.SUCCESS.getCode());
	}

	private StoreContractMerchantDo getStoreContractMerchantDo() {
		return new StoreContractMerchantDo() {
			@Override
			public Integer getMerchantId() {
				return 0;
			}

			@Override
			public String getMerchantName() {
				return "";
			}

			@Override
			public Integer getStoreId() {
				return 0;
			}

			@Override
			public String getStoreCode() {
				return "";
			}

			@Override
			public String getStorefrontCode() {
				return "";
			}

			@Override
			public Integer getContractId() {
				return 0;
			}

			@Override
			public String getContractType() {
				return "";
			}
		};
	}

}
