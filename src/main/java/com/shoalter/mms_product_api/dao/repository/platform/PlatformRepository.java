package com.shoalter.mms_product_api.dao.repository.platform;

import com.shoalter.mms_product_api.dao.repository.platform.pojo.PlatformDo;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface PlatformRepository extends JpaRepository<PlatformDo, Integer> {

    @Query(value = "select p.PLATFORM_CODE from PLATFORM p inner join BUS_UNIT bu on p.ID = bu.PLATFORM_ID where bu.CODE = :buCode ",nativeQuery = true)
    Optional<String> getPlatformCodeByBuCode(@Param("buCode")String buCode);

    @Query(value = "select DISTINCT  p.PLATFORM_CODE  from USER_BUS_UNIT_PENDING ubup join BUS_UNIT bu on ubup.BUS_UNIT_ID = bu.ID join PLATFORM p on p.ID = bu.PLATFORM_ID where USER_ID = :userId ", nativeQuery = true)
    List<String> getPlatformCodeByUserId(@Param("userId") Integer userId);
}
