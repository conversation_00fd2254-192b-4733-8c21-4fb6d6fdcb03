package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdByLittleMallStoreRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckSkuIdResultDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class CheckSkuIdByStoreService {
	private final PermissionHelper permissionHelper;
	private final ProductMasterHelper productMasterHelper;
	private final MessageSource messageSource;

	public ResponseDto<Void> start(UserDto userDto, CheckSkuIdByLittleMallStoreRequestDto checkSkuIdByLittleMallStoreRequestDto) {
		// check sku Id exists or not
		CheckSkuIdResultDto checkSkuIdResultDto = productMasterHelper.requestCheckSkuIdByLittleMallStore(userDto, checkSkuIdByLittleMallStoreRequestDto);

		if (checkSkuIdResultDto == null) {
			log.error("Unable to request product master.");
			return ResponseDto.<Void>builder().status(-1).errorMessageList(List.of(messageSource.getMessage("message62",null,null))).build();
		}

		if (checkSkuIdResultDto.getExistsSkus().size() > 0) {
			String errorMessage = messageSource.getMessage("message66", new String[]{generateErrorMessage(checkSkuIdResultDto.getExistsSkus())}, null);
			return ResponseDto.<Void>builder().status(-1).errorMessageList(List.of(errorMessage)).build();
		} else {
			return ResponseDto.<Void>builder().status(1).build();
		}

	}

	private String generateErrorMessage(List<String> errorMessageList) {
		return String.join(", ", errorMessageList);
	}
}
