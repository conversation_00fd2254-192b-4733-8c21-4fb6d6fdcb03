package com.shoalter.mms_product_api.dao.repository.rabbit;

import java.util.Date;

import lombok.Data;
import javax.persistence.*;
@Entity
@Table(name = "RABBITVO_RETRY")
@Data
public class RabbitRetryDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID")
    private Integer id;
    @Column(name = "VODATA")
    private String vodata;
    @Column(name = "ROUTINGKEY")
    private String routingkey;
    @Column(name = "EXCHANGE")
    private String exchange;
    @Column(name = "VOTYPE")
    private String votype;
    @Column(name = "RETRYCOUNT")
    private Integer retrycount;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;
    @Column(name = "ACTIVATE")
    private String activate;
}
