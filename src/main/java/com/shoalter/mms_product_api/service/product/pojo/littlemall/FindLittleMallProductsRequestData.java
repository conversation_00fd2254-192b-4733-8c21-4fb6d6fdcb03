package com.shoalter.mms_product_api.service.product.pojo.littlemall;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
public class FindLittleMallProductsRequestData {
	private  Integer page;
	private  Integer size;
	private  Integer merchantId;
	private  String storeCode;
	private  List<String> category;
	private  List<String> skuIds;
	private  String productName;
	private  String productId;
	private  List<String> orderBy;
}
