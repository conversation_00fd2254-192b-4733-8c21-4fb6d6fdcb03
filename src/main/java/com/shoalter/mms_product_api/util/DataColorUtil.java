package com.shoalter.mms_product_api.util;


import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;// index 0 = colorEn = code(SYS_PARM)
// index 1 = colorCh = short_desc_tc(SYS_PARM)
public class DataColorUtil {
    private static String splitStr = "    ";

    //默認取用英文
    public static String getColorEnOrCh(String color) {
        if(StringUtil.isNullOrBlank(color))
            return null;
        return color.split(splitStr)[0];
    }
    public static String getColorEnOrCh(String color,Integer index) {
        if(StringUtil.isNullOrBlank(color))
            return null;
        try {
            return color.split(splitStr)[index];
        }
        catch (Exception e){
            return color.split(splitStr)[0];
        }
    }

	public static String getColorFormatForOldMms(SysParmDo sysParmDo) {
		return sysParmDo.getCode() + splitStr + sysParmDo.getShortDescTc();
	}
}
