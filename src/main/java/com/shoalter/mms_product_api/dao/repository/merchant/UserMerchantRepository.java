package com.shoalter.mms_product_api.dao.repository.merchant;

import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserMerchantDo;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserNameAndEmailViewDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface UserMerchantRepository extends JpaRepository<UserMerchantDo, Integer> {

	@Query(value =
		"SELECT su.ID AS userId, su.USER_NAME AS userName, su.EMAIL AS email "
		+ "FROM SYS_USER su "
		+ "JOIN CONTRACT c ON su.ID = RM_ID AND MASTER_CONTRACT_ID IS NULL "
		+ "JOIN STORE s ON c.STORE_ID = s.Id "
		+ "WHERE c.MERCHANT_ID = :merchantId "
		+ "AND s.STOREFRONT_STORE_CODE = :storefrontStoreCode", nativeQuery = true)
	List<UserNameAndEmailViewDo> findContractUsersByMerchantIdAndStorefrontCode(Integer merchantId, String storefrontStoreCode);

}
