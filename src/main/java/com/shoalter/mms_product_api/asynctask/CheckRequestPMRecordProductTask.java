package com.shoalter.mms_product_api.asynctask;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.ProductMasterBusinessUnitType;
import com.shoalter.mms_product_api.config.product.ProductMasterErrorTypeEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.schedule.CheckTimeSchedule;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.SaveProductRecordGroupHelper;
import com.shoalter.mms_product_api.service.product.helper.LittleMallVariantHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.helper.TaskExceptionHelper;
import com.shoalter.mms_product_api.service.product.helper.UserHelper;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditOverseaDeliveryRequestDetailDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductLittleMallFalttenSkusRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.shopline.ProductBatchImportHelper;
import com.shoalter.mms_product_api.util.BatchOverseaDeliveryUtil;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpMethod;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class CheckRequestPMRecordProductTask {

	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final ProductMasterHelper productMasterHelper;
	private final TaskExceptionHelper taskExceptionHelper;
	private final SaveProductRecordGroupHelper saveProductRecordGroupHelper;
	private final ProductBatchImportHelper productBatchImportHelper;
	private final UserHelper userHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final LittleMallVariantHelper littleMallVariantHelper;
	private final MessageSource messageSource;
	private final Gson gson;

	@Value("${product.process.size.limit}")
	private int productProcessSizeLimit;


	@Async("checkRequestPMRecordProductTaskExecutor")
	public void start(SaveProductRecordDo record) {
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();
		String status = SaveProductStatusEnum.getProductStatusName(SaveProductStatus.REQUESTING_PM);

		List<SaveProductRecordRowDo> waitSendPmList = saveProductRecordRowRepository.findByRecordIdAndStatus(record.getId(), SaveProductStatus.REQUESTING_PM);

		log.info("Task info run request product master record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", record.getId(),
			SaveProductTypeEnum.getProductTypeName(record.getUploadType()), waitSendPmList.size(), record.getUploadUserId(), status);
		handleRequestingProductMasterFlow(record, waitSendPmList);

		stopWatch.stop();
		log.info("total time for processing record id {} in {} status : {} millisecond", record.getId(), status, stopWatch.getTotalTimeMillis());
	}

	private void handleRequestingProductMasterFlow(SaveProductRecordDo record, List<SaveProductRecordRowDo> waitSendPmList) {
		try {
			CheckTimeSchedule.addProductRecordId(record.getId());
			List<String> traceIdFromRecords = record.getPmTractId() == null ? new ArrayList<>() : new ArrayList<>(Arrays.asList(record.getPmTractId().split(StringUtil.COMMA)));
			if (waitSendPmList.isEmpty()) {
				record.setStatus(SaveProductStatus.FAIL);
			} else {
				List<String> pmTractIds = new ArrayList<>();
				boolean needRetry = false;
				AtomicInteger partitionIndex = new AtomicInteger();
				AtomicInteger successCount = new AtomicInteger();
				AtomicInteger retryCount = new AtomicInteger();
				AtomicInteger failCount = new AtomicInteger();

				waitSendPmList = checkAndUpdateFailRecordRow(record, waitSendPmList);
				List<List<SaveProductRecordRowDo>> partitionList = splitRecordRowByVariantAndSize(record, waitSendPmList, productProcessSizeLimit);

				log.info("record id: {}, request product master total row size: {}, partition size: {}", record.getId(), waitSendPmList.size(), partitionList.size());
				for (List<SaveProductRecordRowDo> splitWaitSendPmListSplit : partitionList) {
					log.info("start request product master, record id: {}, partition index: {}, partition size: {}", record.getId(), partitionIndex.get(), splitWaitSendPmListSplit.size());

					if (CollectionUtil.isEmpty(splitWaitSendPmListSplit)) {
						log.info("splitWaitSendPmListSplit empty, record id: {}, upload type: {}", record.getId(), SaveProductTypeEnum.getProductTypeName(record.getUploadType()));
						continue;
					}
					ProductMasterResponseDto responseDto = requestSendProductToProductMaster(record, splitWaitSendPmListSplit);
					if (CollectionUtil.isEmpty(responseDto.getErrorMessageList())) {
						pmTractIds.add(responseDto.getTraceId());
						splitWaitSendPmListSplit.forEach(row -> {
							row.setStatus(SaveProductStatus.CHECKING_PM);
						});
						successCount.getAndAdd(splitWaitSendPmListSplit.size());
					} else if (responseDto.getErrorCode() != null && ProductMasterErrorTypeEnum.RETRY_ERRORS.contains(responseDto.getErrorCode())) {
						needRetry = true;
						retryCount.getAndAdd(splitWaitSendPmListSplit.size());
						log.error("record id: {}, partition index: {} receive retry error, PM error code: {}", record.getId(), partitionIndex.get(), responseDto.getErrorCode());
					} else {
						SaveProductRecordRowDo firstRow = splitWaitSendPmListSplit.get(0);
						firstRow.setErrorMessage(StringUtil.generateErrorMessage(responseDto.getErrorMessageList()));
						splitWaitSendPmListSplit.forEach(row -> {
							row.setStatus(SaveProductStatus.FAIL);
						});
						failCount.getAndAdd(splitWaitSendPmListSplit.size());
						log.error("record id: {}, partition index: {}, error message: {}, status: {}, PM error code: {}", record.getId(), partitionIndex.get(), StringUtil.generateErrorMessage(responseDto.getErrorMessageList()), SaveProductStatusEnum.getProductStatusName(SaveProductStatus.FAIL), responseDto.getErrorCode());
					}
					saveProductRecordRowRepository.saveAll(splitWaitSendPmListSplit);
					log.info("end request product master, record id: {}, partition index: {}", record.getId(), partitionIndex.getAndIncrement());
				}

				traceIdFromRecords.addAll(pmTractIds);
				if (!traceIdFromRecords.isEmpty()) {
					record.setPmTractId(String.join(StringUtil.COMMA, traceIdFromRecords));
				}

				if (!needRetry) {
					record.setStatus(SaveProductStatus.CHECKING_PM);
				}

				record.setCheckTime(null);
				log.info("record id: {}, request product master total row size: {}, success count: {}, retry count: {}, fail count: {}",
						record.getId(), waitSendPmList.size(), successCount.get(), retryCount.get(), failCount.get());
			}

			saveProductRecordRepository.save(record);
			saveProductRecordGroupHelper.finallyChangeGroupState(record.getGroupId());
		} catch (Exception e) {
			taskExceptionHelper.start(record, waitSendPmList, e);
		} finally {
			CheckTimeSchedule.removeProductRecordId(record.getId());
		}
	}

	public ProductMasterResponseDto requestSendProductToProductMaster(SaveProductRecordDo record, List<SaveProductRecordRowDo> waitSendPmList) {
		UserDto userDto = userHelper.generateUserDtoByRecord(record);
		ProductMasterResponseDto responseDto;
		switch (record.getUploadType()) {
			case SaveProductType.SINGLE_CREATE_PRODUCT:
			case SaveProductType.BATCH_CREATE_PRODUCT:
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL:
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL:
			case SaveProductType.SINGLE_CREATE_LITTLE_MALL_PRODUCT:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL:
			case SaveProductType.SINGLE_CREATE_BUNDLE:
				responseDto = requestProductMasterCreateProduct(userDto, record, waitSendPmList);
				break;
			case SaveProductType.SINGLE_EDIT_PRODUCT_PACKAGING_INFO:
			case SaveProductType.SINGLE_EDIT_PRODUCT:
			case SaveProductType.COMMISSION_RATE_APPROVE:
			case SaveProductType.BATCH_EDIT_PRODUCT:
			case SaveProductType.BATCH_EDIT_PRODUCT_COMMONLY_USED:
			case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT_COMMONLY_USED:
			case SaveProductType.SYNC_SAME_PRODUCT_CODE:
			case SaveProductType.BATCH_EDIT_STORE_ONLINE_STATUS:
			case SaveProductType.SYNC_OFFLINE_BUNDLE:
			case SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL:
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION:
			case SaveProductType.SINGLE_EDIT_LITTLE_MALL_PRODUCT:
			case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT:
			case SaveProductType.BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION:
			case SaveProductType.SINGLE_EDIT_BUNDLE:
			case SaveProductType.SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT:
			case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
			case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
			case SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY:
			case SaveProductType.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB:
			case SaveProductType.BATCH_EDIT_PRODUCT_READY_DAYS:
				responseDto = requestProductMasterEditProduct(userDto, record, waitSendPmList);
				break;
			case SaveProductType.BATCH_EDIT_OVERSEA_DELIVERY:
				responseDto = requestProductMasterEditProductByBatchEditOverseaDelivery(userDto, record, waitSendPmList);
				break;
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_INFO:
			case SaveProductType.BATCH_EDIT_DEPRECATED_PRODUCT_IMAGES:
			case SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE:
				responseDto = requestProductMasterEditPartialFieldsProduct(userDto, record, waitSendPmList);
				break;
			case SaveProductType.LITTLE_MALL_FLATTEN_PRODUCTS:
				responseDto = requestProductMasterFlattenLittleMallProduct(userDto, record, waitSendPmList);
				break;
			default:
				return ProductMasterResponseDto.fail(List.of(messageSource.getMessage("message21", new String[]{ErrorMessageTypeCode.UPLOAD_TYPE_NO_IMPLEMENT}, null)));
		}
		return responseDto;
	}

	@SuppressWarnings("java:S2259")
	private ProductMasterResponseDto requestProductMasterCreateProduct(UserDto userDto, SaveProductRecordDo record, List<SaveProductRecordRowDo> waitSendPmList) {
		List<String> skuList = new ArrayList<>();
		List<ProductMasterDto> productMasterDtoList = getEditProductMasterDtoList(waitSendPmList, skuList, record.getUploadType());
		SaveProductResultDto productMasterCreateProductResult = productMasterHelper.requestSaveProduct(userDto, productMasterDtoList, skuList.toString(), record);

		productBatchImportHelper.handleSpecialRecordTypeUploadedImages(record, userDto, waitSendPmList, productMasterCreateProductResult);

		List<String> errorMessageList = productMasterHelper.handleProductCreateResponse(productMasterCreateProductResult, record.getUploadType());

		return ProductMasterResponseDto.generate(productMasterCreateProductResult, errorMessageList);
	}

	@SuppressWarnings("java:S2259")
	private ProductMasterResponseDto requestProductMasterEditProduct(UserDto userDto, SaveProductRecordDo record, List<SaveProductRecordRowDo> waitSendPmList) {
		List<String> skuList = new ArrayList<>();
		List<ProductMasterDto> productMasterDtoList = getEditProductMasterDtoList(waitSendPmList, skuList, record.getUploadType());

		SaveProductResultDto productMasterEditProductResult = productMasterHelper.requestEditProduct(userDto, productMasterDtoList, skuList.toString(), HttpMethod.PUT, record);
		List<String> errorMessageList = productMasterHelper.handleProductCreateOrEditMasterResponse(productMasterEditProductResult, record.getUploadType());

		return ProductMasterResponseDto.generate(productMasterEditProductResult, errorMessageList);
	}

	private ProductMasterResponseDto requestProductMasterEditProductByBatchEditOverseaDelivery(UserDto userDto, SaveProductRecordDo record, List<SaveProductRecordRowDo> waitSendPmList) {
		List<String> uuidList = waitSendPmList.stream().map(SaveProductRecordRowDo::getUuid).collect(Collectors.toList());
		ProductMasterSearchRequestDto productMasterSearchRequestDto = ProductMasterSearchRequestDto.builder().uuids(uuidList).build();
		List<ProductMasterResultDto> beforeProductList = productMasterHelper.requestProductsByUuid(userDto, productMasterSearchRequestDto);
		if (beforeProductList.isEmpty()) {
			return ProductMasterResponseDto.fail(List.of(messageSource.getMessage("message10", new String[]{ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_BATCH_PRODUCTS_ERROR}, null)));
		}

		Map<String, ProductMasterResultDto> uuidMapProductMasterResultDto = beforeProductList.stream().collect(Collectors.toMap(ProductMasterResultDto::getUuid, productMasterResultDto -> productMasterResultDto));
		List<String> identifierList = new ArrayList<>();

		List<ProductMasterDto> productMasterDtoList = waitSendPmList
			.stream()
			.map(row -> {
				BatchEditOverseaDeliveryRequestDetailDto requestDto = gson.fromJson(row.getContent(), BatchEditOverseaDeliveryRequestDetailDto.class);
				ProductMasterResultDto beforeProduct = uuidMapProductMasterResultDto.get(row.getUuid());

				List<String> deliveryDistrictList = BatchOverseaDeliveryUtil.generateDeliveryDistrictList(requestDto,
					beforeProduct.getAdditional().getHktv().getDeliveryDistrict());
				List<String> updateBuList = generateBuList(beforeProduct);

				ProductMasterDto productMasterDto = ProductMasterDto.convertFromProductMasterResultDto(beforeProduct);
				productMasterDto.getAdditional().getHktv().setDeliveryDistrict(deliveryDistrictList);
				productMasterDto.setRecordRowId(row.getId());
				productMasterDto.setBuToSend(updateBuList);
				productMasterDto.setMmsModifiedTime(LocalDateTime.ofInstant(record.getUploadTime().toInstant(), ZoneId.systemDefault()));
				productMasterDto.setMmsModifiedUser(userDto.getUserCode());
				identifierList.add(row.getSku());

				return productMasterDto;
			})
			.collect(Collectors.toList());

		SaveProductResultDto productMasterCreateProductResult = productMasterHelper.requestEditProduct(userDto, productMasterDtoList, identifierList.toString(), HttpMethod.PUT, record);
		List<String> errorMessageList = productMasterHelper.handleProductCreateOrEditMasterResponse(productMasterCreateProductResult, record.getUploadType());

		return ProductMasterResponseDto.generate(productMasterCreateProductResult, errorMessageList);
	}

	private List<String> generateBuList(ProductMasterResultDto beforeProduct) {
		List<String> updateBuList = new ArrayList<>();
		if (beforeProduct.getAdditional().getHktv() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.HKTV);
		}
		if (beforeProduct.getAdditional().getThirdParty() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.THIRD_PL);
		}
		if (beforeProduct.getAdditional().getLittleMall() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.LITTLE_MALL);
		}
		if (beforeProduct.getAdditional().getIids() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.IIDS);
		}
		return updateBuList;
	}

	List<ProductMasterDto> getEditProductMasterDtoList(List<SaveProductRecordRowDo> waitSendPmList, List<String> skuList,
													   Integer recordUploadType) {
		return waitSendPmList.stream().map(
			row -> {
				SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);

				if (!SaveProductType.PATCH_EDIT_PRODUCT_SET.contains(recordUploadType) &&
					singleEditProductDto.getProduct().getAdditional().getHktv() != null) {
					resetMallDollar(singleEditProductDto);
				}

				if (SaveProductType.PATCH_EDIT_PRODUCT_CONTENT_SET.contains(recordUploadType)) {
					resetPatchEditProductContent(recordUploadType, singleEditProductDto);
				}

				String productJson;
				if (SaveProductType.MIGRATION_PRODUCT_TYPE_SET.contains(recordUploadType)) {
					productJson = gson.toJson(singleEditProductDto.getProductMigration().getModifiedProduct());
				} else {
					productJson = gson.toJson(singleEditProductDto.getProduct());
				}

				skuList.add(singleEditProductDto.getProduct().getSkuId());

				ProductMasterDto productMasterDto = gson.fromJson(productJson, ProductMasterDto.class);

				//don't give product master selling qty. TODO enhancement
				if (productMasterDto.getBundleSetting() != null) {
					productMasterDto.getBundleSetting().getMallInventoryInfo()
						.forEach(inventoryInfo -> inventoryInfo.setSettingQuantity(null));
				}
				return gson.fromJson(productJson, ProductMasterDto.class);
			}
		).collect(Collectors.toList());
	}

	private ProductMasterResponseDto requestProductMasterEditPartialFieldsProduct(UserDto userDto, SaveProductRecordDo record, List<SaveProductRecordRowDo> waitSendPmList) {
		List<String> skuList = new ArrayList<>();
		List<ProductMasterDto> productMasterDtoList = getEditProductMasterDtoList(waitSendPmList, skuList, record.getUploadType());

		SaveProductResultDto productMasterEditProductResult = productMasterHelper.requestEditProduct(userDto, productMasterDtoList, skuList.toString(), HttpMethod.PATCH, record);
		List<String> errorMessageList = productMasterHelper.handleProductCreateOrEditMasterResponse(productMasterEditProductResult, record.getUploadType());

		return ProductMasterResponseDto.generate(productMasterEditProductResult, errorMessageList);
	}

	private List<SaveProductRecordRowDo> checkAndUpdateFailRecordRow(SaveProductRecordDo record, List<SaveProductRecordRowDo> waitSendPmList) {
		List<SaveProductRecordRowDo> failedList = new ArrayList<>();
		List<SaveProductRecordRowDo> keepProcessingList = new ArrayList<>();

		if (SaveProductType.CHECK_LITTLE_MALL_VARIANT_SET.contains(record.getUploadType())) {
			Pair<List<SaveProductRecordRowDo>, List<SaveProductRecordRowDo>> checkLittleMallResult = littleMallVariantHelper.checkAndUpdateFailRecordRow(waitSendPmList, record);
			failedList.addAll(checkLittleMallResult.getLeft());
			keepProcessingList.addAll(checkLittleMallResult.getRight());
		} else {
			Map<Long, SaveProductRecordRowDo> recordRowIdMap = waitSendPmList.stream().collect(Collectors.toMap(SaveProductRecordRowDo::getId, Function.identity()));
			Map<Long, Set<Long>> dependRecordRowIdMap = saveProductRecordRowHelper.findDependRecordRowIdMap(record, recordRowIdMap.keySet());

			recordRowIdMap.forEach((processingRowId, recordRow) -> {
				if (!dependRecordRowIdMap.containsKey(processingRowId)) {
					keepProcessingList.add(recordRow);
				} else {
					Set<Long> dependRecordRowId = dependRecordRowIdMap.get(processingRowId);
					if (dependRecordRowId.stream().anyMatch(rowId -> !recordRowIdMap.containsKey(rowId))) {
						recordRow.setStatus(SaveProductStatus.FAIL);
						recordRow.setErrorMessage(messageSource.getMessage("message337", null, null));
						failedList.add(recordRow);
					} else {
						keepProcessingList.add(recordRow);
					}
				}
			});
		}

		if (!failedList.isEmpty()) {
			saveProductRecordRowRepository.saveAll(failedList);
		}

		return keepProcessingList;
	}

	private List<List<SaveProductRecordRowDo>> splitRecordRowByVariantAndSize(SaveProductRecordDo record, List<SaveProductRecordRowDo> waitSendPmList, int size) {
		if (SaveProductType.LITTLE_MALL_FLATTEN_PRODUCTS == record.getUploadType()) {
			return ListUtils.partition(waitSendPmList, size);
		}

		if (!SaveProductType.HKTV_TYPE_SET.contains(record.getUploadType())) {
			return List.of(waitSendPmList);
		}

		List<List<SaveProductRecordRowDo>> result = new ArrayList<>();
		Map<String, List<SaveProductRecordRowDo>> variantProductMap = new HashMap<>();
		for (SaveProductRecordRowDo saveProductRecordRow : waitSendPmList) {
			SingleEditProductDto singleEditProductDto = gson.fromJson(saveProductRecordRow.getContent(), SingleEditProductDto.class);

			//if product cannot convert, skip variant partition
			if (singleEditProductDto.getProduct() == null) {
				return ListUtils.partition(waitSendPmList, size);
			}
			String storeAndProductId = singleEditProductDto.getProduct().getAdditional().getHktv().getStores() + StringUtil.UNDERLINE + singleEditProductDto.getProduct().getProductId();
			variantProductMap.putIfAbsent(storeAndProductId, new ArrayList<>());
			variantProductMap.get(storeAndProductId).add(saveProductRecordRow);
		}

		//variant product must send together
		List<SaveProductRecordRowDo> partitionList = new ArrayList<>();
		for (List<SaveProductRecordRowDo> list : variantProductMap.values()) {
			if (partitionList.size() + list.size() <= size) {
				partitionList.addAll(list);
			} else {
				result.add(partitionList);
				partitionList = new ArrayList<>(list);
			}
		}

		if (!partitionList.isEmpty()) {
			result.add(partitionList);
		}

		return result;
	}

	private void resetMallDollar(SingleEditProductDto singleEditProductDto) {
		if (singleEditProductDto.getProduct().getAdditional().getHktv().getMallDollar() == null) {
			singleEditProductDto.getProduct().getAdditional().getHktv().setMallDollar(BigDecimal.ZERO);
		}
		if (singleEditProductDto.getProduct().getAdditional().getHktv().getVipMallDollar() == null) {
			singleEditProductDto.getProduct().getAdditional().getHktv().setVipMallDollar(BigDecimal.ZERO);
		}
	}

	private void resetPatchEditProductContent(Integer recordUploadType, SingleEditProductDto singleEditProductDto) {
		ProductMasterDto newProductMasterDto = generatePatchEditProductDto(singleEditProductDto.getProduct());
		HktvProductDto oldHktvProductDto = singleEditProductDto.getProduct().getAdditional().getHktv();
		HktvProductDto partialHktvProductDto = new HktvProductDto();

		switch (recordUploadType) {
			//temporary disabled this patch type (MS-7171)
			case SaveProductType.SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT:
				partialHktvProductDto.setMallDollar(oldHktvProductDto.getMallDollar());
				partialHktvProductDto.setVipMallDollar(oldHktvProductDto.getVipMallDollar());
				break;
			//temporary disabled this patch type (MS-7171)
			case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
				partialHktvProductDto.setOnlineStatus(oldHktvProductDto.getOnlineStatus());
				break;
			//temporary disabled this patch type (MS-7171)
			case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
				newProductMasterDto.setOriginalPrice(singleEditProductDto.getProduct().getOriginalPrice());
				partialHktvProductDto.setSellingPrice(oldHktvProductDto.getSellingPrice());
				partialHktvProductDto.setStyle(oldHktvProductDto.getStyle());
				partialHktvProductDto.setDiscountTextEn(oldHktvProductDto.getDiscountTextEn());
				partialHktvProductDto.setDiscountTextCh(oldHktvProductDto.getDiscountTextCh());
				singleEditProductDto.getProduct().getAdditional().setHktv(partialHktvProductDto);
				break;
			//temporary disabled this patch type (MS-7171)
			case SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY:
				partialHktvProductDto.setVisibility(oldHktvProductDto.getVisibility());
				break;
			case SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE:
				newProductMasterDto.setSkuNameSc(singleEditProductDto.getProduct().getSkuNameSc());
				partialHktvProductDto.setSkuShortDescriptionSc(oldHktvProductDto.getSkuShortDescriptionSc());
				partialHktvProductDto.setSkuLongDescriptionSc(oldHktvProductDto.getSkuLongDescriptionSc());
				partialHktvProductDto.setPackingSpecSc(oldHktvProductDto.getPackingSpecSc());
				partialHktvProductDto.setInvoiceRemarksSc(oldHktvProductDto.getInvoiceRemarksSc());
				partialHktvProductDto.setFinePrintSc(oldHktvProductDto.getFinePrintSc());
				partialHktvProductDto.setVideoLinkTextSc(oldHktvProductDto.getVideoLinkTextSc());
				partialHktvProductDto.setVideoLinkTextSc2(oldHktvProductDto.getVideoLinkTextSc2());
				partialHktvProductDto.setVideoLinkTextSc3(oldHktvProductDto.getVideoLinkTextSc3());
				partialHktvProductDto.setVideoLinkTextSc4(oldHktvProductDto.getVideoLinkTextSc4());
				partialHktvProductDto.setVideoLinkTextSc5(oldHktvProductDto.getVideoLinkTextSc5());
				partialHktvProductDto.setClaimLinkSc(oldHktvProductDto.getClaimLinkSc());
				partialHktvProductDto.setWarrantySupplierSc(oldHktvProductDto.getWarrantySupplierSc());
				partialHktvProductDto.setWarrantyRemarkSc(oldHktvProductDto.getWarrantyRemarkSc());
				partialHktvProductDto.setServiceCentreAddressSc(oldHktvProductDto.getServiceCentreAddressSc());
				break;
		}
		newProductMasterDto.getAdditional().setHktv(partialHktvProductDto);
		singleEditProductDto.setProduct(newProductMasterDto);
	}

	private ProductMasterDto generatePatchEditProductDto(ProductMasterDto oldProductMasterDto) {
		ProductMasterDto newProductMasterDto = new ProductMasterDto();
		newProductMasterDto.setBuToSend(oldProductMasterDto.getBuToSend());
		newProductMasterDto.setUuid(oldProductMasterDto.getUuid());
		newProductMasterDto.setRecordRowId(oldProductMasterDto.getRecordRowId());
		newProductMasterDto.setAdditional(oldProductMasterDto.getAdditional());
		return newProductMasterDto;
	}

	private ProductMasterResponseDto requestProductMasterFlattenLittleMallProduct(UserDto userDto, SaveProductRecordDo record, List<SaveProductRecordRowDo> waitSendPmList) {
		List<ProductLittleMallFalttenSkusRequestDto> request = waitSendPmList.stream()
			.map(recordRow -> ProductLittleMallFalttenSkusRequestDto.builder()
				.uuid(recordRow.getUuid())
				.recordRowId(recordRow.getId())
				.build())
			.collect(Collectors.toList());
		SaveProductResultDto productMasterEditProductResult = productMasterHelper.requestLittleMallFlattenSkusByStores(userDto, request);
		List<String> errorMessageList = productMasterHelper.handleProductCreateOrEditMasterResponse(productMasterEditProductResult, record.getUploadType());

		return ProductMasterResponseDto.generate(productMasterEditProductResult, errorMessageList);
	}
}
