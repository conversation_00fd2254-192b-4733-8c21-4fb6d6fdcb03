package com.shoalter.mms_product_api.service.product.pojo.hybris;

import com.shoalter.mms_product_api.config.hybris.HybrisAction;
import com.shoalter.mms_product_api.service.product.mapper.HybrisActionDtoMapper;
import com.shoalter.mms_product_api.service.product.pojo.SaveHybrisProductDto;

public enum HybrisActionMapper {
	PRODUCT_SYNC_MODE_SIMPLIFIED_CHINESE(HybrisAction.PRODUCT_SYNC_MODE_SIMPLIFIED_CHINESE) {
		@Override
		public Object mapToActionObject(SaveHybrisProductDto dto) {
			return HybrisActionDtoMapper.INSTANCE.toSimplifiedChineseActionDto(dto);
		}
	},
	DEFAULT_SYNC_ALL(null) {
		@Override
		public Object mapToActionObject(SaveHybrisProductDto dto) {
			return dto;
		}
	};

	final String action;

	HybrisActionMapper(String action) {
		this.action = action;
	}

	public abstract Object mapToActionObject(SaveHybrisProductDto dto);

	public static HybrisActionMapper getActionMapper(String action) {
		if (action == null) {
			return DEFAULT_SYNC_ALL;
		}
		for (HybrisActionMapper mapper : values()) {
			if (mapper != DEFAULT_SYNC_ALL && action.equals(mapper.action)) {
				return mapper;
			}
		}
		return DEFAULT_SYNC_ALL;
	}
}
