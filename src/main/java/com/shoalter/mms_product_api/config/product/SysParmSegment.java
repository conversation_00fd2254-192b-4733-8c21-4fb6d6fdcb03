package com.shoalter.mms_product_api.config.product;

@SuppressWarnings("unused")
public class SysParmSegment {
	private SysParmSegment(){}
	public static final String ACCEPT_REASON = "ACCEPT_REASON";
	public static final String ADDITIONAL_OFFER = "ADDITIONAL_OFFER";
	public static final String ADJUSTMENT_TYPE = "ADJUSTMENT_TYPE";
	public static final String ANNUAL_FEE_BY_CONTRACT_TYPE = "ANNUAL_FEE_BY_CONTRACT_TYPE";
	public static final String ANNUAL_INSURANCE = "ANNUAL_INSURANCE";
	public static final String AUTO_APPROVE_CONTRACT_FOR_FIN = "AUTO_APPROVE_CONTRACT_FOR_FIN";
	public static final String BUYSELL_KEY = "BUYSELL_KEY";
	public static final String BUYSELL_MERCHANT = "BUYSELL_MERCHANT";
	public static final String BUYSELL_MERCHANT_DATA_LIMIT = "BUYSELL_MERCHANT_DATA_LIMIT";
	public static final String CAMPAIGN_TYPE = "CAMPAIGN_TYPE";
	public static final String CATEGORY_FOR_GOODS_TYPE = "CATEGORY_FOR_GOODS_TYPE";
	public static final String CATEGORY_FOR_REMOVAL_SERVICE = "CATEGORY_FOR_REMOVAL_SERVICE";
	public static final String CATEGORY_RESTRICTION = "CATEGORY_RESTRICTION";
	public static final String COLOR = "COLOR";
	public static final String COLOR_FAMILIES = "COLOR_FAMILIES";
	public static final String CONNECTED_MODE = "CONNECTED_MODE";
	public static final String CONTACT_URL = "CONTACT_URL";
	public static final String CONTRACT_RENEW = "CONTRACT_RENEW";
	public static final String CONTRACT_RENEW_OPTION = "CONTRACT_RENEW_OPTION";
	public static final String CONTRACT_RUNNING_NO = "CONTRACT_RUNNING_NO";
	public static final String CONTRACT_TERMINATE_REASON = "CONTRACT_TERMINATE_REASON";
	public static final String COUNTRY_OF_ORIGIN = "COUNTRY_OF_ORIGIN";
	public static final String CREDIT_CARD_WHITELISTED = "CREDIT_CARD_WHITELISTED";
	public static final String CURRENCY = "CURRENCY";
	public static final String DEFAULT_ANNUAL_FEE = "DEFAULT_ANNUAL_FEE";
	public static final String DELIVERY_COMPLETION_DAYS = "DELIVERY_COMPLETION_DAYS";
	public static final String DELIVERY_METHOD = "DELIVERY_METHOD";
	public static final String DOC_TYPE = "DOC_TYPE";
	public static final String ENABLE_DATA_BANK = "ENABLE_DATA_BANK";
	public static final String ENABLE_NEW_INVENTORY_SYSTEM = "ENABLE_NEW_INVENTORY_SYSTEM";
	public static final String ENABLE_QUESTIONNAIRE = "ENABLE_QUESTIONNAIRE";
	public static final String ENABLE_SUPERFOLLOW_FOR_PCR = "ENABLE_SUPERFOLLOW_FOR_PCR";
	public static final String EXCHANGE_ACC_EMAIL = "EXCHANGE_ACC_EMAIL";
	public static final String EXCHANGE_RATE = "EXCHANGE_RATE";
	public static final String EXCHANGE_RATE_FOR_OS_PCR = "EXCHANGE_RATE_FOR_OS_PCR";
	public static final String GOOGLE_DATA_STUDIO = "GOOGLE_DATA_STUDIO";
	public static final String GRADE_MERCHANT_REPORT_EMAIL = "GRADE_MERCHANT_REPORT_EMAIL";
	public static final String INTERNATIONAL_CODE = "INTERNATIONAL_CODE";
	public static final String IS_ENABLE_PILOT_LAUNCH = "IS_ENABLE_PILOT_LAUNCH";
	public static final String LANDMARK_CAT_CODE = "LANDMARK_CAT_CODE";
	public static final String LOGIN_SETTING = "LOGIN_SETTING";
	public static final String LOW_MARGIN = "LOW_MARGIN";
	public static final String MALL_DOLLAR = "MALL_DOLLAR";
	public static final String MERCHANT_CHAT_WHITELISTED = "MERCHANT_CHAT_WHITELISTED";
	public static final String MERCHANT_DEPARTMENT = "MERCHANT_DEPARTMENT";
	public static final String MERCHANT_TYPE = "MERCHANT_TYPE";
	public static final String MERCHANT_TYPE_A_CONTRACT_CODE = "MERCHANT_TYPE_A_CONTRACT_CODE";
	public static final String MERCHANT_TYPE_B_CONTRACT_CODE = "MERCHANT_TYPE_B_CONTRACT_CODE";
	public static final String MERCTEST = "MERCTEST";
	public static final String MESSAGE_RETRY_COUNT = "MESSAGE_RETRY_COUNT";
	public static final String MIX_AND_MATCH_LABEL_COLOR = "MIX_AND_MATCH_LABEL_COLOR";
	public static final String NON_BUSINESS_DAY = "NON_BUSINESS_DAY";
	public static final String NOWHERE = "NOWHERE";
	public static final String NOWHERE2 = "NOWHERE2";
	public static final String OFFER_SUBMISSION_EXPOSURE_CHAN = "OFFER_SUBMISSION_EXPOSURE_CHAN";
	public static final String OPEN_API_GET_ORDERS = "OPEN_API_GET_ORDERS";
	public static final String OPEN_API_INPUT_COUNT = "OPEN_API_INPUT_COUNT";
	public static final String OPEN_API_SKIP_AUTH = "OPEN_API_SKIP_AUTH";
	public static final String OVERSEA_DELIVERY = "OVERSEA_DELIVERY";
	public static final String OVERSEA_METHOD = "OVERSEA_METHOD";
	public static final String PACK_BOX_TYPE = "PACK_BOX_TYPE";
	public static final String PACK_BOX_TYPE_3PL_SKU = "PACK_BOX_TYPE_3PL_SKU";
	public static final String PAYMENT = "PAYMENT";
	public static final String PAYMENT_GROUP = "PAYMENT_GROUP";
	public static final String PAYMENT_TERM = "PAYMENT_TERM";
	public static final String PICKUP_DAYS = "PICKUP_DAYS";
	public static final String PICKUP_DAYS_DEFAULT = "PICKUP_DAYS_DEFAULT";
	public static final String PICKUP_TIMESLOT = "PICKUP_TIMESLOT";
	public static final String PRIMARY_CAT_LIST = "PRIMARY_CAT_LIST";
	public static final String PRODUCT_FIELD = "PRODUCT_FIELD";
	public static final String PRODUCT_FIELD_VALUE = "PRODUCT_FIELD_VALUE";
	public static final String PRODUCT_PROMOTION = "PRODUCT_PROMOTION";
	public static final String PRODUCT_READY_DAYS = "PRODUCT_READY_DAYS";
	public static final String PRODUCT_READY_DAYS_DEFAULT = "PRODUCT_READY_DAYS_DEFAULT";
	public static final String PRODUCT_READY_METHOD = "PRODUCT_READY_METHOD";
	public static final String PRODUCT_READY_METHOD_WAREHOUSE = "PRODUCT_READY_METHOD_WAREHOUSE";
	public static final String PRODUCT_TYPE = "PRODUCT_TYPE";
	public static final String PROMOTION_CONJOB_MULTI_THREAD = "PROMOTION_CONJOB_MULTI_THREAD";
	public static final String PROMOTION_DISCOUNT = "PROMOTION_DISCOUNT";
	public static final String QUESTIONNAIRE_TYPE = "QUESTIONNAIRE_TYPE";
	public static final String REJECT_REASON = "REJECT_REASON";
	public static final String REJECT_REASON_FOR_ANNUAL_FEE = "REJECT_REASON_FOR_ANNUAL_FEE";
	public static final String REQUEST_LIMIT = "REQUEST_LIMIT";
	public static final String RETURN_DAYS = "RETURN_DAYS";
	public static final String REVIEW_STATUS = "REVIEW_STATUS";
	public static final String RM_DEPARTMENT = "RM_DEPARTMENT";
	public static final String RM_GROUP = "RM_GROUP";
	public static final String SIZE = "SIZE";
	public static final String SIZE_SYSTEM = "SIZE_SYSTEM";
	public static final String SMALL_MERCHANT_CONTRACT = "SMALL_MERCHANT_CONTRACT";
	public static final String STAFF_ACC_EMAIL = "STAFF_ACC_EMAIL";
	public static final String STORAGE_TEMPERATURE = "STORAGE_TEMPERATURE";
	public static final String STORAGE_TYPE = "STORAGE_TYPE";
	public static final String STORE_ACCESS_TERMINATE = "STORE_ACCESS_TERMINATE";
	public static final String STORE_CODE_BLACK_LIST = "STORE_CODE_BLACK_LIST";
	public static final String TEST = "TEST";
	public static final String TRAINING_GUIDE_TYPES = "TRAINING_GUIDE_TYPES";
	public static final String USERSTORE_PILOT_LAUNCH = "USERSTORE_PILOT_LAUNCH";
	public static final String VIRTUAL_STORE_MERCHANT = "VIRTUAL_STORE_MERCHANT";
	public static final String VOUCHER_DISPLAY_TYPE = "VOUCHER_DISPLAY_TYPE";
	public static final String VOUCHER_TEMPLATE_TYPE = "VOUCHER_TEMPLATE_TYPE";
	public static final String VOUCHER_TYPE = "VOUCHER_TYPE";
	public static final String WEIGHT_LIMIT_FOR_PACKAGE = "WEIGHT_LIMIT_FOR_PACKAGE";
	public static final String WH_ID_STORAGE_TYPE_MAPPING = "WH_ID_STORAGE_TYPE_MAPPING";
	public static final String PRODUCT_MANAGEMENT_SETTING = "PRODUCT_MANAGEMENT_SETTING";
	public static final String PRODUCT_MANAGEMENT_WHITELIST = "PRODUCT_MANAGEMENT_WHITELIST";
	public static final String PRICE_ALERT_PRICE_DIFF_RATE = "PRICE_ALERT_PRICE_DIFF_RATE";

	public static final String PRICE_MONITOR = "PRICE_MONITOR";
}
