package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.config.security.xss.XssStringDeserializer;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class LittleMallProductDto implements Serializable {

	@JsonProperty("is_primary_sku")
	@SerializedName("is_primary_sku")
	private Boolean isPrimarySku;
	private Boolean visibility;
	@JsonProperty("online_status")
	@SerializedName("online_status")
	private Boolean onlineStatus;
	@JsonProperty("product_ready_method")
	@SerializedName("product_ready_method")
	private String productReadyMethod;
	@JsonProperty("store_category")
	@SerializedName("store_category")
	private List<String> storeCategory;
	@JsonProperty("display_in_hktvmall_category")
	@SerializedName("display_in_hktvmall_category")
	private List<String> displayInHktvmallCategory;
	@JsonProperty("hashtags")
	@SerializedName("hashtags")
	private List<String> hashtags;
	@JsonProperty("sku_long_description_ch")
	@SerializedName("sku_long_description_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuLongDescriptionCh;
	@JsonProperty("selling_price")
	@SerializedName("selling_price")
	private BigDecimal sellingPrice;
	@JsonProperty("main_photo")
	@SerializedName("main_photo")
	private String mainPhoto;
	@JsonProperty("other_photo")
	@SerializedName("other_photo")
	private List<String> otherPhoto;
	@JsonProperty("variant_product_photo")
	@SerializedName("variant_product_photo")
	private List<String> variantProductPhoto;
	@JsonProperty("store_code")
	@SerializedName("store_code")
	private String storeCode;
	@JsonProperty("store_sku_id")
	@SerializedName("store_sku_id")
	private String storeSkuId;
	@JsonProperty("product1_field")
	@SerializedName("product1_field")
	private Integer productField1;
	@JsonProperty("product1_field_category")
	@SerializedName("product1_field_category")
	private Integer productFieldCategory1;
	@JsonProperty("product1_field_option")
	@SerializedName("product1_field_option")
	private Integer productFieldOption1;
	@JsonProperty("product2_field")
	@SerializedName("product2_field")
	private Integer productField2;
	@JsonProperty("product2_field_category")
	@SerializedName("product2_field_category")
	private Integer productFieldCategory2;
	@JsonProperty("product2_field_option")
	@SerializedName("product2_field_option")
	private Integer productFieldOption2;
	@JsonProperty("product3_field")
	@SerializedName("product3_field")
	private Integer productField3;
	@JsonProperty("product3_field_category")
	@SerializedName("product3_field_category")
	private Integer productFieldCategory3;
	@JsonProperty("product3_field_option")
	@SerializedName("product3_field_option")
	private Integer productFieldOption3;
	@JsonProperty("product4_field")
	@SerializedName("product4_field")
	private Integer productField4;
	@JsonProperty("product4_field_category")
	@SerializedName("product4_field_category")
	private Integer productFieldCategory4;
	@JsonProperty("product4_field_option")
	@SerializedName("product4_field_option")
	private Integer productFieldOption4;

}
