package com.shoalter.mms_product_api.exception;


import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.OapiStatusCodeEnum;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;

@RequiredArgsConstructor
@Slf4j
@RestControllerAdvice
public class ExceptionAdvice {

	private final MessageSource messageSource;

	@ExceptionHandler(SystemI18nException.class)
	public final ResponseDto<Void> systemI18nException(SystemI18nException e) {
		String message = messageSource.getMessage(e.getI18nCode(), e.getArgs(), e.getLocale());
		log.error("exception: {}", message);
		return ResponseDto.<Void>builder().status(-1).errorMessageList(List.of(message)).build();
	}

	@ExceptionHandler(NoDataException.class)
	public final HttpEntity<ResponseDto<Void>> noDataException() {
		log.error("exception: {}", "No Data");
		ResponseDto<Void> responseDto = ResponseDto.<Void>builder().status(-1).errorMessageList(List.of("No Data")).build();
		return new ResponseEntity<>(responseDto, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(BadRequestException.class)
	public final HttpEntity<ResponseDto<Void>> badRequestException(BadRequestException e) {
		log.error("exception: {}", e.getMessage());
		ResponseDto<Void> responseDto = ResponseDto.<Void>builder().status(-1).errorMessageList(List.of(e.getMessage())).build();
		return new ResponseEntity<>(responseDto, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(RetryException.class)
	public final HttpEntity<ResponseDto<Void>> retryException(RetryException e) {
		log.error("retry exception: {}", e.getMessage());
		ResponseDto<Void> responseDto = ResponseDto.<Void>builder().status(-1).errorMessageList(List.of(e.getMessage())).build();
		return new ResponseEntity<>(responseDto, HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler({
			SystemException.class,
			HttpMediaTypeNotSupportedException.class,
			MissingServletRequestParameterException.class,
			HttpMessageNotReadableException.class,
			HttpRequestMethodNotSupportedException.class,
			AccessDeniedException.class
	})
	public final ResponseDto<Void> systemException(Exception e) {
		log.error("exception: {}", e.getMessage());
		return ResponseDto.<Void>builder().status(-1).errorMessageList(List.of(e.getMessage())).build();
	}

	@ExceptionHandler({Exception.class})
	public final HttpEntity<ResponseDto<Void>> exception(Exception e) {
		String time = String.valueOf(System.currentTimeMillis());
		log.error("error time: {}", time);
		log.error("exception: {}", e.getMessage(), e);
		ResponseDto<Void> responseDto = ResponseDto.<Void>builder().status(-1).errorMessageList(List.of(messageSource.getMessage("message129", new Object[]{ErrorMessageTypeCode.SERVICE_EXCEPTION, time}, null))).build();
		return new ResponseEntity<>(responseDto, HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@ExceptionHandler(OapiException.class)
	public final HttpEntity<OapiResponseDto<?>> oapiException(OapiException e) {
		log.error("exception: {}", e.getMessage());
		OapiResponseDto<?> responseDto = OapiResponseDto.builder()
			.message(e.getMessage())
			.code(OapiStatusCodeEnum.FAIL.getCode())
			.data(e.getData())
			.build();
		return new ResponseEntity<>(responseDto, HttpStatus.BAD_REQUEST);
	}
}
