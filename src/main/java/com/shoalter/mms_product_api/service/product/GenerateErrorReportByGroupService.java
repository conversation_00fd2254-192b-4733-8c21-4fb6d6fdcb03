package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.template.BatchEditOverseaDeliveryTemplateHelperColumnEnum;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordGroupRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordGroupDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.BadRequestException;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.BatchEditOverseaDeliveryTemplateHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.context.MessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.stream.Collectors;

import static com.shoalter.mms_product_api.config.product.SaveProductType.BATCH_EDIT_OVERSEA_DELIVERY;

@RequiredArgsConstructor
@Service
@Slf4j
public class GenerateErrorReportByGroupService extends AbstractReport {

    private final BatchEditOverseaDeliveryTemplateHelper batchEditOverseaDeliveryTemplateHelper;

    private final SaveProductRecordGroupRepository saveProductRecordGroupRepository;
    private final SaveProductRecordRepository saveProductRecordRepository;
    private final SaveProductRecordRowRepository saveProductRecordRowRepository;

    private final MessageSource messageSource;


    public HttpEntity<ByteArrayResource> start(UserDto userDto, Long groupId) {
        SaveProductRecordGroupDo groupDo = saveProductRecordGroupRepository.findById(groupId).orElseThrow(() -> new BadRequestException(messageSource.getMessage("message138", null, null)));

        Workbook workbook;
        if (groupDo.getUploadType() == BATCH_EDIT_OVERSEA_DELIVERY) {
            workbook = generateBatchEditOverseaDeliveryReport(userDto, groupDo);
        } else {
            throw new BadRequestException(messageSource.getMessage("message75", null, null));
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
            workbook.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return new ResponseEntity<>(new ByteArrayResource(outputStream.toByteArray()), getResponseHeader(groupDo), HttpStatus.OK);
    }

    private Workbook generateBatchEditOverseaDeliveryReport(UserDto userDto, SaveProductRecordGroupDo groupDo) {
        checkUserRole(userDto, groupDo.getUploadType());

        Workbook workbook = batchEditOverseaDeliveryTemplateHelper.start();
        List<SaveProductRecordDo> recordDoList = saveProductRecordRepository.findByGroupId(groupDo.getId());
        List<Long> recordIdList = recordDoList.stream().map(SaveProductRecordDo::getId).collect(Collectors.toList());
        List<SaveProductRecordRowDo> rowDoList = saveProductRecordRowRepository.findErrorRowByRecordIdList(recordIdList);

        addErrorColumn(workbook, BatchEditOverseaDeliveryTemplateHelperColumnEnum.values().length, false, 70, true);
        batchEditOverseaDeliveryTemplateHelper.addBodyColumn(workbook, rowDoList);

        return workbook;
    }

    private void checkUserRole(UserDto userDto, Integer uploadType) {
        if (uploadType == BATCH_EDIT_OVERSEA_DELIVERY) {
            if (RoleCode.ALLOW_OVERSEA_DELIVERY_ROLES.contains(userDto.getRoleCode())) {
                return;
            }
            throw new SystemI18nException("message131");
        }
    }

    public String getFileName(SaveProductRecordGroupDo groupDo) {
        if (groupDo.getUploadType() == BATCH_EDIT_OVERSEA_DELIVERY) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            String uploadTime = dateFormat.format(groupDo.getCreateTime());
            return String.format("%s_error_report_%s.xlsx", groupDo.getFileName(), uploadTime);
        }
        return "error_report.xlsx";
    }

    public HttpHeaders getResponseHeader(SaveProductRecordGroupDo groupDo) {
        HttpHeaders header = new HttpHeaders();
        header.setContentType(new MediaType("application", "octet-stream"));
        header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + getFileName(groupDo));
        return header;
    }
}
