package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.dao.repository.contract.ContractProdTermsRepository;
import com.shoalter.mms_product_api.dao.repository.contract.ContractRepository;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ContractTermsNamesDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
public class LookupContractTermsNamesService {

    private final ContractProdTermsRepository contractProdTermsRepository;
	private final ContractRepository contractRepository;

    public ResponseDto<ContractTermsNamesDto> start(Integer contractId, Integer storeId) {
    	List<ContractDo> supplementaryContractList = contractRepository.findSupplementaryContractList(contractId, new Date());
		Integer lookupContractId = contractId;
		if (CollectionUtil.isNotEmpty(supplementaryContractList)) {
			lookupContractId = supplementaryContractList.get(0).getId();
		}
        return ResponseDto.<ContractTermsNamesDto>builder()
				.status(1)
                .data(ContractTermsNamesDto.builder().termsNames(contractProdTermsRepository.getInsuranceContractTermsNameList(lookupContractId, storeId)).build())
                .build();
    }
}
