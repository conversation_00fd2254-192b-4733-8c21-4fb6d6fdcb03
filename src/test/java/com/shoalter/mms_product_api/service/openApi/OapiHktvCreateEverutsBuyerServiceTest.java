package com.shoalter.mms_product_api.service.openApi;

import com.shoalter.mms_product_api.config.product.OapiStatusCodeEnum;
import com.shoalter.mms_product_api.dao.repository.product.EverutsBuyerRepository;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.openApi.helper.OapiHelper;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiCreateEverutsBuyerMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiCreateEverutsBuyerMainResponseData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiResponseDto;
import com.shoalter.mms_product_api.service.hybris.helper.HybrisHelper;
import com.shoalter.mms_product_api.service.product.helper.UserHelper;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.util.Optional;

@ExtendWith(MockitoExtension.class)
public class OapiHktvCreateEverutsBuyerServiceTest {
	@InjectMocks
	private OapiHktvCreateEverutsBuyerService oapiHktvCreateEverutsBuyerService;

	@Mock
	private OapiHelper oapiHelper;

	@Mock
	private MessageSource messageSource;

	@Mock
	private HybrisHelper hybrisHelper;

	@Mock
	private UserHelper userHelper;

	@Mock
	private EverutsBuyerRepository everutsBuyerRepository;


	private final static String TEST_STRING = "test";

	@Test
	public void start_validateFail_returnFail() {
		//Given
		Mockito.when(oapiHelper.validateStore(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(ResponseDto.fail(null));

		//When
		OapiResponseDto<OapiCreateEverutsBuyerMainResponseData> result = oapiHktvCreateEverutsBuyerService.start(TEST_STRING, TEST_STRING, TEST_STRING, null);

		//Then
		Assertions.assertThat(result.getCode()).isEqualTo(OapiStatusCodeEnum.FAIL.getCode());
	}

	@Test
	public void start_requestMissingData_returnFail() {
		//Given
		Mockito.when(oapiHelper.validateStore(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(ResponseDto.success(null));
		Mockito.when(messageSource.getMessage(Mockito.anyString(), Mockito.any(), Mockito.any()))
			.thenReturn(TEST_STRING);

		//When
		OapiCreateEverutsBuyerMainRequestData request = new OapiCreateEverutsBuyerMainRequestData();
		OapiResponseDto<OapiCreateEverutsBuyerMainResponseData> result = oapiHktvCreateEverutsBuyerService.start(TEST_STRING, TEST_STRING, TEST_STRING, request);

		//Then
		Assertions.assertThat(result.getCode()).isEqualTo(OapiStatusCodeEnum.FAIL.getCode());
	}

	@Test
	public void start_callHybrisFail_returnFail() {
		//Given
		Mockito.when(oapiHelper.validateStore(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(ResponseDto.success(null));
		Mockito.when(hybrisHelper.requestUpsertEverutsBuyer(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(ResponseDto.fail(null));

		//When
		OapiCreateEverutsBuyerMainRequestData request = new OapiCreateEverutsBuyerMainRequestData();
		request.setBuyerId(1L);
		request.setDisplayNameEn(TEST_STRING);
		request.setDisplayNameCn(TEST_STRING);
		request.setDisplayNameZh(TEST_STRING);
		request.setShippingCountry(TEST_STRING);
		OapiResponseDto<OapiCreateEverutsBuyerMainResponseData> result = oapiHktvCreateEverutsBuyerService.start(TEST_STRING, TEST_STRING, TEST_STRING, request);

		//Then
		Assertions.assertThat(result.getCode()).isEqualTo(OapiStatusCodeEnum.FAIL.getCode());
	}

	@Test
	public void start_process_returnSuccess() {
		//Given
		Mockito.when(oapiHelper.validateStore(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(ResponseDto.success(null));
		Mockito.when(hybrisHelper.requestUpsertEverutsBuyer(Mockito.any(), Mockito.any(), Mockito.any()))
			.thenReturn(ResponseDto.success(null));
		Mockito.when(everutsBuyerRepository.findById(Mockito.any()))
			.thenReturn(Optional.empty());

		//When
		OapiCreateEverutsBuyerMainRequestData request = new OapiCreateEverutsBuyerMainRequestData();
		request.setBuyerId(1L);
		request.setDisplayNameEn(TEST_STRING);
		request.setDisplayNameCn(TEST_STRING);
		request.setDisplayNameZh(TEST_STRING);
		request.setShippingCountry(TEST_STRING);
		OapiResponseDto<OapiCreateEverutsBuyerMainResponseData> result = oapiHktvCreateEverutsBuyerService.start(TEST_STRING, TEST_STRING, TEST_STRING, request);

		//Then
		Assertions.assertThat(result.getCode()).isEqualTo(OapiStatusCodeEnum.SUCCESS.getCode());
		Mockito.verify(everutsBuyerRepository, Mockito.times(1)).save(Mockito.any());
	}


}
