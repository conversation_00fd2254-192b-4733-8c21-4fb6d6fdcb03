package com.shoalter.mms_product_api.service.product.template;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.edit_column.ForceOfflineTemplateColumnEnum;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductForceOfflineDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.SetCellValueDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;


@RequiredArgsConstructor
@Service
@Slf4j
public class ProductTemplateForceOfflineHelper extends AbstractReport implements IProductTemplateHelper<ForceOfflineTemplateColumnEnum> {
	private final Gson gson;

	@Override
	public Workbook setErrorReportBodyColumn(Workbook workbook, List<SaveProductRecordRowDo> rows, SaveProductRecordDo recordDo) {
		Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook, 100);
		Sheet dataSheet = tempWorkbook.getSheet(PRODUCT_SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		CellStyle lockBodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		lockBodyStyle.setLocked(true);
		AtomicInteger rowIndex = new AtomicInteger(1);
		SetCellValueDto setCellValueDto = SetCellValueDto.builder()
			.sheet(dataSheet)
			.lockStyle(lockBodyStyle)
			.notLockStyle(bodyStyle)
			.build();

		if (recordDo.getUploadType() == SaveProductType.SINGLE_EDIT_PRODUCT_FORCE_OFFLINE) {
			rows.forEach(row -> {
				setCellValueDto.setRowNumber(rowIndex.get());
				SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
				ProductMasterDto product = singleEditProductDto.getProduct();
				HktvProductDto hktvProductDto = product.getAdditional().getHktv();
				setUnLockCellValue(setCellValueDto, ForceOfflineTemplateColumnEnum.STORE_ID.getColumnNumber(), hktvProductDto.getStorefrontStoreCode());
				setUnLockCellValue(setCellValueDto, ForceOfflineTemplateColumnEnum.SKU_ID.getColumnNumber(), product.getSkuId());
				setCellBooleanValueToString(dataSheet, bodyStyle, rowIndex.get(), ForceOfflineTemplateColumnEnum.STORE_STATUS.getColumnNumber(), hktvProductDto.getForceOffline(), ExcelUtil.SKU_STATUS_FORCE_OFFLINE_LIST);
				setUnLockCellValue(setCellValueDto, ForceOfflineTemplateColumnEnum.CASE_NUMBER.getColumnNumber(), hktvProductDto.getCaseNumber());
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), ForceOfflineTemplateColumnEnum.values().length, row.getErrorMessage());
				rowIndex.addAndGet(1);
			});
		} else if (recordDo.getUploadType() == SaveProductType.BATCH_EDIT_PRODUCT_FORCE_OFFLINE) {
			rows.forEach(row -> {
				setCellValueDto.setRowNumber(rowIndex.get());
				SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
				ProductForceOfflineDto forceOfflineDto = singleEditProductDto.getBatchEditElement().getProductForceOfflineDto();
				setUnLockCellValue(setCellValueDto, ForceOfflineTemplateColumnEnum.STORE_ID.getColumnNumber(), forceOfflineDto.getStorefrontStoreCode());
				setUnLockCellValue(setCellValueDto, ForceOfflineTemplateColumnEnum.SKU_ID.getColumnNumber(), forceOfflineDto.getSkuCode());
				setUnLockCellValue(setCellValueDto, ForceOfflineTemplateColumnEnum.STORE_STATUS.getColumnNumber(), forceOfflineDto.getSkuStatus());
				setUnLockCellValue(setCellValueDto, ForceOfflineTemplateColumnEnum.CASE_NUMBER.getColumnNumber(), forceOfflineDto.getCaseNumber());
				setCellValue(dataSheet, bodyStyle, rowIndex.get(), ForceOfflineTemplateColumnEnum.values().length, row.getErrorMessage());
				rowIndex.addAndGet(1);
			});
		}
		return tempWorkbook;
	}

	@Override
	public int getColumnWidth(TemplateInterface<ForceOfflineTemplateColumnEnum> columnEnum) {
		if (columnEnum == ForceOfflineTemplateColumnEnum.SKU_ID) {
			return 7000;
		}
		return columnEnum.getColumnName().length() * 400;
	}

	@Override
	public void setLoveSheetValues(Sheet lovSheet, Map<String, Map<String, Object>> map, Num theColNum) {
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRODUCT_STATUS, ExcelUtil.SKU_STATUS_FORCE_OFFLINE_LIST, theColNum, map);
	}
}
