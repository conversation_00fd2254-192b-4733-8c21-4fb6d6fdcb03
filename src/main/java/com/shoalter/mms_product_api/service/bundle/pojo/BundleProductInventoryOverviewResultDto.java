package com.shoalter.mms_product_api.service.bundle.pojo;

import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewPageableResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewSortResultDto;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class BundleProductInventoryOverviewResultDto {
	private List<BundleProductOverviewDto> productInventoryOverviewList;
	private Integer totalPages;
	private Integer totalElements;
	private Boolean last;
	private Integer size;
	private Integer number;
	private Integer numberOfElements;
	private Boolean first;
	private Boolean empty;
	private ProductOverviewPageableResultDto pageable;
	private ProductOverviewSortResultDto sort;

	public static BundleProductInventoryOverviewResultDto convertFromProductOverviewResultDto(ProductOverviewResultDto productOverviewResultDto) {
		return BundleProductInventoryOverviewResultDto.builder()
			.empty(productOverviewResultDto.getEmpty())
			.first(productOverviewResultDto.getFirst())
			.last(productOverviewResultDto.getLast())
			.number(productOverviewResultDto.getNumber())
			.pageable(productOverviewResultDto.getPageable())
			.numberOfElements(productOverviewResultDto.getNumberOfElements())
			.sort(productOverviewResultDto.getSort())
			.totalPages(productOverviewResultDto.getTotalPages())
			.totalElements(productOverviewResultDto.getTotalElements())
			.size(productOverviewResultDto.getSize())
			.build();
	}

	public static BundleProductInventoryOverviewResultDto generateEmptyProductOverviewResultDto(Integer size) {
		return BundleProductInventoryOverviewResultDto.builder()
			.size(size)
			.totalPages(0)
			.totalElements(0)
			.numberOfElements(0)
			.build();
	}
}
