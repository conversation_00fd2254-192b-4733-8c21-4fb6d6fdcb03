package com.shoalter.mms_product_api.service.product.pojo.productinventoryapi;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InventorySearchQtyRequestDto {
    private String bu;
    private List<String> uuidList;

	public static InventorySearchQtyRequestDto generateInventorySearchQtyRequestDto(List<String> uuids) {
		return InventorySearchQtyRequestDto.builder()
			.bu(BuCodeEnum.HKTV.name())
			.uuidList(uuids)
			.build();
	}
}
