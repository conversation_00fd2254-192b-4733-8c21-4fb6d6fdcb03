package com.shoalter.mms_product_api.controller;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.config.product.QueryTypeEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BusUnitDo;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.MerchantDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductDo;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.base.pojo.PageDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.BatchEditLittleMallProductCommonlyUsedService;
import com.shoalter.mms_product_api.service.product.BatchEditOverseaDeliveryService;
import com.shoalter.mms_product_api.service.product.BatchEditPriceExchangeRateService;
import com.shoalter.mms_product_api.service.product.BatchEditProductCommonlyUsedService;
import com.shoalter.mms_product_api.service.product.BatchEditProductPackingInfoService;
import com.shoalter.mms_product_api.service.product.BatchUpdateOnlineStatusForStoreService;
import com.shoalter.mms_product_api.service.product.CheckSaveProductRecordsStatusService;
import com.shoalter.mms_product_api.service.product.CreateProductTemplateService;
import com.shoalter.mms_product_api.service.product.DownloadAllProductsService;
import com.shoalter.mms_product_api.service.product.DownloadErrorReportService;
import com.shoalter.mms_product_api.service.product.DownloadPartialProductsService;
import com.shoalter.mms_product_api.service.product.DownloadProductsReportService;
import com.shoalter.mms_product_api.service.product.EditPackagingInfoService;
import com.shoalter.mms_product_api.service.product.EditProductCommonlyUsedTemplateService;
import com.shoalter.mms_product_api.service.product.EditProductTemplateService;
import com.shoalter.mms_product_api.service.product.FindAllProductReadyMethodService;
import com.shoalter.mms_product_api.service.product.FindBatchUploadRecordService;
import com.shoalter.mms_product_api.service.product.FindBatchUploadRecordTypeService;
import com.shoalter.mms_product_api.service.product.FindBuProductService;
import com.shoalter.mms_product_api.service.product.FindBusinessUnitService;
import com.shoalter.mms_product_api.service.product.FindCategoryCodeService;
import com.shoalter.mms_product_api.service.product.FindContractService;
import com.shoalter.mms_product_api.service.product.FindContractsService;
import com.shoalter.mms_product_api.service.product.FindDefaultReadyDaysService;
import com.shoalter.mms_product_api.service.product.FindDeliveryDistrictService;
import com.shoalter.mms_product_api.service.product.FindDeliveryMethodService;
import com.shoalter.mms_product_api.service.product.FindFragileService;
import com.shoalter.mms_product_api.service.product.FindGoodTypeService;
import com.shoalter.mms_product_api.service.product.FindHktvCategoryCodeService;
import com.shoalter.mms_product_api.service.product.FindLittleMallProductIdsByStorefrontStoreCodeService;
import com.shoalter.mms_product_api.service.product.FindLittleMallProductOverviewService;
import com.shoalter.mms_product_api.service.product.FindMenuService;
import com.shoalter.mms_product_api.service.product.FindMerchantService;
import com.shoalter.mms_product_api.service.product.FindPackingBoxTypeService;
import com.shoalter.mms_product_api.service.product.FindPickupDaysService;
import com.shoalter.mms_product_api.service.product.FindPrimaryProductInfoService;
import com.shoalter.mms_product_api.service.product.FindProductIdService;
import com.shoalter.mms_product_api.service.product.FindProductOverviewService;
import com.shoalter.mms_product_api.service.product.FindProductReadyMethodService;
import com.shoalter.mms_product_api.service.product.FindProductStatusService;
import com.shoalter.mms_product_api.service.product.FindProductTypeCodesService;
import com.shoalter.mms_product_api.service.product.FindReadyDaysService;
import com.shoalter.mms_product_api.service.product.FindRecordHistoryRequestDto;
import com.shoalter.mms_product_api.service.product.FindSaveProductRecordService;
import com.shoalter.mms_product_api.service.product.FindStorageTemperatureService;
import com.shoalter.mms_product_api.service.product.FindStorageTypeService;
import com.shoalter.mms_product_api.service.product.FindStoreHashTagsService;
import com.shoalter.mms_product_api.service.product.FindStoreService;
import com.shoalter.mms_product_api.service.product.FindStoreSkuIdProductService;
import com.shoalter.mms_product_api.service.product.FindSubMenuService;
import com.shoalter.mms_product_api.service.product.FindSubSystemParametersService;
import com.shoalter.mms_product_api.service.product.FindSysParmProductFieldsService;
import com.shoalter.mms_product_api.service.product.FindSystemParametersService;
import com.shoalter.mms_product_api.service.product.FindTimeSoltService;
import com.shoalter.mms_product_api.service.product.FindUuidProductService;
import com.shoalter.mms_product_api.service.product.FindVariantProductsService;
import com.shoalter.mms_product_api.service.product.FindVirtualStoreService;
import com.shoalter.mms_product_api.service.product.FindWarehouseService;
import com.shoalter.mms_product_api.service.product.GenerateErrorReportByGroupService;
import com.shoalter.mms_product_api.service.product.GenerateProductUploadExcelService;
import com.shoalter.mms_product_api.service.product.GetProductService;
import com.shoalter.mms_product_api.service.product.LookupContractTermsNamesService;
import com.shoalter.mms_product_api.service.product.SingleEditProductService;
import com.shoalter.mms_product_api.service.product.SingleSaveProductService;
import com.shoalter.mms_product_api.service.product.UpdateContractForStoreService;
import com.shoalter.mms_product_api.service.product.ViewUuidProductService;
import com.shoalter.mms_product_api.service.product.helper.BatchEditHelper;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditLittleMallProductCommonlyUsedRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditOnlineStatusDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditOverseaDeliveryRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditOverseaReserveRegionDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditPackingDimensionDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditPriceDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditProductCommonlyUsedRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditProductPackingInfoRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditVisibilityDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchUpdateOnlineStatusForStoreRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.CategoryCodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ContractStoreDto;
import com.shoalter.mms_product_api.service.product.pojo.ContractTermsNamesDto;
import com.shoalter.mms_product_api.service.product.pojo.ContractTypeDto;
import com.shoalter.mms_product_api.service.product.pojo.DeliveryDistrictMainRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.DeliveryMethodResponseData;
import com.shoalter.mms_product_api.service.product.pojo.DownloadAllProductsRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.DownloadCreateProductTemplateRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.DownloadEditProductTemplateRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.DownloadPartialProductsRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.EditHistoryDto;
import com.shoalter.mms_product_api.service.product.pojo.EditProductPackagingInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.FindProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.FindStoreSkuIdProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.FindStoreSkuIdProductResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.GenerateProductExcelRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.PrimaryProductInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductInventoryDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductInventoryOverviewResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductInventoryViewDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductReadyMethodResponseData;
import com.shoalter.mms_product_api.service.product.pojo.ProductRecordResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductStatusDto;
import com.shoalter.mms_product_api.service.product.pojo.RecordTypeResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.StoreHashTagsDto;
import com.shoalter.mms_product_api.service.product.pojo.StoreWarehouseDto;
import com.shoalter.mms_product_api.service.product.pojo.SubSysParmMainRequestData;
import com.shoalter.mms_product_api.service.product.pojo.SysParmMainRequestData;
import com.shoalter.mms_product_api.service.product.pojo.UpdateContractForStoreRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.VariantProductOverviewDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.FindLittleMallProductsRequestData;
import com.shoalter.mms_product_api.service.product.pojo.response.CheckSaveProductRecordsStatusMainResponseData;
import com.shoalter.mms_product_api.service.product.pojo.response.DeliveryDistrictMainResponseData;
import com.shoalter.mms_product_api.service.product.pojo.response.FindSaveProductRecordResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "產品")
@RequiredArgsConstructor
@RestController
@PreAuthorize("hasRole('ROLE_MMS_ROLE')")
@Slf4j
public class ProductController {

	private final Gson gson;

	private final SingleSaveProductService singleSaveProductService;

	@Operation(summary = "單筆新增產品", description = "status 1代表成功-1代表失敗")
	@PostMapping("/single/save")
	public ResponseDto<ProductRecordResponseDto> singleSave(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody SingleEditProductDto productRequestDto) {
		String clientIp = ClientIpHolder.getClientIp();
		return singleSaveProductService.start(userDto, productRequestDto, clientIp);
	}

	private final SingleEditProductService singleEditProductService;

	@Operation(summary = "單筆修改產品", description = "status 1代表成功-1代表失敗")
	@PostMapping("/single/edit")
	public ResponseDto<ProductRecordResponseDto> singleEdit(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody SingleEditProductDto singleEditProductDto) throws SystemException {
		String clientIp = ClientIpHolder.getClientIp();
		return singleEditProductService.start(userDto, singleEditProductDto, SaveProductType.SINGLE_EDIT_PRODUCT, clientIp);
	}

	@Operation(summary = "單筆修改產品 from_promotion_contract 使用", description = "status 1代表成功-1代表失敗")
	@PostMapping("/single/edit_from_promotion_contract_service")
	public ResponseDto<ProductRecordResponseDto> singleEditFromPromotionContract(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody SingleEditProductDto singleEditProductDto) throws SystemException {
		return singleEditProductService.start(userDto, singleEditProductDto, SaveProductType.SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT, null);
	}

	private final EditPackagingInfoService editPackagingInfoService;

	@Operation(summary = "單筆修改產品包裝資訊", description = "status 1代表成功-1代表失敗")
	@PostMapping("/single/editPacking")
	public ResponseDto<ProductRecordResponseDto> editProductPackagingInfo(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody EditProductPackagingInfoDto editProductPackagingInfoDto) throws SystemException {
		return editPackagingInfoService.start(userDto, editProductPackagingInfoDto);
	}

	private final FindProductOverviewService findProductOverviewService;

	@Operation(summary = "產品瀏覽", description = "status 1代表成功-1代表失敗")
	@PostMapping("/products")
	public ResponseDto<ProductInventoryOverviewResultDto> findProducts(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody FindProductRequestDto findProductRequestDto) {
		ProductSearchRequestDto productSearchRequestDto = new ProductSearchRequestDto(findProductRequestDto.getPage(), findProductRequestDto.getSize(), findProductRequestDto.getProductName(),
			findProductRequestDto.getSkuId(), findProductRequestDto.getBarcode(), findProductRequestDto.getProductId(),
			findProductRequestDto.getBuCode(), findProductRequestDto.getProductType(),
			findProductRequestDto.getBrand(), findProductRequestDto.getLastUpdateFrom(), findProductRequestDto.getLastUpdateTo(), findProductRequestDto.getOnlineStatus(),
			findProductRequestDto.getMerchantId(), findProductRequestDto.getOrderBy(), findProductRequestDto.getProductReadyMethod(), findProductRequestDto.getVisibility(), false);
		return findProductOverviewService.start(userDto, productSearchRequestDto, findProductRequestDto.getDisableInventory(), findProductRequestDto.getMerchantName(), findProductRequestDto.getStoreId());
	}

	private final GetProductService getProductService;
	@Operation(summary = "產品瀏覽", description = "status 1代表成功-1代表失敗")
	@GetMapping("/products/{uuid}")
	public ResponseDto<ProductInventoryDto> getProductByUuid(
			@AuthenticationPrincipal UserDto userDto,
			@PathVariable("uuid") String uuid,
			@RequestParam(required = false) Boolean hasEditStatus) {
		return getProductService.start(userDto, uuid, hasEditStatus);
	}

	private final FindLittleMallProductOverviewService findLittleMallProductOverviewService;

	@Operation(summary = "Little Mall產品瀏覽", description = "status 1代表成功-1代表失敗")
	@GetMapping("/little-mall/products")
	public ResponseDto<ProductInventoryOverviewResultDto> findLittleMallProducts(
			@AuthenticationPrincipal UserDto userDto,
			@RequestParam(required = false) Integer page, @RequestParam(required = false) Integer size, @RequestParam Integer merchantId,
			@RequestParam(required = false) String storeCode, @RequestParam(required = false) List<String> category,
			@RequestParam(required = false) List<String> skuIds, @RequestParam(required = false) String productName,
			@RequestParam(required = false) String productId, @RequestParam(required = false) List<String> orderBy) {
		FindLittleMallProductsRequestData findLittleMallProductsRequestData = new FindLittleMallProductsRequestData(
			page, size, merchantId, storeCode, category, skuIds, productName, productId, orderBy);
		return findLittleMallProductOverviewService.start(userDto, findLittleMallProductsRequestData);
	}

	private final FindLittleMallProductIdsByStorefrontStoreCodeService findLittleMallProductIdsByStorefrontStoreCodeService;

	@Operation(summary = "query store productId 選單", description = "status 1代表成功 -1代表失敗")
	@GetMapping("/little-mall/{storefrontStoreCode}/productIds")
	public ResponseDto<List<String>> findLittleMallProductIdsByStorefrontStoreCode(
		@AuthenticationPrincipal UserDto userDto,
		@PathVariable String storefrontStoreCode) {
		return findLittleMallProductIdsByStorefrontStoreCodeService.start(userDto, storefrontStoreCode);
	}

	private final FindUuidProductService findUuidProductService;

	@Operation(summary = "用UUID取得產品資料", description = "status 1代表成功-1代表失敗")
	@GetMapping("/uuidProduct")
	public ResponseDto<ProductInventoryDto> findUuidProduct(
			@AuthenticationPrincipal UserDto userDto,
			@RequestParam String uuid) {
		return findUuidProductService.start(userDto, uuid);
	}

	private final FindStoreSkuIdProductService findStoreSkuIdProductService;
	@Operation(summary = "用store sku id取得產品資料", description = "status 1代表成功-1代表失敗")
	@PostMapping("/storeSkuIdProduct")
	public ResponseDto<List<FindStoreSkuIdProductResponseDto>> findStoreSkuIdProduct(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody FindStoreSkuIdProductRequestDto findStoreSkuIdProductRequestDto) {
		return findStoreSkuIdProductService.start(userDto, findStoreSkuIdProductRequestDto);
	}

	private final ViewUuidProductService viewUuidProductService;

	@Operation(summary = "用UUID取得產品資料給view用", description = "status 1代表成功-1代表失敗")
	@GetMapping("/viewUuidProduct")
	public ResponseDto<ProductInventoryViewDto> viewUuidProduct(
			@AuthenticationPrincipal UserDto userDto,
			@RequestParam String uuid) {
		return viewUuidProductService.start(userDto, uuid);
	}

	private final FindBusinessUnitService findBusinessUnitService;

	@Operation(summary = "Business Unit列表", description = "status 1代表成功-1代表失敗")
	@GetMapping("/businessUnit")
	public ResponseDto<List<BusUnitDo>> findBusinessUnit() {
		return findBusinessUnitService.start();
	}

	private final FindMerchantService findMerchantService;

	@Operation(summary = "商戶列表", description = "status 1代表成功-1代表失敗")
	@GetMapping("/merchant")
	public ResponseDto<List<MerchantDo>> findMerchant(
			@AuthenticationPrincipal UserDto userDto) {
		return findMerchantService.start(userDto);
	}

	private final FindMenuService findMenuService;

	@Operation(summary = "選單列表", description = "status 1代表成功-1代表失敗")
	@GetMapping("/menu")
	public ResponseDto<List<SysParmDo>> findMenu(
			@RequestParam String segment, @RequestParam(required = false) String buCode) {
		return findMenuService.start(segment, buCode);
	}

	private final FindSubMenuService findSubMenuService;

	@Operation(summary = "子選單列表", description = "status 1代表成功-1代表失敗，parentSegment跟parentCode來自父選單")
	@GetMapping("/subMenu")
	public ResponseDto<List<SysParmDo>> findSubMenu(
			@RequestParam String parentSegment, @RequestParam String parentCode, @RequestParam(required = false) String buCode) {
		return findSubMenuService.start(parentSegment, parentCode, buCode);
	}

	private final FindContractService findContractService;

	@Operation(summary = "尋找合約編號", description = "status 1代表成功-1代表失敗")
	@GetMapping("/contract")
	public ResponseDto<List<ContractTypeDto>> findContract(
			@AuthenticationPrincipal UserDto userDto,
			@RequestParam Integer merchantId, @RequestParam String buCode) {
		return findContractService.start(userDto, merchantId, buCode);
	}

	private final FindStoreService findStoreService;

	@Operation(summary = "商店列表", description = "status 1代表成功-1代表失敗")
	@GetMapping("/store")
	public ResponseDto<List<ContractStoreDto>> findStore(
			@AuthenticationPrincipal UserDto userDto,
			@RequestParam Integer merchantId) {
		return findStoreService.start(userDto, merchantId);
	}

	private final FindProductIdService findProductIdService;

	@Operation(summary = "取得該store底下的所有product code", description = "status 1代表成功-1代表失敗")
	@GetMapping("/productId")
	public ResponseDto<List<String>> findProductId(
			@RequestParam Integer storeId,
			@RequestParam String buCode) {
		return findProductIdService.start(storeId, buCode);
	}

	private final FindBuProductService findBuProductService;

	@Operation(summary = "bu產品", description = "status 1代表成功-1代表失敗")
	@GetMapping("/buProduct")
	public ResponseDto<PageDto<ProductDo>> findBuProduct(
			@RequestParam Integer merchantId,
			@RequestParam String buCode,
			@RequestParam String productId,
			@Parameter(description = "從1開始") @RequestParam Integer pageNumber,
			@RequestParam Integer pageSize) {
		return findBuProductService.start(pageNumber, pageSize, merchantId, buCode, productId);
	}

	private final FindHktvCategoryCodeService findHktvCategoryCodeService;
	@Operation(summary = "hktv產品種類Code", description = "status 1代表成功-1代表失敗")
	@GetMapping("/hktvCategoryCode")
	public ResponseDto<List<CategoryCodeDto>> findHktvCategoryCode(
			@RequestParam(required = false) String categoryChineseName,
			@RequestParam(required = false) String categoryEnglishName,
			@RequestParam(required = false) String productReadyMethodCode) {
		return findHktvCategoryCodeService.start(categoryChineseName, categoryEnglishName, productReadyMethodCode);
	}

	private final FindCategoryCodeService findCategoryCodeService;

	@Operation(summary = "產品種類Code", description = "status 1代表成功-1代表失敗")
	@GetMapping("/categoryCode")
	public ResponseDto<List<CategoryCodeDto>> findCategoryCode(
			@RequestParam String buCode,
			@RequestParam Integer storeId,
			@RequestParam String contractTypeCode,
			@RequestParam(required = false) String searchCategoryCode,
			@RequestParam(required = false) String searchCategoryName,
			@RequestParam(required = false) String productReadyMethodCode) {
		return findCategoryCodeService.start(buCode, storeId, contractTypeCode, searchCategoryCode, searchCategoryName, productReadyMethodCode);
	}

	private final FindProductReadyMethodService findProductReadyMethodService;

	@Operation(summary = "貨品準備方式", description = "status 1代表成功-1代表失敗")
	@GetMapping("/productReadyMethod")
	public ResponseDto<List<SysParmDo>> findProductReadyMethod(
			@RequestParam String buCode,
			@RequestParam(required = false) Integer contractId
	) {
		return findProductReadyMethodService.start(contractId, buCode);
	}

	private final FindAllProductReadyMethodService findAllProductReadyMethodService;

	@Operation(summary = "所有貨品準備方式", description = "status 1代表成功-1代表失敗")
	@GetMapping("/productReadyMethods")
	public ResponseDto<List<ProductReadyMethodResponseData>> findAllProductReadyMethod(@RequestParam String buCode) {
		return findAllProductReadyMethodService.start(buCode);
	}

	private final FindDeliveryMethodService findDeliveryMethodService;

	@Operation(summary = "運送方式", description = "status 1代表成功-1代表失敗")
	@GetMapping("/deliveryMethod")
	public ResponseDto<SysParmDo> findDeliveryMethod(
			@RequestParam String buCode,
			@RequestParam String productReadyMethodCode) {
		return findDeliveryMethodService.start(buCode, productReadyMethodCode);
	}

	@Operation(summary = "product ready method對應delivery method列表", description = "status 1代表成功-1代表失敗")
	@GetMapping("/productReadyMethodMappingInfo")
	public ResponseDto<List<DeliveryMethodResponseData>> findDeliveryMethod() {
		return findDeliveryMethodService.start();
	}

	private final FindPickupDaysService findPickupDaysService;

	@Operation(summary = "領取日期", description = "status 1代表成功-1代表失敗")
	@GetMapping("/pickupDays")
	public ResponseDto<List<SysParmDo>> findPickupDays(
			@RequestParam String buCode,
			@RequestParam Integer storeId,
			@RequestParam String productReadyMethodCode) {
		return findPickupDaysService.start(buCode, storeId, productReadyMethodCode);
	}

	private final FindTimeSoltService findTimeSoltService;

	@Operation(summary = "領取時間", description = "status 1代表成功-1代表失敗")
	@GetMapping("/timeSlot")
	public ResponseDto<List<SysParmDo>> findTimeSlot(
			@RequestParam String buCode,
			@RequestParam String productReadyMethodCode) {
		return findTimeSoltService.start(buCode, productReadyMethodCode);
	}

	private final FindReadyDaysService findReadyDaysService;

	@Operation(summary = "就續日", description = "status 1代表成功-1代表失敗")
	@GetMapping("/readyDays")
	public ResponseDto<List<SysParmDo>> findReadyDays(
			@RequestParam String buCode,
			@RequestParam String productReadyMethodCode) {
		return findReadyDaysService.start(buCode, productReadyMethodCode);
	}

	private final FindPackingBoxTypeService findPackingBoxTypeService;

	@Operation(summary = "包裝盒類型", description = "status 1代表成功-1代表失敗")
	@GetMapping("/packingBoxType")
	public ResponseDto<List<SysParmDo>> findPackingBoxType(
			@RequestParam String buCode,
			@RequestParam String categoryCode,
			@RequestParam String productReadyMethodCode) {
		return findPackingBoxTypeService.start(buCode, categoryCode, productReadyMethodCode);
	}

	private final FindGoodTypeService findGoodTypeService;

	@Operation(summary = "Goods Type", description = "status 1代表成功-1代表失敗")
	@GetMapping("/goodsType")
	public ResponseDto<List<String>> findGoodsType() {
		return findGoodTypeService.start();
	}

	private final FindWarehouseService findWarehouseService;

	@Operation(summary = "倉存狀態", description = "status 1代表成功-1代表失敗")
	@GetMapping("/warehouse")
	public ResponseDto<List<StoreWarehouseDto>> findWarehouse(
			@RequestParam Integer storeId,
			@RequestParam String productReadyMethodCode,
			@RequestParam(required = false) String storageType
	) {
		return findWarehouseService.start(storeId, productReadyMethodCode, storageType);
	}

	private final FindSysParmProductFieldsService findSysParmProductFieldsService;

	@Operation(summary = "依據contract取得SysParm的ProductField 選單", description = "status 1代表成功-1代表失敗")
	@GetMapping("/{contractId}/findSysParmProductFields")
	public ResponseDto<List<SysParmDo>> findSysParmProductFields(@PathVariable Integer contractId) {
		return findSysParmProductFieldsService.start(contractId);
	}

	private final CreateProductTemplateService createProductTemplateService;

	@Operation(summary = "下載批次新增產品模板", description = "status -1代表失敗")
	@PostMapping("/hktv/excel/createTemplate/download")
	public HttpEntity<ByteArrayResource> downloadCreateProductTemplate(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody DownloadCreateProductTemplateRequestDto requestDto) {
		return createProductTemplateService.start(userDto, requestDto);
	}

	public final EditProductCommonlyUsedTemplateService editProductCommonlyUsedTemplateService;

	@Operation(summary = "下載批次編輯常用欄位模板", description = "status -1代表失敗")
	@GetMapping("/downloadEditProductCommonlyUsedTemplate")
	public HttpEntity<ByteArrayResource> downloadEditProductCommonlyUsedTemplate(
			@AuthenticationPrincipal UserDto userDto,
			@RequestParam List<String> skuList,
			@RequestParam Integer contractId,
			@RequestParam Integer merchantId) {
		return editProductCommonlyUsedTemplateService.start(skuList, userDto, merchantId, contractId);
	}

	private final EditProductTemplateService editProductTemplateService;

	@Operation(summary = "下載批次編輯產品模板", description = "status -1代表失敗")
	@PostMapping("/hktv/excel/editTemplate/download")
	public HttpEntity<ByteArrayResource> downloadEditProductTemplate(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody DownloadEditProductTemplateRequestDto requestDto) {
		return editProductTemplateService.start(requestDto);
	}

	private final DownloadPartialProductsService downloadPartialProductsService;

	@Operation(summary = "根據sku uuid下載批次編輯產品Excel", description = "status -1代表失敗")
	@PostMapping("/hktv/excel/partialProducts/export")
	public ResponseDto<Void> exportProductsBySkuUuids(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody DownloadPartialProductsRequestDto requestDto) {
		return downloadPartialProductsService.start(userDto, requestDto);
	}

	private final DownloadAllProductsService downloadAllProductsService;

	@Operation(summary = "根據sku查詢條件下載批次編輯產品Excel", description = "status -1代表失敗")
	@PostMapping("/hktv/excel/allProducts/export")
	public ResponseDto<Void> exportProductsByConditions(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody DownloadAllProductsRequestDto requestDto) {
		return downloadAllProductsService.start(userDto, requestDto);
	}

	private final DownloadProductsReportService downloadProductsReportService;

	@Operation(summary = "下載產品報告", description = "status -1 代表失敗")
	@GetMapping("/downloadProductsReport")
	public HttpEntity<ByteArrayResource> downloadProductsReport(
			@AuthenticationPrincipal UserDto userDto,
			@Parameter(description = "產品 UUID 列表，若此列表為空則報告為空", required = true) @RequestParam List<String> uuidList) {
		return downloadProductsReportService.start(userDto, uuidList);
	}

	private final CheckSaveProductRecordsStatusService checkSaveProductRecordsStatusService;

	@Operation(summary = "Get save product redords status", description = "api status: 1 成功, -1 失敗")
	@GetMapping("/checkSaveProductRecordsStatus")
	public ResponseDto<List<CheckSaveProductRecordsStatusMainResponseData>> checkSaveProductRecordsStatus(
			@RequestParam(required = false) List<Long> recordIds) {
		return checkSaveProductRecordsStatusService.start(recordIds);
	}

	private final GenerateProductUploadExcelService generateProductUploadExcelService;

	@Operation(summary = "下載MMS1.0上傳產品excel and create excel", description = "如果UUID為空產生create excel,反之則產生edit excel。<br> status -1 代表失敗。")
	@PostMapping("/batch-uploads/hktv/excel")
	public HttpEntity<ByteArrayResource> generateProductExcel(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody GenerateProductExcelRequestDto generateProductExcelRequestDto) {
		return generateProductUploadExcelService.start(userDto, generateProductExcelRequestDto);
	}

	private final FindBatchUploadRecordService findBatchUploadRecordService;

	@Operation(summary = "Get Product History", description = "api status: 1 成功, -1 失敗")
	@GetMapping("/batchUploadRecord")
	public ResponseDto<PageDto<EditHistoryDto>> findBatchUploadRecord(
			@AuthenticationPrincipal UserDto userDto,
			@RequestParam List<Integer> uploadTypeList,
			@RequestParam(defaultValue = "1") Integer pageSize,
			@Parameter(description = "從1開始")
			@RequestParam(defaultValue = "1") Integer pageNumber,
			@Parameter(description = "SUCCESS、FAIL、PENDING")
			@RequestParam(required = false) List<String> statusList,
			@RequestParam(required = false) Long startTimestamp,
			@RequestParam(required = false) Long endTimestamp,
			@RequestParam(required = false) String skuId,
			@RequestParam(required = false) String merchantName
	) {
		FindRecordHistoryRequestDto requestDto = FindRecordHistoryRequestDto.builder()
			.uploadTypeList(uploadTypeList)
			.pageSize(pageSize)
			.pageNumber(pageNumber)
			.statusList(statusList)
			.skuId(skuId)
			.merchantName(merchantName)
			.startTimestamp(startTimestamp)
			.endTimestamp(endTimestamp)
			.build();
		return findBatchUploadRecordService.start(userDto, requestDto);
	}


	private final FindBatchUploadRecordTypeService findBatchUploadRecordTypeService;

	@Operation(summary = "Get Product History Record Type", description = "api status: 1 成功, -1 失敗")
	@GetMapping(path = "/batchUploadRecordType")
	public ResponseDto<List<RecordTypeResponseDto>> upload() {
		return findBatchUploadRecordTypeService.start();
	}

	private final BatchEditProductCommonlyUsedService batchEditProductCommonlyUsedService;

	@Operation(summary = "批次編輯產品常用欄位", description = "status 1代表成功 -1代表失敗")
	@PostMapping("/batchEditProductCommonlyUsed")
	public ResponseDto<Long> batchEditProductCommonlyUsed(@AuthenticationPrincipal UserDto userDto,
														  @RequestBody BatchEditProductCommonlyUsedRequestDto batchEditProductCommonlyUsedRequestDto) {
		String clientIp = ClientIpHolder.getClientIp();
		return batchEditProductCommonlyUsedService.start(userDto, batchEditProductCommonlyUsedRequestDto, clientIp);
	}

	private final BatchEditLittleMallProductCommonlyUsedService batchEditLittleMallProductCommonlyUsedService;

	@Operation(summary = "批次編輯Little Mall產品常用欄位", description = "status 1代表成功 -1代表失敗")
	@PostMapping("/batchEditLittleMallProductCommonlyUsed")
	public ResponseDto<Void> batchEditLittleMallProductCommonlyUsed(@AuthenticationPrincipal UserDto userDto,
																	@RequestBody BatchEditLittleMallProductCommonlyUsedRequestDto batchEditLittleMallProductCommonlyUsedRequestDto) {
		return batchEditLittleMallProductCommonlyUsedService.start(userDto, batchEditLittleMallProductCommonlyUsedRequestDto);
	}

	private final BatchEditOverseaDeliveryService batchEditOverseaDeliveryService;

	@Operation(summary = "batch edit HKTV product's oversea delivery", description = "status 1 is success, -1 is fail.</br>data is group id")
	@PatchMapping("/batch/product/oversea-delivery")
	public ResponseDto<Long> batchEditOverseaDelivery(@AuthenticationPrincipal UserDto userDto,
													  @RequestBody BatchEditOverseaDeliveryRequestDto batchEditOverseaDeliveryRequestDto) {
		return batchEditOverseaDeliveryService.start(userDto, batchEditOverseaDeliveryRequestDto);
	}

	private final LookupContractTermsNamesService lookupContractTermsNamesService;

	@Operation(summary = "查詢保險合約 Terms 名稱")
	@GetMapping("/lookupContractTermsNames")
	public ResponseDto<ContractTermsNamesDto> lookupContractTermsNames(
			@Parameter(description = "合約 ID", required = true) @RequestParam Integer contractId,
			@Parameter(description = "商店 ID", required = true) @RequestParam Integer storeId) {
		return lookupContractTermsNamesService.start(contractId, storeId);
	}

	private final DownloadErrorReportService downloadErrorReportService;

	@Operation(summary = "下載批次上傳失敗記錄", description = "status -1代表失敗<br>" +
			"uploadType：" +
			"SINGLE_CREATE_PRODUCT：" + SaveProductType.SINGLE_CREATE_PRODUCT +
			",<br>SINGLE_EDIT_PRODUCT：" + SaveProductType.SINGLE_EDIT_PRODUCT +
			",<br>SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT：" + SaveProductType.SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT +
			",<br>BATCH_CREATE_PRODUCT：" + SaveProductType.BATCH_CREATE_PRODUCT +
			",<br>BATCH_EDIT_PRODUCT：" + SaveProductType.BATCH_EDIT_PRODUCT +
			",<br>BATCH_EDIT_PRODUCT_COMMONLY_USED：" + SaveProductType.BATCH_EDIT_PRODUCT_COMMONLY_USED +
			",<br>SINGLE_CREATE_LITTLE_MALL_PRODUCT：" + SaveProductType.SINGLE_CREATE_LITTLE_MALL_PRODUCT +
			",<br>BATCH_CREATE_LITTLE_MALL_PRODUCT：" + SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT +
			",<br>SINGLE_EDIT_LITTLE_MALL_PRODUCT：" + SaveProductType.SINGLE_EDIT_LITTLE_MALL_PRODUCT +
			",<br>BATCH_EDIT_PRODUCT_PACKAGING_INFO：" + SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_INFO +
			",<br>SINGLE_EDIT_LITTLE_MALL_PRODUCT：" + SaveProductType.SINGLE_EDIT_LITTLE_MALL_PRODUCT +
			",<br>SINGLE_CREATE_BUNDLE：" + SaveProductType.SINGLE_CREATE_BUNDLE +
			",<br>SINGLE_EDIT_BUNDLE：" + SaveProductType.SINGLE_EDIT_BUNDLE +
			",<br>SYNC_OFFLINE_BUNDLE：" + SaveProductType.SYNC_OFFLINE_BUNDLE +
	        ",<br>BATCH_CREATE_SHOPLINE_PRODUCT：" + SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE +
	        ",<br>BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV：" + SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV +
			",<br>BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL：" + SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL +
	        ",<br>BATCH_CREATE_PRODUCT_FROM_EXCEL：" + SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL +
	        ",<br>BATCH_EDIT_PRODUCT_FROM_EXCEL：" + SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL +
			",<br>BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION: " + SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION +
			",<br>BATCH_EDIT_PRODUCT_PRICE: " + SaveProductType.BATCH_EDIT_PRODUCT_PRICE +
			",<br>BATCH_EDIT_PRODUCT_ONLINE_STATUS: " + SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS +
			",<br>BATCH_EDIT_PRODUCT_VISIBILITY: " + SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY +
			",<br>BATCH_CRATE_BINDING_EXTENDED_WARRANTY: " + SaveProductType.BATCH_CRATE_BINDING_EXTENDED_WARRANTY +
			",<br>BATCH_DELETE_BINDING_EXTENDED_WARRANTY: " + SaveProductType.BATCH_DELETE_BINDING_EXTENDED_WARRANTY +
			",<br>BATCH_EDIT_LITTLE_MALL_PRODUCT: " + SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT +
			",<br>BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION: " + SaveProductType.BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION +
			",<br>BATCH_EDIT_HKTV_PRODUCT_TRANSLATE: " + SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE + "<br>" +
			",<br>LITTLE_MALL_FLATTEN_PRODUCTS: " + SaveProductType.LITTLE_MALL_FLATTEN_PRODUCTS + "<br>" +
			",<br>BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB: " + SaveProductType.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB + "<br>" +
			",<br>BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL: " + SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL + "<br>"
	)
	@GetMapping("/downloadErrorReport")
	public HttpEntity<ByteArrayResource> downloadErrorReport(
			@AuthenticationPrincipal UserDto userDto,
			@RequestParam Long recordId,
			@RequestParam Integer uploadType) {
		return downloadErrorReportService.start(userDto, recordId, uploadType);
	}

	private final FindSaveProductRecordService findSaveProductRecordService;

	@Operation(summary = "get history by record id</br>" +
			"status is 1 as success</br>" +
			"status is -1 as fail </br>" +
			"status is other as pending")
	@GetMapping("/product-record/{recordId}")
	public ResponseDto<FindSaveProductRecordResponseDto> findSaveProductRecord(@PathVariable Long recordId) {
		return findSaveProductRecordService.start(recordId);
	}

	private final GenerateErrorReportByGroupService generateErrorReportByGroupService;

	@Operation(summary = "download error report by group id", description = "如果UUID為空產生create excel,反之則產生edit excel。<br> status -1 代表失敗。")
	@PostMapping("/error-report/group/{groupId}")
	public HttpEntity<ByteArrayResource> generateErrorReportByGroup(@AuthenticationPrincipal UserDto userDto,
																	@PathVariable Long groupId) {
		return generateErrorReportByGroupService.start(userDto, groupId);
	}

	private final FindStorageTemperatureService findStorageTemperatureService;

	@Operation(summary = "取得儲存溫度", description = "status 1代表成功-1代表失敗")
	@GetMapping("/storageTemperature")
	public ResponseDto<List<SysParmDo>> findStorageTemperature(
			@RequestParam String buCode,
			@RequestParam String categoryCode,
			@RequestParam boolean isCoupon,
			@RequestParam String productReadyMethodCode
	) {
		return findStorageTemperatureService.start(buCode, categoryCode, isCoupon, productReadyMethodCode);
	}

	private final FindFragileService findFragileService;

	@Operation(summary = "取得易碎品選項", description = "status 1代表成功-1代表失敗")
	@GetMapping("/fragile")
	public ResponseDto<List<SysParmDo>> findFragileMenuItem(
			@RequestParam String buCode,
			@RequestParam String categoryCode,
			@RequestParam String storageTemperatureCode
	) {
		return findFragileService.start(buCode, categoryCode, storageTemperatureCode);
	}

	private final FindStoreHashTagsService findStoreHashTagsService;

	@Operation(summary = "HashTag 列表", description = "status 1代表成功 -1代表失敗\n沒有也會回傳空陣列")
	@GetMapping(path = "hashtags")
	public ResponseDto<StoreHashTagsDto> findStoreHashTags(
			@AuthenticationPrincipal UserDto user,
			@RequestParam String keyword,
			@RequestParam(required = false) String storeCode) {
		return findStoreHashTagsService.start(user, keyword, storeCode);
	}

	private final BatchUpdateOnlineStatusForStoreService batchUpdateOnlineStatusForStoreService;

	@Operation(summary = "Batch update OnlineStatus for store", description = "status 1代表成功 -1代表失敗")
	@PatchMapping(path = "/store/{storeId}/product/online-status")
	public ResponseDto<Long> batchUpdateOnlineStatusForStore(
			@AuthenticationPrincipal UserDto userDto,
			@PathVariable Integer storeId,
			@RequestBody BatchUpdateOnlineStatusForStoreRequestDto requestDto) {
		return batchUpdateOnlineStatusForStoreService.start(userDto, storeId, requestDto);
	}

	private final UpdateContractForStoreService updateContractForStoreService;

	@Operation(summary = "Update contract for store", description = "status 1代表成功 -1代表失敗\n沒有也會回傳空陣列")
	@PatchMapping(path = "/stores/{storeId}/products/contract")
	public ResponseDto<Long> updateContractForStore(
			@AuthenticationPrincipal UserDto userDto,
			@PathVariable Integer storeId,
			@RequestBody UpdateContractForStoreRequestDto requestDto) {
		return updateContractForStoreService.start(userDto, storeId, requestDto);
	}

	private final BatchEditProductPackingInfoService batchEditProductPackingInfoService;

	@Operation(summary = "批次修改產品包裝資訊", description = "status 1代表成功 -1代表失敗")
	@PostMapping("/batch/editPacking")
	public ResponseDto<Long> batchEditPacking(@AuthenticationPrincipal UserDto userDto,
														  @RequestBody BatchEditProductPackingInfoRequestDto batchEditProductPackingInfoRequestDto) {
		String clientIp = ClientIpHolder.getClientIp();
		return batchEditProductPackingInfoService.start(userDto, batchEditProductPackingInfoRequestDto, clientIp);
	}

	private final FindDeliveryDistrictService findDeliveryDistrictService;

	@Operation(summary = "取得該stores能使用的oversea delivery district可用選項", description = "status 1代表成功 -1代表失敗")
	@GetMapping("/deliveryDistrict")
	public ResponseDto<List<String>> findDeliveryDistrict(
			@RequestParam String buCode,
			@RequestParam Integer storeId,
			@RequestParam Integer primaryCategoryId) {
		return findDeliveryDistrictService.start(buCode, storeId, primaryCategoryId);
	}

	@Operation(summary = "取得該stores能使用的oversea delivery district可用選項, 支援多個primary category code", description = "status 1代表成功 -1代表失敗")
	@PostMapping("/deliveryDistricts")
	public ResponseDto<DeliveryDistrictMainResponseData> findDeliveryDistricts(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody DeliveryDistrictMainRequestDto requestDto) {
		return findDeliveryDistrictService.start(requestDto.getBuCode(), requestDto.getStoreId(), requestDto.getPrimaryCategoryCodeList());
	}

	private final FindDefaultReadyDaysService findDefaultReadyDaysService;

	@Operation(summary = "就續日", description = "status 1代表成功-1代表失敗")
	@GetMapping("/defaultReadyDays")
	public ResponseDto<List<SysParmDo>> findDefaultReadyDays(
			@RequestParam String buCode,
			@RequestParam String productReadyMethodCode) {
		return findDefaultReadyDaysService.start(buCode, productReadyMethodCode);
	}

	private final FindProductStatusService findProductStatusService;
	@Operation(summary = "Product Status", description = "status 1代表成功-1代表失敗")
	@GetMapping("/products/{uuid}/product-status")
	public ResponseDto<ProductStatusDto> findProductStatus(@PathVariable String uuid) {
		return findProductStatusService.start(uuid);
	}

	private final FindStorageTypeService findStorageTypeService;

	@Operation(summary = "find all buysell merchant storage type", description = "status 1代表成功-1代表失敗")
	@GetMapping("/storageType")
	public ResponseDto<List<SysParmDo>> findStorageType(
			@RequestParam String productReadyMethod) {
		return findStorageTypeService.start(productReadyMethod);
	}

	private final FindVirtualStoreService findVirtualStoreService;

	@Operation(summary = "Virtual Store選項", description = "status 1代表成功 -1代表失敗，true代表是，false代表不是")
	@GetMapping("/virtualStore")
	public ResponseDto<List<SysParmDo>> findVirtualStore(
			@AuthenticationPrincipal UserDto userDto,
			@RequestParam Integer merchantId) {
		return findVirtualStoreService.start(userDto, merchantId);
	}

	private final FindPrimaryProductInfoService findPrimaryProductInfoService;

	@Operation(summary = "query primary product info for edit", description = "status 1代表成功-1代表失敗")
	@GetMapping("/primaryInfo")
	public ResponseDto<PrimaryProductInfoDto> findPrimaryProductInfo(
		@RequestParam String productCode,
		@RequestParam Integer merchantId,
		@RequestParam String buCode,
		@RequestParam String storeCode) {
		return findPrimaryProductInfoService.start(productCode, merchantId, buCode, storeCode);
	}

	private final FindVariantProductsService findVariantProductsService;

	@Operation(summary = "view variant products", description = "status 1代表成功-1代表失敗")
	@GetMapping("/variantProducts/{storeCode}/{productCode}")
	public ResponseDto<VariantProductOverviewDto> findVariantProducts(
			@AuthenticationPrincipal UserDto userDto,
			@RequestParam(defaultValue = "1") Integer page,
			@RequestParam(defaultValue = "10") Integer size,
			@RequestParam(name = "order_by",defaultValue = "is_primary_sku desc") List<String> orderBy,
			@PathVariable("storeCode") String storeCode,
			@PathVariable("productCode") String productCode) {

		ProductSearchRequestDto productSearchRequestDto = ProductSearchRequestDto.builder()
				.storeId(List.of(storeCode))
				.productId(productCode)
				.queryType(QueryTypeEnum.EXACT.name())
				.isBundle(false)
				.page(page)
				.size(size)
				.orderBy(orderBy)
				.build();

		return findVariantProductsService.start(userDto, productSearchRequestDto);
	}

	@Operation(summary = "產品瀏覽", description = "status 1代表成功-1代表失敗")
	@GetMapping("/products")
	public ResponseDto<ProductInventoryOverviewResultDto> findProducts(
			@AuthenticationPrincipal UserDto userDto,
			@RequestParam(required = false) Integer page, @RequestParam(required = false) Integer size, @RequestParam(required = false) String productName,
			@RequestParam(required = false) String skuId, @RequestParam(required = false) String barcode, @RequestParam(required = false) String productId,
			@RequestParam(required = false) List<String> buCode, @RequestParam(required = false) String productType, @RequestParam(required = false) List<Integer> storeId,
			@RequestParam(required = false) List<Integer> brand, @RequestParam(required = false) String lastUpdateFrom, @RequestParam(required = false) String lastUpdateTo, @RequestParam(required = false) String onlineStatus,
			@RequestParam(required = false) List<Integer> merchantId, @RequestParam(required = false) List<String> orderBy, @RequestParam(required = false) boolean disableInventory,
			@RequestParam(required = false) String merchantName, @RequestParam(required = false) List<String> productReadyMethod, @RequestParam(required = false) String visibility,
			@RequestParam(defaultValue = "false") Boolean isBundle) {
		ProductSearchRequestDto productSearchRequestDto = new ProductSearchRequestDto(page, size, productName,
				skuId, barcode, productId,
				buCode, productType,
				brand, lastUpdateFrom, lastUpdateTo, onlineStatus,
				merchantId, orderBy, productReadyMethod, visibility, isBundle);
		return findProductOverviewService.start(userDto, productSearchRequestDto, disableInventory, merchantName, storeId);
	}

	private final BatchEditHelper batchEditHelper;
	@Operation(summary = "批次編輯產品 packing dimension", description = "status 1代表成功-1代表失敗")
	@PostMapping("/batch/editPackingDimension")
	public ResponseDto<Set<Long>> batchEditPackingDimension(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody BatchEditProductRequestDto<BatchEditPackingDimensionDto> batchEditProductRequestDto) {
		return batchEditHelper.createBatchEditRecord(userDto, batchEditProductRequestDto, SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION, null);
	}

	@Operation(summary = "批次編輯產品 price", description = "status 1代表成功-1代表失敗")
	@PostMapping("/batch/editPrice")
	public ResponseDto<Set<Long>> batchEditSkuPrice(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody BatchEditProductRequestDto<BatchEditPriceDto> batchEditProductRequestDto) {
		String clientIp = ClientIpHolder.getClientIp();
		return batchEditHelper.createBatchEditRecord(userDto, batchEditProductRequestDto, SaveProductType.BATCH_EDIT_PRODUCT_PRICE, clientIp);
	}

	@Operation(summary = "批次編輯產品 online status", description = "status 1代表成功-1代表失敗")
	@PostMapping("/batch/editOnlineStatus")
	public ResponseDto<Set<Long>> batchEditSkuOnlineStatus(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody BatchEditProductRequestDto<BatchEditOnlineStatusDto> batchEditProductRequestDto) {
		String clientIp = ClientIpHolder.getClientIp();
		return batchEditHelper.createBatchEditRecord(userDto, batchEditProductRequestDto, SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS, clientIp);
	}

	@Operation(summary = "批次編輯產品 visibility", description = "status 1代表成功-1代表失敗")
	@PostMapping("/batch/editVisibility")
	public ResponseDto<Set<Long>> batchEditSkuVisibility(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody BatchEditProductRequestDto<BatchEditVisibilityDto> batchEditProductRequestDto) {
		String clientIp = ClientIpHolder.getClientIp();
		return batchEditHelper.createBatchEditRecord(userDto, batchEditProductRequestDto, SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY, clientIp);
	}

	@Operation(summary = "批次編輯oversea delivery地區", description = "status 1代表成功-1代表失敗")
	@PostMapping("/batch/editReserveOverseaDelivery")
	public ResponseDto<Set<Long>> batchEditReserveOverseaDelivery(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody BatchEditProductRequestDto<BatchEditOverseaReserveRegionDto> batchEditOverseaReserveRegionDto) {
		String clientIp = ClientIpHolder.getClientIp();
		return batchEditHelper.createBatchEditRecord(userDto, batchEditOverseaReserveRegionDto, SaveProductType.BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION, clientIp);
	}

	private final BatchEditPriceExchangeRateService batchEditPriceExchangeRateService;

	@Operation(summary = "check rate exchange, batch edit currency rmb sku price to productMaster and hybris")
	@PostMapping("/batch/edit/currency/rmb/sku/price")
	public ResponseDto<Void> batchEditCurrencyRmbSkuPrice(@AuthenticationPrincipal UserDto userDto) {
		return batchEditPriceExchangeRateService.batchEditCurrencyRmbSkuPrice(userDto);
	}

	private final FindContractsService findContractsService;
	@Operation(summary = "尋找合約", description = "status 1代表成功-1代表失敗")
	@PostMapping("/contracts")
	public ResponseDto<List<ContractTypeDto>> findContracts(@AuthenticationPrincipal UserDto userDto, @RequestBody List<Integer> contractIds) {
		return findContractsService.start(userDto, contractIds);
	}

	private final FindProductTypeCodesService findProductTypeCodesService;
	@Operation(summary = "查詢product type code", description = "status 1代表成功-1代表失敗")
	@PostMapping("/productTypeCodes")
	public ResponseDto<List<CategoryCodeDto>> findProductTypeCodes(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody List<String> productTypeCodes) {
		return findProductTypeCodesService.start(productTypeCodes);
	}

	private final FindSystemParametersService findSystemParametersService;
	@Operation(summary = "查詢system parameter", description = "status 1代表成功-1代表失敗")
	@PostMapping("/systemParameters")
	public ResponseDto<List<SysParmDo>> findSystemParameters(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody List<SysParmMainRequestData> sysParmMainRequestDataList) {
		return findSystemParametersService.start(sysParmMainRequestDataList);
	}

	private final FindSubSystemParametersService findSubSystemParametersService;
	@Operation(summary = "查詢sub system parameter", description = "status 1代表成功-1代表失敗")
	@PostMapping("/subSystemParameters")
	public ResponseDto<List<SysParmDo>> findSubSystemParameters(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody List<SubSysParmMainRequestData> subSysParmMainRequestData) {
		return findSubSystemParametersService.start(subSysParmMainRequestData);
	}
}
