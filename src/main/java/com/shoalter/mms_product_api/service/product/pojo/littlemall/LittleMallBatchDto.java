package com.shoalter.mms_product_api.service.product.pojo.littlemall;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.config.security.xss.XssStringDeserializer;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LittleMallBatchDto {

	@JsonProperty("stores")
	private String stores;
	@JsonProperty("sku_id")
	@SerializedName("sku_id")
	private String skuId;
	@JsonProperty("product_id")
	@SerializedName("product_id")
	private String productId;
	@JsonProperty("is_primary_sku")
	@SerializedName("is_primary_sku")
	private Boolean isPrimarySku;
	@JsonProperty("sku_name")
	@SerializedName("sku_name")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuName;
	@JsonProperty("original_price")
	@SerializedName("original_price")
	private BigDecimal originalPrice;
	@JsonProperty("selling_price")
	@SerializedName("selling_price")
	private BigDecimal sellingPrice;
	private Boolean visibility;
	@JsonProperty("online_status")
	@SerializedName("online_status")
	private Boolean onlineStatus;
	@JsonProperty("display_in_hktvmall_category")
	@SerializedName("display_in_hktvmall_category")
	private List<String> displayInHktvmallCategory;
	@JsonProperty("sku_long_description_ch")
	@SerializedName("sku_long_description_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuLongDescriptionCh;
	@JsonProperty("main_photo")
	@SerializedName("main_photo")
	private String mainPhoto;
	@JsonProperty("other_photo")
	@SerializedName("other_photo")
	private List<String> otherPhoto;
	@JsonProperty("merchant_id")
	@SerializedName("merchant_id")
	private Integer merchantId;
	@JsonProperty("merchant_name")
	@SerializedName("merchant_name")
	private String merchantName;
	@JsonProperty("product_ready_method")
	@SerializedName("product_ready_method")
	private String productReadyMethod;
	@JsonProperty("product1_field")
	@SerializedName("product1_field")
	private Integer productField1;
	@JsonProperty("product1_field_category")
	@SerializedName("product1_field_category")
	private Integer productFieldCategory1;
	@JsonProperty("product1_field_option")
	@SerializedName("product1_field_option")
	private Integer productFieldOption1;
	@JsonProperty("product2_field")
	@SerializedName("product2_field")
	private Integer productField2;
	@JsonProperty("product2_field_category")
	@SerializedName("product2_field_category")
	private Integer productFieldCategory2;
	@JsonProperty("product2_field_option")
	@SerializedName("product2_field_option")
	private Integer productFieldOption2;
	@JsonProperty("product3_field")
	@SerializedName("product3_field")
	private Integer productField3;
	@JsonProperty("product3_field_category")
	@SerializedName("product3_field_category")
	private Integer productFieldCategory3;
	@JsonProperty("product3_field_option")
	@SerializedName("product3_field_option")
	private Integer productFieldOption3;
	@JsonProperty("product4_field")
	@SerializedName("product4_field")
	private Integer productField4;
	@JsonProperty("product4_field_category")
	@SerializedName("product4_field_category")
	private Integer productFieldCategory4;
	@JsonProperty("product4_field_option")
	@SerializedName("product4_field_option")
	private Integer productFieldOption4;

    public static SingleEditProductDto convertToSingleEditProductDtoWithRelation(LittleMallBatchDto littleMallBatchDto, Map<String, LittleMallRelationDto> relationProductIdMap) {
        ProductMasterDto product = ProductMasterDto.convertFromLittleMallBatchDto(littleMallBatchDto);
        SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
        singleEditProductDto.setProduct(product);
		singleEditProductDto.setRelation(relationProductIdMap.get(littleMallBatchDto.getProductId()));
        return singleEditProductDto;
    }

	public static SingleEditProductDto convertToSingleEditProductDto(LittleMallBatchDto littleMallBatchDto) {
		ProductMasterDto product = ProductMasterDto.convertFromLittleMallBatchDto(littleMallBatchDto);
		SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
		singleEditProductDto.setProduct(product);
		return singleEditProductDto;
	}
}
