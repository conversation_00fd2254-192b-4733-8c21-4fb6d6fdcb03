apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mms-product-ingress
  namespace: mms
  annotations:
    nginx.ingress.kubernetes.io/use-forwarded-headers: "true"
spec:
  ingressClassName: haproxy-ingress-class
  rules:
    - host: mms-product-staging.hkmpcl.com.hk
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: mms-product-api-svc
                port:
                  number: 8080

