package com.shoalter.mms_product_api.service.openApi.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.shoalter.mms_product_api.config.security.xss.XssStringDeserializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OapiSingleSaveMainRequestData {
	private String skuCode;
	private String productCode;
	private String productReadyMethod;
	private List<ProductTypeCodeRequestData> productCategories;
	private String primaryCategoryCode;
	private String brandCode;
	private String isPrimarySku;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuName;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuNameTchi;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuNameZhCN;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuSDescEn;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuSDescCh;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuSDescZhCN;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuLDescEn;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuLDescCh;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuLDescZhCN;
	private String manuCountry;
	private String affiliateUrl;
	private String currencyCode;
	private BigDecimal originalPrice;
	private BigDecimal sellingPrice;
	private String style;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountText;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountTextTchi;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountTextZhCN;
	private Long userMax;
	private String mainPhoto;
	private List<String> otherProductPhoto;
	private List<String> otherPhoto;
	private String advertisingPhoto;
	private String videoLink;
	private String videoLinkEn;
	private String videoLinkCh;
	private String videoLinkZhCN;
	private String videoLink2;
	private String videoLinkEn2;
	private String videoLinkCh2;
	private String videoLinkZhCN2;
	private String videoLink3;
	private String videoLinkEn3;
	private String videoLinkCh3;
	private String videoLinkZhCN3;
	private String videoLink4;
	private String videoLinkEn4;
	private String videoLinkCh4;
	private String videoLinkZhCN4;
	private String videoLink5;
	private String videoLinkEn5;
	private String videoLinkCh5;
	private String videoLinkZhCN5;
	private String colorFamilies;
	private String colorEn;
	private String sizeSystem;
	private String size;
	private String field1;
	private String value1;
	private String field2;
	private String value2;
	private String field3;
	private String value3;
	private String packBoxType;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String packSpecEn;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String packSpecCh;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String packSpecZhCN;
	private BigDecimal packHeight;
	private BigDecimal packLength;
	private BigDecimal packDepth;
	private String packDimensionUnit;
	private BigDecimal weight;
	private String weightUnit;
	private Integer cartonLength;
	private Integer cartonWidth;
	private Integer cartonHeight;
	private String invisibleFlag;
	private String barcode;
	private String featureStartTime;
	private String featureEndTime;
	private String voucherType;
	private String voucherDisplayType;
	private String voucherTemplateType;
	private String expiryType;
	private String redeemStartDate;
	private String fixedRedemptionDate;
	private Integer uponPurchaseDate;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String finePrintEn;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String finePrintCh;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String finePrintZhCN;
	private String removalServices;
	private String goodsType;
	private String warranty;
	private String warrantyPeriodUnit;
	private Integer warrantyPeriod;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantySupplierCh;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantySupplierEn;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantySupplierZhCN;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String serviceCentreAddressCh;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String serviceCentreAddressEn;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String serviceCentreAddressZhCN;
	private String serviceCentreEmail;
	private String serviceCentreContact;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantyRemarkCh;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantyRemarkEn;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantyRemarkZhCN;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String invoiceRemarksEn;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String invoiceRemarksCh;
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String invoiceRemarksZhCN;
	private Integer returnDays;
	private String productReadyDays;
	private String pickupDays;
	private String pickupTimeslot;
	private List<String> productOverseaDeliveryList;
	private Integer minimumShelfLife;
	private String onOfflineStatus;
	private OapiSingleSavePartnerInfoRequestData partnerInfo;
	private OapiSingleSaveExternalPlatformRequestData externalPlatform;
}
