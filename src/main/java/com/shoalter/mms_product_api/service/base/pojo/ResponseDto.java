package com.shoalter.mms_product_api.service.base.pojo;

import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
public class ResponseDto<T> {
    private int status;
    private T data;
    private List<String> errorMessageList;

	public static <T> ResponseDto<T> success(T data) {
		return ResponseDto.<T>builder()
			.status(StatusCodeEnum.SUCCESS.getCode())
			.data(data)
			.build();
	}
	public static <T> ResponseDto<T> fail(List<String> errorMessageList) {
		return ResponseDto.<T>builder()
			.status(StatusCodeEnum.FAIL.getCode())
			.errorMessageList(errorMessageList)
			.build();
	}
	public static <T> ResponseDto<T> generate(T data, List<String> errorMessageList) {
		return ResponseDto.<T>builder()
			.status(CollectionUtil.isEmpty(errorMessageList) ? StatusCodeEnum.SUCCESS.getCode() : StatusCodeEnum.FAIL.getCode())
			.data(data)
			.errorMessageList(errorMessageList)
			.build();
	}
	public static ResponseDto<String> generate(List<String> errorMessageList) {
		return ResponseDto.<String>builder()
			.status(CollectionUtil.isEmpty(errorMessageList) ? StatusCodeEnum.SUCCESS.getCode() : StatusCodeEnum.FAIL.getCode())
			.data(CollectionUtil.isEmpty(errorMessageList) ? "Success" : null)
			.errorMessageList(errorMessageList)
			.build();
	}
}
