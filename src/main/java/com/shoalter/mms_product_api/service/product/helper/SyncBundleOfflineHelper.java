package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.product.SystemUserEnum;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class SyncBundleOfflineHelper {

	private final Gson gson;

	private final ProductMasterHelper productMasterHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final UserHelper userHelper;

	@Transactional
	public void checkAndCreateOfflineBundleRecordAndRow(SaveProductRecordDo record, List<SaveProductRecordRowDo> saveProductRecordRowDos) {

		switch (record.getUploadType()) {
			case SaveProductType.SINGLE_CREATE_BUNDLE:
			case SaveProductType.SINGLE_EDIT_BUNDLE:
			case SaveProductType.SYNC_OFFLINE_BUNDLE:
			case SaveProductType.SINGLE_CREATE_PRODUCT:
			case SaveProductType.BATCH_CREATE_PRODUCT:
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL:
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL:
			case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT_COMMONLY_USED:
			case SaveProductType.SINGLE_CREATE_LITTLE_MALL_PRODUCT:
			case SaveProductType.SINGLE_EDIT_LITTLE_MALL_PRODUCT:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_SHOPLINE:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_HKTV:
			case SaveProductType.BATCH_CREATE_LITTLE_MALL_PRODUCT_FROM_EXCEL:
			case SaveProductType.BATCH_EDIT_LITTLE_MALL_PRODUCT:
				return;
		}

		try {
			List<SaveProductRecordRowDo> filterSuccessRows = saveProductRecordRowDos.stream().filter(row -> SaveProductStatus.SUCCESS == row.getStatus()).collect(Collectors.toList());
			if(CollectionUtil.isEmpty(filterSuccessRows)){
				return;
			}
			log.info("Run check SYNC_OFFLINE_BUNDLE record id: {}, row size: {}, save product type: {}", record.getId(), filterSuccessRows.size(), record.getUploadType());

			UserDto userDto = userHelper.generateUserDtoByRecord(record);
			// find need to offline bundle's child uuid by "isCheckOffLineBundle"
			List<String> childSkuUuids =
					filterSuccessRows.stream().map(row -> gson.fromJson(row.getContent(), SingleEditProductDto.class))
							.filter(singleEditProduct -> Objects.nonNull(singleEditProduct.getProduct().getAdditional().getHktv()) && singleEditProduct.getProduct().getAdditional().getHktv().isCheckOffLineBundle())
							.map(needCheckData -> needCheckData.getProduct().getUuid()).collect(Collectors.toList());

			if (CollectionUtil.isNotEmpty(childSkuUuids)) {
				log.info("check offline bundle child skus uuid : [{}]", childSkuUuids);
				// find bundles from PM by child uuids
				List<ProductMasterResultDto> productMasterResultDtos =
						productMasterHelper.requestPmForGetBundlesByChildUuids(userDto, childSkuUuids.toString(), childSkuUuids);
				// filter without offline bundle
				List<ProductMasterResultDto> productMasterResOnlineBundles =
						productMasterResultDtos.stream().filter(productMasterRes -> OnlineStatusEnum.ONLINE.name().equals(productMasterRes.getAdditional().getHktv().getOnlineStatus().name())).collect(Collectors.toList());

				if (CollectionUtil.isNotEmpty(productMasterResOnlineBundles)) {
					//create record
					ProductMasterDto firstOfEditProduct = gson.fromJson(filterSuccessRows.get(0).getContent(), SingleEditProductDto.class).getProduct();
					SaveProductRecordDo saveProductRecordDo =
							saveProductRecordHelper.createSaveProductRecord(UserDto.builder().userId(SystemUserEnum.MMS_PRODUCT_SYSTEM.getSystemId()).build(), firstOfEditProduct.getMerchantId(), SaveProductType.SYNC_OFFLINE_BUNDLE,
									String.format("Bundle_offline_%s_%d.xlsx", firstOfEditProduct.getAdditional().getHktv().getStores(), System.currentTimeMillis()), SaveProductStatus.PROCESSING);
					//create rows
					for (ProductMasterResultDto result : productMasterResOnlineBundles) {
						// set bundle offline
						result.getAdditional().getHktv().setOnlineStatus(OnlineStatusEnum.OFFLINE);
						result.getBundleSetting().getMallInventoryInfo().forEach(mallInventoryInfo -> mallInventoryInfo.setSettingQuantity(0));
						// convert data to singleEditProductDto and save in row content
						SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
						singleEditProductDto.setProduct(ProductMasterDto.convertFromProductMasterResultDto(result));
						singleEditProductDto.getProduct().setMmsModifiedUser(SystemUserEnum.MMS_PRODUCT_SYSTEM.getSystemName());
						singleEditProductDto.getProduct().setMmsModifiedTime(LocalDateTime.now());
						saveProductRecordRowHelper.createProductRecordRowDo(saveProductRecordDo.getId(), singleEditProductDto, SaveProductStatus.PROCESSING, null);
					}
					log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), productMasterResOnlineBundles.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));

				}
			}
		} catch (Exception e) {
			log.error("create bundle offline record fail : ", e);
		}
	}
}
