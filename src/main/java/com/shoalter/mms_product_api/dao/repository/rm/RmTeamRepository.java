package com.shoalter.mms_product_api.dao.repository.rm;

import com.shoalter.mms_product_api.dao.repository.rm.pojo.RmTeamDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface RmTeamRepository extends JpaRepository<RmTeamDo, Integer> {
    RmTeamDo findByUserId(Integer rmId);

    @Query(value =
            "SELECT * "+
            "FROM RM_TEAM "+
            "WHERE DEPT_CODE IN ( "+
            "SELECT DEPT_CODE FROM RM_TEAM "+
            "WHERE USER_ID = :userId ) ", nativeQuery = true)
    List<RmTeamDo> findRmlByUserIdAndDeptCode(Integer userId);
}
