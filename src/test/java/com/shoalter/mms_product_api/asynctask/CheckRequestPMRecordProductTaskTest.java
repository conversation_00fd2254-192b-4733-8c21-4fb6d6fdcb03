package com.shoalter.mms_product_api.asynctask;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.service.product.SaveProductRecordGroupHelper;
import com.shoalter.mms_product_api.service.product.helper.LittleMallVariantHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.helper.TaskExceptionHelper;
import com.shoalter.mms_product_api.service.product.helper.UserHelper;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.shopline.ProductBatchImportHelper;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class CheckRequestPMRecordProductTaskTest {

	@InjectMocks
	private CheckRequestPMRecordProductTask checkRequestPMRecordProductTask;
	@Mock
	private SaveProductRecordRepository saveProductRecordRepository;
	@Mock
	private SaveProductRecordRowRepository saveProductRecordRowRepository;
	@Mock
	private ProductMasterHelper productMasterHelper;
	@Mock
	private TaskExceptionHelper taskExceptionHelper;
	@Mock
	private SaveProductRecordGroupHelper saveProductRecordGroupHelper;
	@Mock
	private ProductBatchImportHelper productBatchImportHelper;
	@Mock
	private UserHelper userHelper;
	@Mock
	private SaveProductRecordRowHelper saveProductRecordRowHelper;
	@Mock
	private LittleMallVariantHelper littleMallVariantHelper;
	@Mock
	private MessageSource messageSource;

	private final Gson gson = new Gson();

	@BeforeEach
	void setUp() {
		checkRequestPMRecordProductTask = new CheckRequestPMRecordProductTask(
			saveProductRecordRepository,
			saveProductRecordRowRepository,
			productMasterHelper,
			taskExceptionHelper,
			saveProductRecordGroupHelper,
			productBatchImportHelper,
			userHelper,
			saveProductRecordRowHelper,
			littleMallVariantHelper,
			messageSource,
			gson
		);

	}

	private static final BigDecimal TEST_MALL_DOLLAR = new BigDecimal("3");
	private static final BigDecimal TEST_VIP_MALL_DOLLAR = new BigDecimal("1");
	private static final OnlineStatusEnum TEST_ONLINE_STATUS = OnlineStatusEnum.ONLINE;
	private static final String TEST_VISIBILITY = "Y";
	private static final BigDecimal TEST_ORIGINAL_PRICE = new BigDecimal("4");
	private static final BigDecimal TEST_SELLING_PRICE = new BigDecimal("5");
	private static final String TEST_STYLE = "TEST_STYLE";
	private static final String TEST_DISCOUNT_TEXT_EN = "TEST_DISCOUNT_TEXT_EN";
	private static final String TEST_DISCOUNT_TEXT_CH = "TEST_DISCOUNT_TEXT_CH";

	@Test
	void getEditProductMasterDtoList_singleEditProductFromPromotionContract_returnExpectedResult() {
		//When
		List<String> skuList = new ArrayList<>();
		List<ProductMasterDto> result = checkRequestPMRecordProductTask.getEditProductMasterDtoList(List.of(generateSaveProductRecordRowDo()),
			skuList, SaveProductType.SINGLE_EDIT_PRODUCT_FROM_PROMOTION_CONTRACT);

		//Then
		Assertions.assertThat(skuList.size()).isEqualTo(1);
		Assertions.assertThat(result.size()).isEqualTo(1);
		ProductMasterDto productMasterDto = result.get(0);
		Assertions.assertThat(productMasterDto.getAdditional().getHktv().getMallDollar()).isEqualTo(TEST_MALL_DOLLAR);
		Assertions.assertThat(productMasterDto.getAdditional().getHktv().getVipMallDollar()).isEqualTo(TEST_VIP_MALL_DOLLAR);
	}

	@Test
	void getEditProductMasterDtoList_batchEditProductOnlineStatus_returnExpectedResult() {
		//When
		List<String> skuList = new ArrayList<>();
		List<ProductMasterDto> result = checkRequestPMRecordProductTask.getEditProductMasterDtoList(List.of(generateSaveProductRecordRowDo()),
			skuList, SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS);

		//Then
		Assertions.assertThat(skuList.size()).isEqualTo(1);
		Assertions.assertThat(result.size()).isEqualTo(1);
		ProductMasterDto productMasterDto = result.get(0);
		Assertions.assertThat(productMasterDto.getAdditional().getHktv().getOnlineStatus()).isEqualTo(TEST_ONLINE_STATUS);
	}

	@Test
	void getEditProductMasterDtoList_batchEditProductPrice_returnExpectedResult() {
		//When
		List<String> skuList = new ArrayList<>();
		List<ProductMasterDto> result = checkRequestPMRecordProductTask.getEditProductMasterDtoList(List.of(generateSaveProductRecordRowDo()),
			skuList, SaveProductType.BATCH_EDIT_PRODUCT_PRICE);

		//Then
		Assertions.assertThat(skuList.size()).isEqualTo(1);
		Assertions.assertThat(result.size()).isEqualTo(1);
		ProductMasterDto productMasterDto = result.get(0);
		Assertions.assertThat(productMasterDto.getOriginalPrice()).isEqualTo(TEST_ORIGINAL_PRICE);
		Assertions.assertThat(productMasterDto.getAdditional().getHktv().getSellingPrice()).isEqualTo(TEST_SELLING_PRICE);
		Assertions.assertThat(productMasterDto.getAdditional().getHktv().getStyle()).isEqualTo(TEST_STYLE);
		Assertions.assertThat(productMasterDto.getAdditional().getHktv().getDiscountTextEn()).isEqualTo(TEST_DISCOUNT_TEXT_EN);
		Assertions.assertThat(productMasterDto.getAdditional().getHktv().getDiscountTextCh()).isEqualTo(TEST_DISCOUNT_TEXT_CH);
	}

	@Test
	void getEditProductMasterDtoList_batchEditProductVisibility_returnExpectedResult() {
		//When
		List<String> skuList = new ArrayList<>();
		List<ProductMasterDto> result = checkRequestPMRecordProductTask.getEditProductMasterDtoList(List.of(generateSaveProductRecordRowDo()),
			skuList, SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY);

		//Then
		Assertions.assertThat(skuList.size()).isEqualTo(1);
		Assertions.assertThat(result.size()).isEqualTo(1);
		ProductMasterDto productMasterDto = result.get(0);
		Assertions.assertThat(productMasterDto.getAdditional().getHktv().getVisibility()).isEqualTo(TEST_VISIBILITY);
	}

	private SaveProductRecordRowDo generateSaveProductRecordRowDo() {
		HktvProductDto hktvProductDto = new HktvProductDto();
		hktvProductDto.setMallDollar(TEST_MALL_DOLLAR);
		hktvProductDto.setVipMallDollar(TEST_VIP_MALL_DOLLAR);
		hktvProductDto.setOnlineStatus(TEST_ONLINE_STATUS);
		hktvProductDto.setVisibility(TEST_VISIBILITY);
		hktvProductDto.setSellingPrice(TEST_SELLING_PRICE);
		hktvProductDto.setStyle(TEST_STYLE);
		hktvProductDto.setDiscountTextEn(TEST_DISCOUNT_TEXT_EN);
		hktvProductDto.setDiscountTextCh(TEST_DISCOUNT_TEXT_CH);
		SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
		ProductMasterDto productMasterDto = new ProductMasterDto();
		productMasterDto.setOriginalPrice(TEST_ORIGINAL_PRICE);
		singleEditProductDto.setProduct(productMasterDto);
		singleEditProductDto.getProduct().setAdditional(new BuProductDto());
		singleEditProductDto.getProduct().getAdditional().setHktv(hktvProductDto);

		SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
		saveProductRecordRowDo.setContent(gson.toJson(singleEditProductDto));
		return saveProductRecordRowDo;
	}

}
