package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CheckSkuIdByHktvStoreProductMasterRequestDto {
    @JsonProperty("hktv_stores")
	@SerializedName("hktv_stores")
    private String hktvStores;
    @JsonProperty(value ="sku_id",required = true)
	@SerializedName("sku_id")
    private List<String> skuId;
}
