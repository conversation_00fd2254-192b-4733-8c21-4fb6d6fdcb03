package com.shoalter.mms_product_api.dao.repository.system.pojo;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;
@Entity
@Table(name = "SYS_ROLE")
@Data
public class SysRoleDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID")
    private Integer id;
    @Column(name = "ROLE_CODE")
    private String roleCode;
    @Column(name = "ROLE_NAME")
    private String roleName;
    @Column(name = "REMARK")
    private String remark;
    @Column(name = "DISP_SEQ")
    private Integer dispSeq;
    @Column(name = "ACTIVE_IND")
    private String activeInd;
    @Column(name = "INACTIVE_DATE")
    private Date inactiveDate;
    @Column(name = "ROLE_TYPE")
    private String roleType;
    @Column(name = "CREATED_BY")
    private String createdBy;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "LAST_UPDATED_BY")
    private String lastUpdatedBy;
    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;
}

