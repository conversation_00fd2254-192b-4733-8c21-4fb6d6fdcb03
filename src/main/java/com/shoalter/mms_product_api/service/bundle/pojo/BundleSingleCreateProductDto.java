package com.shoalter.mms_product_api.service.bundle.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.config.security.xss.XssStringDeserializer;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BundleSingleCreateProductDto {
	@Schema(hidden = true)
	@JsonProperty("record_row_id")
	@SerializedName("record_row_id")
	private Long recordRowId;

	@JsonProperty("merchant_id")
	@SerializedName("merchant_id")
	private Integer merchantId;

	@JsonProperty("product_id")
	@SerializedName("product_id")
	private String productId;

	@JsonProperty("brand_id")
	@SerializedName("brand_id")
	private Integer brandId;

	@JsonProperty("manufactured_country")
	@SerializedName("manufactured_country")
	private String manufacturedCountry;

	@JsonProperty("sku_id")
	@SerializedName("sku_id")
	private String skuId;

	@JsonProperty("sku_name_en")
	@SerializedName("sku_name_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuNameEn;

	@JsonProperty("sku_name_ch")
	@SerializedName("sku_name_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuNameCh;

	@JsonProperty("sku_name_sc")
	@SerializedName("sku_name_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuNameSc;

	@JsonProperty("original_price")
	@SerializedName("original_price")
	private BigDecimal originalPrice;

	@JsonProperty("packing_box_type")
	@SerializedName("packing_box_type")
	private String packingBoxType;

	@JsonProperty("bundle_setting")
	@SerializedName("bundle_setting")
	private BundleSettingRequestDto bundleSetting;

	private BuProductDto additional;
}
