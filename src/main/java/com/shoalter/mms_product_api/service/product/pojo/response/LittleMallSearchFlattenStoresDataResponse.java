package com.shoalter.mms_product_api.service.product.pojo.response;

import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewPageableResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewSortResultDto;
import lombok.Data;

import java.util.List;

@Data
public class LittleMallSearchFlattenStoresDataResponse {
	private List<LittleMallSearchFlattenStoreDetailResponse> content;
	private Integer totalPages;
	private Integer totalElements;
	private Boolean last;
	private Integer size;
	private Integer number;
	private Integer numberOfElements;
	private Boolean first;
	private Boolean empty;
	private ProductOverviewPageableResultDto pageable;
	private ProductOverviewSortResultDto sort;
}
