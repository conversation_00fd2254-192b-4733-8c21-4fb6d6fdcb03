package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class BatchEditLittleMallStoreCategoryRequestData {
	@JsonProperty("storefront_store_code")
	@SerializedName("storefront_store_code")
	private String storefrontStoreCode;
	@JsonProperty("product_id")
	@SerializedName("product_id")
	private String productId;
	@JsonProperty("store_category")
	@SerializedName("store_category")
	private List<String> storeCategory;
}
