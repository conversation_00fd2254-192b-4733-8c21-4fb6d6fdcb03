package com.shoalter.mms_product_api.config.product;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Slf4j
public enum ForceOfflineStatusEnum {
	// product master force_offline = true
	FORCE_OFFLINE("Suspended"),
	// product master force_offline = null
	OFFLINE("Offline");

	private final String status;

	ForceOfflineStatusEnum(String status) {
		this.status = status;
	}

	public static ForceOfflineStatusEnum getEnum(String status) {
		if (status == null) {
			log.warn("ForceOfflineStatusEnum status is null");
			return null;
		}
		for (ForceOfflineStatusEnum enumValue : ForceOfflineStatusEnum.values()) {
			if (enumValue.getStatus().equalsIgnoreCase(status)) {
				return enumValue;
			}
		}
		return null;
	}
}
