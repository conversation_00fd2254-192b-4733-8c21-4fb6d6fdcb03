package com.shoalter.mms_product_api.service.product.pojo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProductStatusDto {
    private String storefrontStoreCode;
    private String skuCode;
    private String onlineStatus;
    private Integer wareHouseId;
    private BigDecimal commissionRate;
    private BigDecimal tier1Threshold;
    private BigDecimal tier1CommissionRate;
    private BigDecimal tier2Threshold;
    private BigDecimal tier2CommissionRate;
    private String status;
    private Date lastSynchronizeDate;
}
