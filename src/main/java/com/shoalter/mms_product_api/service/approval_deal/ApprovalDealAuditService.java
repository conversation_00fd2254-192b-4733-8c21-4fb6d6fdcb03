package com.shoalter.mms_product_api.service.approval_deal;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.approval_deal.ApprovalDealRepository;
import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.ApprovalDealDo;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserNameAndEmailViewDo;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealStatusEnum;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealTypeEnum;
import com.shoalter.mms_product_api.service.approval_deal.helper.ApprovalDealHelper;
import com.shoalter.mms_product_api.service.approval_deal.helper.ApprovalDealStatusHelper;
import com.shoalter.mms_product_api.service.approval_deal.pojo.ApprovalDealSendMessageDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class ApprovalDealAuditService {

	private final ApprovalDealHelper approvalDealHelper;
	private final ApprovalDealStatusHelper approvalDealStatusHelper;
	private final ApprovalDealRepository approvalDealRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final Gson gson;

	public ResponseDto<Void> start(UserDto userDto, Long approvalId, ApprovalDealStatusEnum approvalDealAuditStatus) {
		ResponseDto<Void> response = null;
		if (!RoleCode.ALLOW_APPROVAL_DEAL_AUDIT_ROLE_SET.contains(userDto.getRoleCode())) {
			throw new SystemI18nException("message131");
		}

		boolean isRm = userDto.getRoleCode().equals(RoleCode.RM);
		ApprovalDealStatusEnum originalApprovalDealStatus = isRm ? ApprovalDealStatusEnum.MERCHANT_SUBMITTED : ApprovalDealStatusEnum.RM_REVIEWED;

		ApprovalDealDo approvalDealRecord = approvalDealRepository.findByIdAndApprovalTypeAndApprovalStatus(approvalId, ApprovalDealTypeEnum.COMMISSION_RATE, originalApprovalDealStatus)
			.orElseThrow(() -> new SystemI18nException("message146"));
		SaveProductRecordRowDo recordRow = saveProductRecordRowRepository.findById(approvalDealRecord.getRecordRowId()).orElseThrow(() -> new SystemI18nException("message146"));

		if (approvalDealAuditStatus.equals(ApprovalDealStatusEnum.APPROVED)) {
			if (isRm) {
				response = approvalDealHelper.processRmReviewedApprovalDeal(userDto, approvalDealRecord);
			} else {
				response = approvalDealHelper.processApproveApprovalDeal(userDto, approvalDealRecord, recordRow.getUuid());
				sendMessageForRejectOrRmlApprove(userDto, response, approvalDealRecord, recordRow.getUuid(), ApprovalDealStatusEnum.APPROVED);
			}
		} else if (approvalDealAuditStatus.equals(ApprovalDealStatusEnum.REJECTED)) {
			response = approvalDealHelper.processRejectApprovalDeal(userDto, approvalDealRecord);
			sendMessageForRejectOrRmlApprove(userDto, response, approvalDealRecord, recordRow.getUuid(), ApprovalDealStatusEnum.REJECTED);
		}

		return response;
	}

	private void sendMessageForRejectOrRmlApprove(UserDto userDto, ResponseDto<Void> response, ApprovalDealDo approvalDealRecord, String uuid, ApprovalDealStatusEnum approvalStatusChange) {
		if (response == null || response.getStatus() == StatusCodeEnum.FAIL.getCode() || !ApprovalDealStatusEnum.SEND_MESSAGE_STATUS_SET.contains(approvalStatusChange)) {
			log.error("sendMessageForRejectOrRmlApprove error, response:{}, uuid:{}, approvalStatusChange:{}", gson.toJson(response), uuid, approvalStatusChange);
			return;
		}
		List<UserNameAndEmailViewDo> receivers = approvalDealStatusHelper.findApprovalDealCreatorAndContractRms(
			approvalDealRecord.getMerchantId(),
			approvalDealRecord.getStorefrontStoreCode(),
			approvalDealRecord.getCreatedBy());
		ApprovalDealSendMessageDto sendMessage = ApprovalDealSendMessageDto.builder()
			.approvalDealStatusChange(approvalStatusChange)
			.receiverUsers(receivers)
			.uuid(uuid)
			.skuCode(approvalDealRecord.getSkuCode())
			.storefrontStoreCode(approvalDealRecord.getStorefrontStoreCode())
			.build();
		approvalDealStatusHelper.sendMessagesForApprovalStatusChange(userDto, List.of(sendMessage));
	}
}
