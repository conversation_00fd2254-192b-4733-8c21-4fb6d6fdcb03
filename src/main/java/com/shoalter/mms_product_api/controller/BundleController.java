package com.shoalter.mms_product_api.controller;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.bundle.FindBundleChildProductDetailService;
import com.shoalter.mms_product_api.service.bundle.FindBundleParentProductDetailService;
import com.shoalter.mms_product_api.service.bundle.FindBundleProductService;
import com.shoalter.mms_product_api.service.bundle.SingleEditBundleService;
import com.shoalter.mms_product_api.service.bundle.SingleSaveBundleService;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleChildDetailDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleParentDetailDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleProductInventoryOverviewResultDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleProductQueryDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleSingleCreateDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleSingleEditDto;
import com.shoalter.mms_product_api.service.bundle.pojo.request.BundleChildProductQueryDto;
import com.shoalter.mms_product_api.service.bundle.pojo.request.BundleParentProductQueryDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductRecordResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Bundle")
@RequiredArgsConstructor
@RestController
@PreAuthorize("hasRole('ROLE_MMS_ROLE')")
@Slf4j
@RequestMapping("/bundle")
public class BundleController {

	private final Gson gson;

	private final SingleSaveBundleService singleSaveBundleService;
	private final SingleEditBundleService singleEditBundleService;
	private final FindBundleChildProductDetailService findBundleChildProductDetailService;
	private final FindBundleParentProductDetailService findBundleParentProductDetailService;

	@Operation(summary = "create single bundle product", description = "status 1代表成功-1代表失敗")
	@PostMapping("/single/create")
	public ResponseDto<ProductRecordResponseDto> singleCreate(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody BundleSingleCreateDto productRequestDto) {
		String clientIp = ClientIpHolder.getClientIp();
		return singleSaveBundleService.start(userDto, productRequestDto, clientIp);
	}

	@Operation(summary = "edit single bundle product", description = "status 1代表成功-1代表失敗")
	@PutMapping("/single/edit")
	public ResponseDto<ProductRecordResponseDto> singleEdit(
			@AuthenticationPrincipal UserDto userDto,
			@RequestBody BundleSingleEditDto productRequestDto) {
		String clientIp = ClientIpHolder.getClientIp();
		return singleEditBundleService.start(userDto, productRequestDto, clientIp);
	}


	private final FindBundleProductService findBundleProductService;
	@Operation(summary = "query skus for bundle", description = "status 1代表成功-1代表失敗")
	@GetMapping("/products")
	public ResponseDto<BundleProductInventoryOverviewResultDto> products(
		@AuthenticationPrincipal UserDto userDto,
		@RequestParam(defaultValue = "1") Integer page,
		@RequestParam(defaultValue = "10") Integer size,
		@RequestParam(name = "is_bundle", required = false) Boolean isBundle,
		@RequestParam(name = "sku_code", required = false) String skuCode,
		@RequestParam(name = "sku_name", required = false) String skuName,
		@RequestParam(name = "merchant_id", required = false) List<Integer> merchantId,
		@RequestParam(name = "merchant_name", required = false) String merchantName,
		@RequestParam(name = "store_code", required = false) List<String> storeCode,
		@RequestParam(name = "product_ready_method", required = false) List<String> productReadyMethod,
		@RequestParam(defaultValue = "0", required = false) Integer priority,
		@RequestParam(name = "storage_type", required = false) List<String> storageType,
		@RequestParam(name = "bu_code", defaultValue = "HKTV", required = false) String buCode,
		@RequestParam(name = "order_by", required = false) List<String> orderBy
		) {
		BundleProductQueryDto bundleProductQueryDto = BundleProductQueryDto.builder()
			.page(page)
			.size(size)
			.isBundle(isBundle)
			.skuCode(skuCode)
			.skuName(skuName)
			.merchantId(merchantId)
			.merchantName(merchantName)
			.storeId(storeCode)
			.productReadyMethod(productReadyMethod)
			.priority(priority)
			.storageType(storageType)
			.orderBy(orderBy)
			.build();
		return findBundleProductService.start(userDto, bundleProductQueryDto, buCode);
	}

	@Operation(summary = "query parent sku detail by parent uuid", description = "status 1代表成功-1代表失敗")
	@GetMapping("/{uuid}")
	public ResponseDto<BundleParentDetailDto> findParentDetailByParentUUID(
			@AuthenticationPrincipal UserDto userDto,
			@PathVariable("uuid") String uuid,
			@RequestParam(name = "bu_code", defaultValue = "HKTV", required = false) String buCode) {
		BundleParentProductQueryDto bundleParentProductQueryDto =
				BundleParentProductQueryDto.builder().userDto(userDto).uuid(uuid).build();

		return findBundleParentProductDetailService.start(bundleParentProductQueryDto, buCode);

	}

	@Operation(summary = "query child sku detail by parent uuid", description = "status 1代表成功-1代表失敗")
	@GetMapping("/{uuid}/childSkus")
	public ResponseDto<BundleChildDetailDto> findChildDetailByParentUUID(
			@AuthenticationPrincipal UserDto userDto,
			@PathVariable("uuid") String uuid,
			@RequestParam(name = "page", defaultValue = "1", required = false) Integer page,
			@RequestParam(name = "size", defaultValue = "10", required = false) Integer size,
			@RequestParam(name = "order_by", required = false) List<String> orderBy,
			@RequestParam(name = "sku_code", required = false) String skuCode,
			@RequestParam(name = "sku_name", required = false) String skuName) {

		BundleChildProductQueryDto bundleChildProductQueryDto =
				BundleChildProductQueryDto.builder()
						.userDto(userDto).uuid(uuid)
						.page(page).size(size).orderBy(orderBy)
						.skuCode(skuCode).skuName(skuName)
						.build();

		return findBundleChildProductDetailService.start(bundleChildProductQueryDto);

	}

}



