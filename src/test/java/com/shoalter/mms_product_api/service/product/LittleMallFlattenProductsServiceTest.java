package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.StoreMerchantViewDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallFlattenProductsRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterBaseResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchLittleMallFalttenSkuRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.response.LittleMallFlattenSkuDetailResponse;
import com.shoalter.mms_product_api.service.product.pojo.response.LittleMallFlattenSkuResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LittleMallFlattenProductsServiceTest {

	@Mock
	private SaveProductRecordHelper saveProductRecordHelper;

	@Mock
	private SaveProductRecordRowHelper saveProductRecordRowHelper;

	@Mock
	private StoreRepository storeRepository;

	@Mock
	private ProductMasterHelper productMasterHelper;

	@Mock
	private MessageSource messageSource;

	@InjectMocks
	private LittleMallFlattenProductsService littleMallFlattenProductsService;

	private LittleMallFlattenProductsRequestDto requestDto;

	private static final String STORE_CODE = "STORE001";
	private static final String DEFAULT_MESSAGE = "Default message";
	private static final int USER_ID = 1;
	private static final int MERCHANT_ID = 1;
	private static final long RECORD_ID = 1L;
	private static final UserDto TEST_USER = UserDto.builder().userId(USER_ID).userName("test").build();

	@BeforeEach
	void setUp() {
		requestDto = new LittleMallFlattenProductsRequestDto();
		requestDto.setStorefrontStoreCodes(List.of(STORE_CODE));

		lenient().when(messageSource.getMessage(anyString(), any(), any()))
			.thenReturn(DEFAULT_MESSAGE);
	}

	@Test
	void start_emptyStoreCodes_throwsNoDataException() {
		//Given
		requestDto.setStorefrontStoreCodes(Collections.emptyList());

		//Then
		assertThrows(NoDataException.class, () ->
			littleMallFlattenProductsService.start(TEST_USER, requestDto));
	}

	@Test
	void start_nonExistentStore_returnsFail() {
		//Given
		when(storeRepository.findStoreMerchantViewDoByBuCodeAndStorefrontStoreCodes(
			eq(BuCodeEnum.LITTLE_MALL.name()), anyList()))
			.thenReturn(Collections.emptyList());

		//When
		ResponseDto<Void> response = littleMallFlattenProductsService.start(TEST_USER, requestDto);

		//Then
		assertEquals(StatusCodeEnum.FAIL.getCode(), response.getStatus());
		assertNotNull(response.getErrorMessageList());
		assertEquals(1, response.getErrorMessageList().size());
	}

	@Test
	void start_successfulProcess_returnsSuccess() {
		//Given
		when(storeRepository.findStoreMerchantViewDoByBuCodeAndStorefrontStoreCodes(eq(BuCodeEnum.LITTLE_MALL.name()), anyList()))
			.thenReturn(List.of(generateStoreMerchantViewDo()));

		LittleMallFlattenSkuDetailResponse littleMallFlattenSkuDetailResponse = new LittleMallFlattenSkuDetailResponse();
		LittleMallFlattenSkuResponse skuResponse = new LittleMallFlattenSkuResponse();
		skuResponse.setStorefrontStoreCode(STORE_CODE);
		skuResponse.setProducts(List.of(littleMallFlattenSkuDetailResponse));
		ProductMasterBaseResponseDto<List<LittleMallFlattenSkuResponse>> productMasterResponse = new ProductMasterBaseResponseDto<>();
		productMasterResponse.setData(List.of(skuResponse));
		when(productMasterHelper.requestSearchLittleMallFlattenSkusByStores(any(UserDto.class), any(ProductSearchLittleMallFalttenSkuRequestDto.class)))
			.thenReturn(productMasterResponse);

		SaveProductRecordDo saveProductRecordDo = new SaveProductRecordDo();
		saveProductRecordDo.setId(RECORD_ID);
		saveProductRecordDo.setUploadType(SaveProductType.LITTLE_MALL_FLATTEN_PRODUCTS);
		saveProductRecordDo.setStatus(SaveProductStatus.REQUESTING_PM);
		when(saveProductRecordHelper.createSaveProductRecord(eq(USER_ID), eq(MERCHANT_ID), eq(SaveProductType.LITTLE_MALL_FLATTEN_PRODUCTS), anyString(), eq(SaveProductStatus.REQUESTING_PM)))
			.thenReturn(saveProductRecordDo);

		when(saveProductRecordRowHelper.generateProductRecordRowDo(eq(RECORD_ID), any(SingleEditProductDto.class), eq(SaveProductStatus.REQUESTING_PM), isNull()))
			.thenReturn(new SaveProductRecordRowDo());

		//When
		ResponseDto<Void> response = littleMallFlattenProductsService.start(TEST_USER, requestDto);

		//Then
		assertEquals(StatusCodeEnum.SUCCESS.getCode(), response.getStatus());
		verify(saveProductRecordRowHelper, times(1)).batchSaveSaveProductRecordRowDo(anyList());
	}

	@Test
	void start_productMasterNull_returnsFail() {
		//Given
		when(storeRepository.findStoreMerchantViewDoByBuCodeAndStorefrontStoreCodes(
			eq(BuCodeEnum.LITTLE_MALL.name()), anyList()))
			.thenReturn(List.of(generateStoreMerchantViewDo()));
		when(productMasterHelper.requestSearchLittleMallFlattenSkusByStores(
			any(UserDto.class), any(ProductSearchLittleMallFalttenSkuRequestDto.class)))
			.thenReturn(null);

		//When
		ResponseDto<Void> response = littleMallFlattenProductsService.start(TEST_USER, requestDto);

		//Then
		assertEquals(StatusCodeEnum.FAIL.getCode(), response.getStatus());
		assertNotNull(response.getErrorMessageList());
		assertEquals(1, response.getErrorMessageList().size());
	}

	@Test
	void start_emptyProductMasterData_returnsFail() {
		//Given
		when(storeRepository.findStoreMerchantViewDoByBuCodeAndStorefrontStoreCodes(eq(BuCodeEnum.LITTLE_MALL.name()), anyList()))
			.thenReturn(List.of(generateStoreMerchantViewDo()));

		ProductMasterBaseResponseDto<List<LittleMallFlattenSkuResponse>> productMasterResponse = new ProductMasterBaseResponseDto<>();
		productMasterResponse.setData(Collections.emptyList());
		when(productMasterHelper.requestSearchLittleMallFlattenSkusByStores(any(UserDto.class), any(ProductSearchLittleMallFalttenSkuRequestDto.class)))
			.thenReturn(productMasterResponse);

		//When
		ResponseDto<Void> response = littleMallFlattenProductsService.start(TEST_USER, requestDto);

		//Then
		assertEquals(StatusCodeEnum.FAIL.getCode(), response.getStatus());
		assertNotNull(response.getErrorMessageList());
		assertEquals(1, response.getErrorMessageList().size());
	}

	private StoreMerchantViewDo generateStoreMerchantViewDo() {
		return new StoreMerchantViewDo() {
			@Override
			public Integer getMerchantId() {
				return MERCHANT_ID;
			}

			@Override
			public String getStorefrontStoreCode() {
				return STORE_CODE;
			}

			@Override
			public String getStoreCode() {
				return STORE_CODE;
			}
		};
	}
}
