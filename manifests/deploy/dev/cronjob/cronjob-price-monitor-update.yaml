apiVersion: batch/v1
kind: CronJob
metadata:
  name: mms-product-cronjob-price-monitor-update
  namespace: mms
spec:
  suspend: true
  schedule: "TZ=Asia/Hong_Kong 0,30 * * * *"  # (DEV only) Every 30 minutes for testing
  startingDeadlineSeconds: 600
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      activeDeadlineSeconds: 900  # 15 minutes timeout
      backoffLimit: 0
      parallelism: 1
      completions: 1
      template:
        spec:
          containers:
            - name: mms-product-cronjob
              image: alpine/curl:3.14
              imagePullPolicy: IfNotPresent
              volumeMounts:
                - name: command-config-volume
                  mountPath: /etc/config/command-config
              env:
                - name: JOB_NAME
                  value: "mms-product-cronjob-price-monitor-update"
                - name: JOB_URL
                  value: "http://mms-product-api-svc.mms.svc.cluster.local:8080/product/api/s2s/cronjob/price-monitor/update-price"
              command:
                - /bin/sh
                - -c
                - |
                  source /etc/config/command-config/odd-week-job-command.sh
          volumes:
            - name: command-config-volume
              configMap:
                name: mms-product-configmap
          restartPolicy: Never
